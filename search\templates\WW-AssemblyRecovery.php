<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'AssemblyRecovery.'.$today.'.csv';
$i = 0;
$csv = "entity_id,recovery_type_scan_time,recovered_serial_id,serial_scan_time,mpn_id,mpn_scan_time,part_type,part_type_scan_time,manufacturer_id,source_type,assembly_id,assembly_id_scan_time,assembly_mpn_id,assembly_part_type,assembly_bin_id,assembly_next_step_action,assembly_next_step_rule_id,operator_login_id,recovery_location_id,recovery_result,result_scan_time,recovery_datetime,bin_id,bin_scan_time,container_id,container_scan_time,next_step_action,next_step_rule_id,workstation_id,workstation_scan_time,storage_location_group_id,coo_id,assembly_classification_type,assembly_classification_code_id,classification_type,classification_code_id,controller_login_id,tpvr_reason,tpvr_request_scan_time,controller_scan_time,recovery_verification_id,coo_scan_time,batch_event_flag,event_id,customer_id,event_s_duration_value\n";//Column headers
$sql = "Select distinct(A.SerialNumber) as recovered_serial_id,A.recovery_type_scan_time as recovery_type_scan_time,A.serial_scan_time as serial_scan_time,
        A.UniversalModelNumber as mpn_id, A.mpn_scan_time as mpn_scan_time,APT.parttype as part_type,A.part_type_scan_time as part_type_scan_time,
        AM.ManufacturerName as manufacturer_id,ST.Cumstomertype as source_type,A.TopLevelSerial as assembly_id,A.origin_container_id_scan_time as assembly_id_scan_time,
        SSR.MPN as assembly_mpn_id,SSR.Type as assembly_part_type,SSR.origin_bin_id as assembly_bin_id,SSRD.disposition as assembly_next_step_action,
        NULL as assembly_next_step_rule_id,AU.UserName as operator_login_id,AF.FacilityName as recovery_location_id,
        ASW.input as recovery_result,A.result_scan_time as result_scan_time,A.DateCreated,ACP.BinName as bin_id,A.bin_scan_time as bin_scan_time,
        A.ShippingContainerID as container_id,A.origin_container_id_scan_time as container_scan_time,AD.disposition as next_step_action,
        ABR.rule_name as next_step_rule_id,ASS.SiteName as workstation_id,A.workstation_scan_time as workstation_scan_time,
        ACPLG.GroupName as storage_location_group_id,ACOO.COO as coo_id,SSRD.WasteClassificationType as assembly_classification_type,
        SSR.WasteCode as assembly_classification_code_id,AD.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,P.AuditControllerLoginID as controller_login_id,
        NULL as tpvr_reason,NULL as tpvr_request_scan_time,A.origin_container_id_scan_time as controller_scan_time,A.MediaRecovery_VerificationID as recovery_verification_id,
        A.coo_scan_time as coo_scan_time,A.batch_event_flag,A.event_id,AWSSC.Customer as customer_id,A.DateUpdated,ASCLG.GroupName
        FROM asset as A
        LEFT JOIN parttype APT ON APT.parttypeid = A.parttypeid
        LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
        LEFT JOIN speed_server_recovery SSR on SSR.ServerSerialNumber = A.TopLevelSerial
        LEFT JOIN pallets SSRP on SSRP.idPallet = SSR.idPallet
        LEFT JOIN disposition SSRD ON SSRD.`disposition_id` = SSR.`origin_disposition_id`
        LEFT JOIN users AU on AU.UserId = A.CreatedBy
        LEFT JOIN facility AF on AF.FacilityID = A.FacilityID
        LEFT JOIN workflow_input SSRW ON SSRW.input_id = SSR.input_id
        LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
        LEFT JOIN business_rule ABR on ABR.rule_id = A.rule_id
        LEFT JOIN workflow_input ASW ON ASW.input_id = A.input_id
        LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN shipping_containers ASCS ON ASCS.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCL ON ASCL.LocationID = ASCS.LocationID
        LEFT JOIN location_group ASCLG ON ASCLG.GroupID = ASCL.GroupID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.DateCreated Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        and A.Recoverytypeid IN (Select Recoverytypeid from Recoverytype where Recoverytype = 'Assembly')
        AND A.StatusID != 10
        order by A.event_id,A.serial_scan_time ASC";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    if($row['event_id'] != $eventid)
    {
        $i = 0;
    }
    /*if($i ==0)
    {
        $eventid = $row['event_id'];
        $timeFirst  = strtotime($row['recovery_type_scan_time']);
        $timeSecond = strtotime($row['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    else
    {
        $eventid = $row['event_id'];
        $timeFirst  = $timeSecond;
        $timeSecond = strtotime($row['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    $i = $i+1;*/
    $timeFirst  = strtotime($row['serial_scan_time']);
    $timeSecond = strtotime($row['DateCreated']);
    $differenceInSeconds = $timeSecond - $timeFirst;
    if($row['DateUpdated'] == '')
    {
        $row['DateUpdated'] = $row['DateCreated'];
    }
    if($row['tpvr_reason'] != '')
    {
        $row['controller_scan_time'] = '';
    }
    if($row['ShippingContainerID'] != '')
    {
        $row['bin_id'] = 'n/a';
        $row['storage_location_group_id'] = $row['GroupName'];
    }
    $start = strtotime($row['DateCreated']);
	$end = strtotime($row['DateUpdated']);
	$mins = ($end - $start) / 60;
	$hrs = $mins/60;
	$hrs1 = number_format($hrs,2);
    $date1 = explode(" ",$row['DateCreated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("H:i:s", strtotime($row['DateCreated']));
    if($row['DateCreated'] != '')
    {
        $row['DateCreated'] = date("Y-m-d H:i:s", strtotime($row['DateCreated']));
    }
    else
    {
        $row['DateCreated'] = '';
    }
    $row['operator_login_id'] = strtolower($row['operator_login_id']);
    $row['controller_login_id'] = strtolower($row['controller_login_id']);
    $row['entity_id'] = str_replace(","," ",$row['entity_id']);
	$row['recovered_serial_id'] = str_replace(","," ",$row['recovered_serial_id']);
	$row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
	$row['part_type'] = str_replace(","," ",$row['part_type']);
    $row['manufacturer_id'] = str_replace(","," ",$row['manufacturer_id']);
    $row['source_type'] = str_replace(","," ",$row['source_type']);
    $row['assembly_id'] = str_replace(","," ",$row['assembly_id']);
    $row['assembly_mpn_id'] = str_replace(","," ",$row['assembly_mpn_id']);
    $row['assembly_part_type'] = str_replace(","," ",$row['assembly_part_type']);
    $row['assembly_bin_id'] = str_replace(","," ",$row['assembly_bin_id']);
    $row['assembly_next_step_action'] = str_replace(","," ",$row['assembly_next_step_action']);
    $row['assembly_next_step_rule_id'] = str_replace(","," ",$row['assembly_next_step_rule_id']);
    $row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
    $row['recovery_location_id'] = str_replace(","," ",$row['recovery_location_id']);
    $row['recovery_result'] = str_replace(","," ",$row['recovery_result']);
    $row['bin_id'] = str_replace(","," ",$row['bin_id']);
    $row['container_id'] = str_replace(","," ",$row['container_id']);
    $row['next_step_action'] = str_replace(","," ",$row['next_step_action']);
    $row['next_step_rule_id'] = str_replace(","," ",$row['next_step_rule_id']);
    $row['workstation_id'] = str_replace(","," ",$row['workstation_id']);
    $row['storage_location_group_id'] = str_replace(","," ",$row['storage_location_group_id']);
    $row['coo_id'] = str_replace(","," ",$row['coo_id']);
    $row['assembly_classification_type'] = str_replace(","," ",$row['assembly_classification_type']);
    $row['assembly_classification_code_id'] = str_replace(","," ",$row['assembly_classification_code_id']);
    $row['classification_type'] = str_replace(","," ",$row['classification_type']);
    $row['classification_code_id'] = str_replace(","," ",$row['classification_code_id']);
    $row['controller_login_id'] = str_replace(","," ",$row['controller_login_id']);
    $row['tpvr_reason'] = str_replace(","," ",$row['tpvr_reason']);
    $row['recovery_verification_id'] = str_replace(","," ",$row['recovery_verification_id']);
    $row['batch_event_flag'] = str_replace(","," ",$row['batch_event_flag']);
    $row['event_id'] = str_replace(","," ",$row['event_id']);
    $row['customer_id'] = str_replace(","," ",$row['customer_id']);
    
    if($row['entity_id'] == '')
    {
        $row['entity_id'] = 'n/a';
    }
    if($row['recovery_type_scan_time'] == '')
    {
        $row['recovery_type_scan_time'] = '';
    }
    if($row['recovered_serial_id'] == '')
    {
        $row['recovered_serial_id'] = 'n/a';
    }
    if($row['serial_scan_time'] == '')
    {
        $row['serial_scan_time'] = '';
    }
    if($row['mpn_id'] == '')
    {
        $row['mpn_id'] = 'n/a';
    }
    if($row['mpn_scan_time'] == '')
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['part_type'] == '')
    {
        $row['part_type'] = 'n/a';
    }
    if($row['part_type_scan_time'] == '')
    {
        $row['part_type_scan_time'] = '';
    }
    if($row['manufacturer_id'] == '')
    {
        $row['manufacturer_id'] = 'n/a';
    }
    if($row['source_type'] == '')
    {
        $row['source_type'] = 'n/a';
    }
    if($row['assembly_id'] == '')
    {
        $row['assembly_id'] = 'n/a';
    }
    if($row['assembly_id_scan_time'] == '')
    {
        $row['assembly_id_scan_time'] = '';
    }
    if($row['assembly_mpn_id'] == '')
    {
        $row['assembly_mpn_id'] = 'n/a';
    }
    if($row['assembly_part_type'] == '')
    {
        $row['assembly_part_type'] = 'n/a';
    }
    if($row['assembly_bin_id'] == '')
    {
        $row['assembly_bin_id'] = 'n/a';
    }
    if($row['assembly_next_step_action'] == '')
    {
        $row['assembly_next_step_action'] = 'n/a';
    }
    if($row['assembly_next_step_rule_id'] == '')
    {
        $row['assembly_next_step_rule_id'] = 'n/a';
    }
    if($row['operator_login_id'] == '')
    {
        $row['operator_login_id'] = 'n/a';
    }
    if($row['recovery_location_id'] == '')
    {
        $row['recovery_location_id'] = 'n/a';
    }
    if($row['recovery_result'] == '')
    {
        $row['recovery_result'] = 'n/a';
    }
    if($row['result_scan_time'] == '')
    {
        $row['result_scan_time'] = '';
    }
    if($row['bin_id'] == '')
    {
        $row['bin_id'] = 'n/a';
    }
    if($row['bin_scan_time'] == '')
    {
        $row['bin_scan_time'] = '';
    }
    if($row['container_id'] == '')
    {
        $row['container_id'] = 'n/a';
    }
    if($row['container_scan_time'] == '')
    {
        $row['container_scan_time'] = '';
    }
    if($row['next_step_action'] == '')
    {
        $row['next_step_action'] = 'n/a';
    }
    if($row['next_step_rule_id'] == '')
    {
        $row['next_step_rule_id'] = 'n/a';
    }
    if($row['workstation_id'] == '')
    {
        $row['workstation_id'] = 'n/a';
    }
    if($row['workstation_scan_time'] == '')
    {
        $row['workstation_scan_time'] = '';
    }
    if($row['storage_location_group_id'] == '')
    {
        $row['storage_location_group_id'] = 'n/a';
    }
    if($row['coo_id'] == '')
    {
        $row['coo_id'] = 'n/a';
    }
    if($row['assembly_classification_type'] == '')
    {
        $row['assembly_classification_type'] = 'n/a';
    }
    if($row['assembly_classification_code_id'] == '')
    {
        $row['assembly_classification_code_id'] = 'n/a';
    }
    if($row['classification_type'] == '')
    {
        $row['classification_type'] = 'n/a';
    }
    if($row['classification_code_id'] == '')
    {
        $row['classification_code_id'] = 'n/a';
    }
    if($row['controller_login_id'] == '')
    {
        $row['controller_login_id'] = 'n/a';
    }
    if($row['tpvr_reason'] == '')
    {
        $row['tpvr_reason'] = 'n/a';
    }
    if($row['tpvr_request_scan_time'] == '')
    {
        $row['tpvr_request_scan_time'] = '';
    }
    if($row['controller_scan_time'] == '')
    {
        $row['controller_scan_time'] = '';
    }
    if($row['recovery_verification_id'] == '')
    {
        $row['recovery_verification_id'] = 'n/a';
    }
    if($row['coo_scan_time'] == '')
    {
        $row['coo_scan_time'] = '';
    }
    if($row['batch_event_flag'] == '')
    {
        $row['batch_event_flag'] = 'N';
    }
    if($row['event_id'] == '')
    {
        $row['event_id'] = 'n/a';
    }
    if($row['customer_id'] == '')
    {
        $row['customer_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    if($row['recovery_type_scan_time'] != '')
    {
        if($row['recovery_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['recovery_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row['recovery_type_scan_time']));
        }
        else
        {
            $row['recovery_type_scan_time'] = '';
        }
    }
    else
    {
        $row['recovery_type_scan_time'] = '';
    }
    if($row['serial_scan_time'] != '')
    {
        if($row['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row['serial_scan_time']));
        }
        else
        {
            $row['serial_scan_time'] = '';
        }
    }
    else
    {
        $row['serial_scan_time'] = '';
    }
    if($row['mpn_scan_time'] != '')
    {
        if($row['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row['mpn_scan_time']));
        }
        else
        {
            $row['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['part_type_scan_time'] != '')
    {
        if($row['part_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['part_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row['part_type_scan_time']));
        }
        else
        {
            $row['part_type_scan_time'] = '';
        }
    }
    else
    {
        $row['part_type_scan_time'] = '';
    }
    if($row['assembly_id_scan_time'] != '')
    {
        if($row['assembly_id_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['assembly_id_scan_time'] = date("Y-m-d H:i:s", strtotime($row['assembly_id_scan_time']));
        }
        else
        {
            $row['assembly_id_scan_time'] = '';
        }
    }
    else
    {
        $row['assembly_id_scan_time'] = '';
    }
    if($row['result_scan_time'] != '')
    {
        if($row['result_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['result_scan_time'] = date("Y-m-d H:i:s", strtotime($row['result_scan_time']));
        }
        else
        {
            $row['result_scan_time'] = '';
        }
    }
    else
    {
        $row['result_scan_time'] = '';
    }
    if($row['bin_scan_time'] != '')
    {
        if($row['bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row['bin_scan_time']));
        }
        else
        {
            $row['bin_scan_time'] = '';
        }
    }
    else
    {
        $row['bin_scan_time'] = '';
    }
    if($row['container_scan_time'] != '')
    {
        if($row['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row['container_scan_time']));
        }
        else
        {
            $row['container_scan_time'] = '';
        }
    }
    else
    {
        $row['container_scan_time'] = '';
    }
    if($row['workstation_scan_time'] != '')
    {
        if($row['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row['workstation_scan_time']));
        }
        else
        {
            $row['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row['workstation_scan_time'] = '';
    }
    if($row['tpvr_request_scan_time'] != '')
    {
        if($row['tpvr_request_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['tpvr_request_scan_time'] = date("Y-m-d H:i:s", strtotime($row['tpvr_request_scan_time']));
        }
        else
        {
            $row['tpvr_request_scan_time'] = '';
        }
    }
    else
    {
        $row['tpvr_request_scan_time'] = '';
    }
    if($row['controller_scan_time'] != '')
    {
        if($row['controller_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['controller_scan_time'] = date("Y-m-d H:i:s", strtotime($row['controller_scan_time']));
        }
        else
        {
            $row['controller_scan_time'] = '';
        }
    }
    else
    {
        $row['controller_scan_time'] = '';
    }
    if($row['coo_scan_time'] != '')
    {
        if($row['coo_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['coo_scan_time'] = date("Y-m-d H:i:s", strtotime($row['coo_scan_time']));
        }
        else
        {
            $row['coo_scan_time'] = '';
        }
    }
    else
    {
        $row['coo_scan_time'] = '';
    }
    $row2  = array('eV-Disposition-1',$row['recovery_type_scan_time'],$row['recovered_serial_id'],$row['serial_scan_time'],$row['mpn_id'],$row['mpn_scan_time'],$row['part_type'],$row['part_type_scan_time'],$row['manufacturer_id'],$row['source_type'],$row['assembly_id'],$row['assembly_id_scan_time'],$row['assembly_mpn_id'],$row['assembly_part_type'],$row['assembly_bin_id'],$row['assembly_next_step_action'],$row['assembly_next_step_rule_id'],$row['operator_login_id'],$row['recovery_location_id'],$row['recovery_result'],$row['result_scan_time'],$row['DateCreated'],$row['bin_id'],$row['bin_scan_time'],$row['container_id'],$row['container_scan_time'],$row['next_step_action'],$row['next_step_rule_id'],$row['workstation_id'],$row['workstation_scan_time'],$row['storage_location_group_id'],$row['coo_id'],$row['assembly_classification_type'],$row['assembly_classification_code_id'],$row['classification_type'],$row['classification_code_id'],$row['controller_login_id'],$row['tpvr_reason'],$row['tpvr_request_scan_time'],$row['controller_scan_time'],$row['recovery_verification_id'],$row['coo_scan_time'],$row['batch_event_flag'],$row['event_id'],$row['customer_id'],$differenceInSeconds);
    $rows[] = $row2;
    $i = $i+1;
}
$j = 0;
$sql1 = "Select distinct(A.MediaSerialNumber) as recovered_serial_id,A.recovery_type_scan_time as recovery_type_scan_time,A.serial_scan_time as serial_scan_time,
        A.MediaMPN as mpn_id, A.mpn_scan_time as mpn_scan_time,APT.parttype as part_type,A.part_type_scan_time as part_type_scan_time,
        AM.ManufacturerName as manufacturer_id,ST.Cumstomertype as source_type,A.ServerSerialNumber as assembly_id,A.origin_container_id_scan_time as assembly_id_scan_time,
        SSR.MPN as assembly_mpn_id,SSR.Type as assembly_part_type,SSR.origin_bin_id as assembly_bin_id,SSRD.disposition as assembly_next_step_action,
        NULL as assembly_next_step_rule_id,AU.UserName as operator_login_id,AF.FacilityName as recovery_location_id,
        ASW.input as recovery_result,A.result_scan_time as result_scan_time,A.CreatedDate,ACP.BinName as bin_id,A.bin_scan_time as bin_scan_time,
        A.ShippingContainerID as container_id,A.origin_container_id_scan_time as container_scan_time,AD.disposition as next_step_action,
        NULL as next_step_rule_id,ASS.SiteName as workstation_id,A.workstation_scan_time as workstation_scan_time,
        ACPLG.GroupName as storage_location_group_id,ACOO.COO as coo_id,SSRD.WasteClassificationType as assembly_classification_type,
        SSR.WasteCode as assembly_classification_code_id,AD.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,P.AuditControllerLoginID as controller_login_id,
        A.TPVRReason as tpvr_reason,A.TPVRControllerScanTime as tpvr_request_scan_time,A.origin_container_id_scan_time as controller_scan_time,A.VerificationID as recovery_verification_id,
        A.coo_scan_time as coo_scan_time,A.batch_event_flag,A.event_id,AWSSC.Customer as customer_id,A.UpdatedDate,ASCLG.GroupName
        FROM speed_media_recovery as A
        LEFT JOIN parttype APT ON APT.parttypeid = A.parttypeid
        LEFT JOIN catlog_creation ACC ON ACC.mpn_id = A.MediaMPN
        LEFT JOIN manufacturer AM on AM.idManufacturer = ACC.idManufacturer
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
        LEFT JOIN speed_server_recovery SSR on SSR.ServerSerialNumber = A.ServerSerialNumber
        LEFT JOIN pallets SSRP on SSRP.idPallet = SSR.idPallet
        LEFT JOIN disposition SSRD ON SSRD.`disposition_id` = SSR.`origin_disposition_id`
        LEFT JOIN users AU on AU.UserId = A.CreatedBy
        LEFT JOIN facility AF on AF.FacilityID = A.FacilityID
        LEFT JOIN workflow_input SSRW ON SSRW.input_id = SSR.input_id
        LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
        LEFT JOIN workflow_input ASW ON ASW.input_id = A.input_id
        LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN shipping_containers ASCS ON ASCS.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCL ON ASCL.LocationID = ASCS.LocationID
        LEFT JOIN location_group ASCLG ON ASCLG.GroupID = ASCL.GroupID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        order by A.event_id,A.serial_scan_time ASC";
$query1 = mysqli_query($connectionlink1,$sql1);
$i = 0;
while($row1 = mysqli_fetch_assoc($query1))
{
    /*if($row1['FacilityName'] == 'CVG110')
	{
		$row1['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row1['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row1['FacilityName'] == 'DUB210')
	{
		$row1['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row1['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row1['FacilityName'] == 'SIN100')
	{
		$row1['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row1['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    /*if($row1['event_id'] != $eventid)
    {
        $i = 0;
    }
    if($i ==0)
    {
        $eventid = $row1['event_id'];
        $timeFirst  = strtotime($row1['recovery_type_scan_time']);
        $timeSecond = strtotime($row1['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    else
    {
        $eventid = $row1['event_id'];
        $timeFirst  = $timeSecond;
        $timeSecond = strtotime($row1['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    $i = $i+1;*/
    $timeFirst  = strtotime($row1['CreatedDate']);
    $timeSecond = strtotime($row1['serial_scan_time']);
    $differenceInSeconds = $timeFirst - $timeSecond;
    if($row1['UpdatedDate'] == '')
    {
        $row1['UpdatedDate'] = $row1['CreatedDate'];
    }
    if($row1['ShippingContainerID'] != '')
    {
        $row1['bin_id'] = 'n/a';
        $row1['storage_location_group_id'] = $row1['GroupName'];
    }
    $start = strtotime($row1['CreatedDate']);
	$end = strtotime($row1['UpdatedDate']);
	$mins = ($end - $start) / 60;
	$hrs = $mins/60;
	$hrs = number_format($hrs,2);
    $date1 = explode(" ",$row1['DateCreated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("H:i:s", strtotime($row1['DateCreated']));
    if($row1['CreatedDate'] != '')
    {
        $row1['CreatedDate'] = date("Y-m-d H:i:s", strtotime($row1['CreatedDate']));
    }
    else
    {
        $row1['CreatedDate'] = '';
    }
    if($row1['tpvr_reason'] != '')
    {
        $row1['controller_scan_time'] = '';
    }
    $row1['operator_login_id'] = strtolower($row1['operator_login_id']);
    $row1['controller_login_id'] = strtolower($row1['controller_login_id']);
    $row1['entity_id'] = str_replace(","," ",$row1['entity_id']);
	$row1['recovered_serial_id'] = str_replace(","," ",$row1['recovered_serial_id']);
	$row1['mpn_id'] = str_replace(","," ",$row1['mpn_id']);
	$row1['part_type'] = str_replace(","," ",$row1['part_type']);
    $row1['manufacturer_id'] = str_replace(","," ",$row1['manufacturer_id']);
    $row1['source_type'] = str_replace(","," ",$row1['source_type']);
    $row1['assembly_id'] = str_replace(","," ",$row1['assembly_id']);
    $row1['assembly_mpn_id'] = str_replace(","," ",$row1['assembly_mpn_id']);
    $row1['assembly_part_type'] = str_replace(","," ",$row1['assembly_part_type']);
    $row1['assembly_bin_id'] = str_replace(","," ",$row1['assembly_bin_id']);
    $row1['assembly_next_step_action'] = str_replace(","," ",$row1['assembly_next_step_action']);
    $row1['assembly_next_step_rule_id'] = str_replace(","," ",$row1['assembly_next_step_rule_id']);
    $row1['operator_login_id'] = str_replace(","," ",$row1['operator_login_id']);
    $row1['recovery_location_id'] = str_replace(","," ",$row1['recovery_location_id']);
    $row1['recovery_result'] = str_replace(","," ",$row1['recovery_result']);
    $row1['bin_id'] = str_replace(","," ",$row1['bin_id']);
    $row1['container_id'] = str_replace(","," ",$row1['container_id']);
    $row1['next_step_action'] = str_replace(","," ",$row1['next_step_action']);
    $row1['next_step_rule_id'] = str_replace(","," ",$row1['next_step_rule_id']);
    $row1['workstation_id'] = str_replace(","," ",$row1['workstation_id']);
    $row1['storage_location_group_id'] = str_replace(","," ",$row1['storage_location_group_id']);
    $row1['coo_id'] = str_replace(","," ",$row1['coo_id']);
    $row1['assembly_classification_type'] = str_replace(","," ",$row1['assembly_classification_type']);
    $row1['assembly_classification_code_id'] = str_replace(","," ",$row1['assembly_classification_code_id']);
    $row1['classification_type'] = str_replace(","," ",$row1['classification_type']);
    $row1['classification_code_id'] = str_replace(","," ",$row1['classification_code_id']);
    $row1['controller_login_id'] = str_replace(","," ",$row1['controller_login_id']);
    $row1['tpvr_reason'] = str_replace(","," ",$row1['tpvr_reason']);
    $row1['recovery_verification_id'] = str_replace(","," ",$row1['recovery_verification_id']);
    $row1['batch_event_flag'] = str_replace(","," ",$row1['batch_event_flag']);
    $row1['event_id'] = str_replace(","," ",$row1['event_id']);
    $row1['customer_id'] = str_replace(","," ",$row1['customer_id']);
    
    if($row1['entity_id'] == '')
    {
        $row1['entity_id'] = 'n/a';
    }
    if($row1['recovery_type_scan_time'] == '')
    {
        $row1['recovery_type_scan_time'] = '';
    }
    if($row1['recovered_serial_id'] == '')
    {
        $row1['recovered_serial_id'] = 'n/a';
    }
    if($row1['serial_scan_time'] == '')
    {
        $row1['serial_scan_time'] = '';
    }
    if($row1['mpn_id'] == '')
    {
        $row1['mpn_id'] = 'n/a';
    }
    if($row1['mpn_scan_time'] == '')
    {
        $row1['mpn_scan_time'] = '';
    }
    if($row1['part_type'] == '')
    {
        $row1['part_type'] = 'n/a';
    }
    if($row1['part_type_scan_time'] == '')
    {
        $row1['part_type_scan_time'] = '';
    }
    if($row1['manufacturer_id'] == '')
    {
        $row1['manufacturer_id'] = 'n/a';
    }
    if($row1['source_type'] == '')
    {
        $row1['source_type'] = 'n/a';
    }
    if($row1['assembly_id'] == '')
    {
        $row1['assembly_id'] = 'n/a';
    }
    if($row1['assembly_id_scan_time'] == '')
    {
        $row1['assembly_id_scan_time'] = '';
    }
    if($row1['assembly_mpn_id'] == '')
    {
        $row1['assembly_mpn_id'] = 'n/a';
    }
    if($row1['assembly_part_type'] == '')
    {
        $row1['assembly_part_type'] = 'n/a';
    }
    if($row1['assembly_bin_id'] == '')
    {
        $row1['assembly_bin_id'] = 'n/a';
    }
    if($row1['assembly_next_step_action'] == '')
    {
        $row1['assembly_next_step_action'] = 'n/a';
    }
    if($row1['assembly_next_step_rule_id'] == '')
    {
        $row1['assembly_next_step_rule_id'] = 'n/a';
    }
    if($row1['operator_login_id'] == '')
    {
        $row1['operator_login_id'] = 'n/a';
    }
    if($row1['recovery_location_id'] == '')
    {
        $row1['recovery_location_id'] = 'n/a';
    }
    if($row1['recovery_result'] == '')
    {
        $row1['recovery_result'] = 'n/a';
    }
    if($row1['result_scan_time'] == '')
    {
        $row1['result_scan_time'] = '';
    }
    if($row1['bin_id'] == '')
    {
        $row1['bin_id'] = 'n/a';
    }
    if($row1['bin_scan_time'] == '')
    {
        $row1['bin_scan_time'] = '';
    }
    if($row1['container_id'] == '')
    {
        $row1['container_scan_time'] = '';
    }
    if($row1['container_id'] == '')
    {
        $row1['container_id'] = 'n/a';
    }
    if($row1['container_scan_time'] == '')
    {
        $row1['container_scan_time'] = '';
    }
    if($row1['next_step_action'] == '')
    {
        $row1['next_step_action'] = 'n/a';
    }
    if($row1['next_step_rule_id'] == '')
    {
        $row1['next_step_rule_id'] = 'n/a';
    }
    if($row1['workstation_id'] == '')
    {
        $row1['workstation_id'] = 'n/a';
    }
    if($row1['workstation_scan_time'] == '')
    {
        $row1['workstation_scan_time'] = '';
    }
    if($row1['storage_location_group_id'] == '')
    {
        $row1['storage_location_group_id'] = 'n/a';
    }
    if($row1['coo_id'] == '')
    {
        $row1['coo_id'] = 'n/a';
    }
    if($row1['assembly_classification_type'] == '')
    {
        $row1['assembly_classification_type'] = 'n/a';
    }
    if($row1['assembly_classification_code_id'] == '')
    {
        $row1['assembly_classification_code_id'] = 'n/a';
    }
    if($row1['classification_type'] == '')
    {
        $row1['classification_type'] = 'n/a';
    }
    if($row1['classification_code_id'] == '')
    {
        $row1['classification_code_id'] = 'n/a';
    }
    if($row1['controller_login_id'] == '')
    {
        $row1['controller_login_id'] = 'n/a';
    }
    if($row1['tpvr_reason'] == '')
    {
        $row1['tpvr_reason'] = 'n/a';
    }
    if($row1['tpvr_request_scan_time'] == '')
    {
        $row1['tpvr_request_scan_time'] = '';
    }
    if($row1['controller_scan_time'] == '')
    {
        $row1['controller_scan_time'] = '';
    }
    if($row1['recovery_verification_id'] == '')
    {
        $row1['recovery_verification_id'] = 'n/a';
    }
    if($row1['coo_scan_time'] == '')
    {
        $row1['coo_scan_time'] = 'n/a';
    }
    if($row1['batch_event_flag'] == '')
    {
        $row1['batch_event_flag'] = 'N';
    }
    if($row1['event_id'] == '')
    {
        $row1['event_id'] = 'n/a';
    }
    if($row1['customer_id'] == '')
    {
        $row1['customer_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    if($row1['recovery_type_scan_time'] != '')
    {
        if($row1['recovery_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['recovery_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['recovery_type_scan_time']));
        }
        else
        {
            $row1['recovery_type_scan_time'] = '';
        }
    }
    else
    {
        $row1['recovery_type_scan_time'] = '';
    }
    if($row1['serial_scan_time'] != '')
    {
        if($row1['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['serial_scan_time']));
        }
        else
        {
            $row1['serial_scan_time'] = '';
        }
    }
    else
    {
        $row1['serial_scan_time'] = '';
    }
    if($row1['mpn_scan_time'] != '')
    {
        if($row1['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['mpn_scan_time']));
        }
        else
        {
            $row1['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row1['mpn_scan_time'] = '';
    }
    if($row1['part_type_scan_time'] != '')
    {
        if($row1['part_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['part_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['part_type_scan_time']));
        }
        else
        {
            $row1['part_type_scan_time'] = '';
        }
    }
    else
    {
        $row1['part_type_scan_time'] = '';
    }
    if($row1['assembly_id_scan_time'] != '')
    {
        if($row1['assembly_id_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['assembly_id_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['assembly_id_scan_time']));
        }
        else
        {
            $row1['assembly_id_scan_time'] = '';
        }
    }
    else
    {
        $row1['assembly_id_scan_time'] = '';
    }
    if($row1['result_scan_time'] != '')
    {
        if($row1['result_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['result_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['result_scan_time']));
        }
        else
        {
            $row1['result_scan_time'] = '';
        }
    }
    else
    {
        $row1['result_scan_time'] = '';
    }
    if($row1['bin_scan_time'] != '')
    {
        if($row1['bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['bin_scan_time']));
        }
        else
        {
            $row1['bin_scan_time'] = '';
        }
    }
    else
    {
        $row1['bin_scan_time'] = '';
    }
    if($row1['container_scan_time'] != '')
    {
        if($row1['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['container_scan_time']));
        }
        else
        {
            $row1['container_scan_time'] = '';
        }
    }
    else
    {
        $row1['container_scan_time'] = '';
    }
    if($row1['workstation_scan_time'] != '')
    {
        if($row1['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['workstation_scan_time']));
        }
        else
        {
            $row1['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row1['workstation_scan_time'] = '';
    }
    if($row1['tpvr_request_scan_time'] != '')
    {
        if($row1['tpvr_request_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['tpvr_request_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['tpvr_request_scan_time']));
        }
        else
        {
            $row1['tpvr_request_scan_time'] = '';
        }
    }
    else
    {
        $row1['tpvr_request_scan_time'] = '';
    }
    if($row1['controller_scan_time'] != '')
    {
        if($row1['controller_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['controller_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['controller_scan_time']));
        }
        else
        {
            $row1['controller_scan_time'] = '';
        }
    }
    else
    {
        $row1['controller_scan_time'] = '';
    }
    if($row1['coo_scan_time'] != '')
    {
        if($row1['coo_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['coo_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['coo_scan_time']));
        }
        else
        {
            $row1['coo_scan_time'] = '';
        }
    }
    else
    {
        $row1['coo_scan_time'] = '';
    }
    $row22  = array('eV-Disposition-1',$row1['recovery_type_scan_time'],$row1['recovered_serial_id'],$row1['serial_scan_time'],$row1['mpn_id'],$row1['mpn_scan_time'],$row1['part_type'],$row1['part_type_scan_time'],$row1['manufacturer_id'],$row1['source_type'],$row1['assembly_id'],$row1['assembly_id_scan_time'],$row1['assembly_mpn_id'],$row1['assembly_part_type'],$row1['assembly_bin_id'],$row1['assembly_next_step_action'],$row1['assembly_next_step_rule_id'],$row1['operator_login_id'],$row1['recovery_location_id'],$row1['recovery_result'],$row1['result_scan_time'],$row1['CreatedDate'],$row1['bin_id'],$row1['bin_scan_time'],$row1['container_id'],$row1['container_scan_time'],$row1['next_step_action'],$row1['next_step_rule_id'],$row1['workstation_id'],$row1['workstation_scan_time'],$row1['storage_location_group_id'],$row1['coo_id'],$row1['assembly_classification_type'],$row1['assembly_classification_code_id'],$row1['classification_type'],$row1['classification_code_id'],$row1['controller_login_id'],$row1['tpvr_reason'],$row1['tpvr_request_scan_time'],$row1['controller_scan_time'],$row1['recovery_verification_id'],$row1['coo_scan_time'],$row1['batch_event_flag'],$row1['event_id'],$row1['customer_id'],$differenceInSeconds);
    $rows[] = $row22;
}

foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34].','.$record[35].','.$record[36].','.$record[37].','.$record[38].','.$record[39].','.$record[40].','.$record[41].','.$record[42].','.$record[43].','.$record[44].','.$record[45]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>