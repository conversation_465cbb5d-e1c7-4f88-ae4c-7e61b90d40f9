(function () {
    'use strict';

    angular.module('app').controller("transfer_container_conversion", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {        
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Transfer Container Conversion',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
        
        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        

        $scope.newContainer = {};
        $scope.addingContainer = false;

        $scope.Dispositions = [];
        $scope.PackageTypes = [];
        $scope.Facilities = [];       

        $scope.GetCurrentFacility = function () {
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetSesstionFacility',
                success: function (data) {
                    if (data.Success) {                    
                        $scope.newContainer.FacilityID = data.FacilityID;         
                        $scope.GetFacilityContainers();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );                    
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        
        $scope.GetCurrentFacility();

        $scope.FocusNextField = function (nextid,wait) {            
            if(wait == '1') {
                setTimeout(function () {
                    $window.document.getElementById(nextid).focus();
                }, 100);
            } else {
                $window.document.getElementById(nextid).focus();
            }
        };

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                } else {
                    $scope.Facilities = [];
                }                
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {                
                initSessionTime(); $scope.$apply();;
            }
        });

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.Dispositions = data.Result;
                } else {
                    $scope.Dispositions = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });        


        $scope.GetInboundContainerDetails = function (idPallet) {
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/inter_facility_transfer_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetInboundContainerDetails&idPallet='+idPallet,
                success: function (data) {
                    if (data.Success) {
                        $scope.newContainer.LoadId = data.Result.LoadId;
                        $scope.newContainer.MaterialType = data.Result.MaterialType;

                        $scope.newContainer.CurrentLocation = data.Result.LocationName;
                        $scope.newContainer.parttype = data.Result.parttype;
                        $scope.newContainer.MaterialType = data.Result.MaterialType;
                        $scope.newContainer.ContainerNotes = 'n/a';
                        $scope.newContainer.SealID = data.Result.SealNo1;
                        $scope.newContainer.ContainerWeight = data.Result.pallet_netweight;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );   
                        
                        $scope.newContainer.LoadId = '';
                        $scope.newContainer.MaterialType = '';

                        $scope.newContainer.CurrentLocation = '';
                        $scope.newContainer.parttype = '';
                        $scope.newContainer.MaterialType = '';
                        $scope.newContainer.ContainerNotes = '';
                        $scope.newContainer.SealID = '';
                        $scope.newContainer.ContainerWeight = '';
                    }
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        
        $scope.GetFacilityContainers = function () {
            if($scope.newContainer.FacilityID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetFacilityPackageTypes&FacilityID='+$scope.newContainer.FacilityID,
                    success: function (data) {
                        if (data.Success) {
                            $scope.PackageTypes = data.Result;
                        } else {
                            $scope.PackageTypes = [];
                            $scope.newContainer.idPackage = '';
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.PackageTypes = [];
                $scope.newContainer.idPackage = '';
            }           
        };
        

        $scope.CreateContainer = function () {                       
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/inter_facility_transfer_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ConvertInboundToOutboundContainer&' + $.param($scope.newContainer),
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $scope.newContainer = {};   
                        $scope.GetCurrentFacility();                                                                  
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });                     
        };

        function SanitizationTPVRController($scope, $mdDialog, $mdToast) {
            $scope.hide = function () {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function () {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        function afterShowAnimation () {            
            $window.document.getElementById("password").focus();
        }
        $scope.confirmDetails = {};
        $scope.ValidateSanitizationControllerPopup = function (ev) {
            $scope.newContainer.PasswordVerified = false;
            $mdDialog.show({
                controller: SanitizationTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose: true
            })
                .then(function (confirmDetails) {
                    $rootScope.$broadcast('preloader:active');
                    $scope.confirmDetails = confirmDetails;
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateRemovalController&UserName=' + $scope.newContainer.ShippingControllerLoginID + '&Password=' + $scope.confirmDetails.Password,
                        success: function (data) {
                            if (data.Success) {
                                $scope.newContainer.PasswordVerified = true;
                                if($scope.newContainer.CreatedBy > 0) {//Updating existing
                                    setTimeout(function () {
                                        $window.document.getElementById('SerialNumber').focus();
                                    }, 100);
                                } else {
                                    // setTimeout(function () {
                                    //     $window.document.getElementById('save_button').focus();
                                    // }, 100);
                                    if(!$scope.containerForm.$invalid) {
                                        $scope.CreateContainer();
                                    }                                    
                                }                                
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                $scope.newContainer.PasswordVerified = false;
                            }
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $scope.newContainer.PasswordVerified = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }, function (confirmDetails) {
                    $scope.confirmDetails = confirmDetails;
                });
        };


        function LocationChange(text) {
            $scope.newContainer.location = text;
        }

        function selectedLocationChange(item) {
            if (item) {
                if (item.value) {
                    $scope.newContainer.location = item.value;
                } else {
                    $scope.newContainer.location = '';
                }
            } else {
                $scope.newContainer.location = '';
            }
        }

        $scope.queryLocationSearch = queryLocationSearch;
        $scope.LocationChange = LocationChange;
        $scope.selectedLocationChange = selectedLocationChange;
        function queryLocationSearch(query) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'administration/includes/admin_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + $scope.newContainer.FacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }

        function LocationChange1(text) {
            $scope.newContainer.group = text;
        }

        function selectedLocationChange1(item) {
            if (item) {
                if (item.value) {
                    $scope.newContainer.group = item.value;
                } else {
                    $scope.newContainer.group = '';
                }
            } else {
                $scope.newContainer.group = '';
            }
        }

        $scope.queryLocationSearch1 = queryLocationSearch1;
        $scope.LocationChange1 = LocationChange1;
        $scope.selectedLocationChange1 = selectedLocationChange1;
        function queryLocationSearch1(query) {
            if (query) {
                if (query != '' && query != 'undefined') {                    
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + $scope.newContainer.FacilityID+'&LocationType=Outbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }

        
    });
})();
