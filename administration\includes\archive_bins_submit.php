<?php
session_start();
include_once("../database/archive_bins.class.php");
$obj = new ArchiveBinsClass();

if($_POST['ajax'] == "GetArchiveBins") {
    $result = $obj->GetArchiveBins($_POST);
    echo $result;
}

if($_POST['ajax'] == "GenerateArchiveBinsListxls") {
    $result = $obj->GenerateArchiveBinsListxls($_POST);
    echo $result;
}

if($_POST['ajax'] == "ReActivateBin") {
    $result = $obj->ReActivateBin($_POST);
    echo $result;
}

if($_POST['ajax'] == "GetLocationGroups") {
    $result = $obj->GetLocationGroups($_POST);
    echo $result;
}
?>
