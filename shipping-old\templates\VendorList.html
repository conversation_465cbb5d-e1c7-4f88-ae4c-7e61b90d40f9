<div ng-controller = "VendorList" class="page">

    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">  
                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="VendorList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                <i ng-click="VendorList = !VendorList" class="material-icons md-primary" ng-show="VendorList">keyboard_arrow_up</i>
                                <i ng-click="VendorList = !VendorList" class="material-icons md-primary" ng-show="! VendorList">keyboard_arrow_down</i>
                                <span ng-click="VendorList = !VendorList">Destination List</span>
                                <div flex></div> 
                                <a href="#!/Vendor" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Destination
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="VendorList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div class="table-responsive" style="overflow: auto;">
                                        <div ng-show="pagedItems" class="pull-right" style="margin-top: 10px;">
                                            <small>
                                                Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                                to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                    <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                                of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div>
                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>
        
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('VendorName')" ng-class="{'orderby' : OrderBy == 'VendorName'}">
                                                        <div>                               
                                                            Destination Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'VendorName'"></i>                                 
                                                            <span ng-show="OrderBy == 'VendorName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                                                                                                            
                                                    <th style="cursor:pointer; min-width:140px;" ng-click="MakeOrderBy('VendorCategoryName')" ng-class="{'orderby' : OrderBy == 'VendorCategoryName'}">                           
                                                        <div>                               
                                                            Destination Category <i class="fa fa-sort pull-right" ng-show="OrderBy != 'VendorCategoryName'"></i>                                    
                                                            <span ng-show="OrderBy == 'VendorCategoryName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                        
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('StreetAddress')" ng-class="{'orderby' : OrderBy == 'StreetAddress'}">                          
                                                        <div>                               
                                                            Street Address <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StreetAddress'"></i>                                  
                                                            <span ng-show="OrderBy == 'StreetAddress'">                                  
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                   
                                                    <th style="cursor:pointer; min-width:220px;" ng-click="MakeOrderBy('PaymentCode')" ng-class="{'orderby' : OrderBy == 'PaymentCode'}">                         
                                                        <div>                               
                                                            Payment Method <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PaymentCode'"></i>                                  
                                                            <span ng-show="OrderBy == 'PaymentCode'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
        
                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="VendorName" ng-model="filter_text[0].VendorName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
            
                                                    <td>
                                                        <md-input-container class="md-block mt-0"><input type="text" name="VendorCategoryName" ng-model="filter_text[0].VendorCategoryName" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0"><input type="text" name="StreetAddress" ng-model="filter_text[0].StreetAddress" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0"><input type="text" name="PaymentCode" ng-model="filter_text[0].PaymentCode" ng-change="MakeFilter()" aria-label="text" /></md-input-container>
                                                    </td>
                                                                   
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/Vendor/{{product.VendorID}}"><md-icon class="material-icons text-danger">edit</md-icon></a></td>
            
                                                    <td>
                                                        {{product.VendorName}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.VendorCategoryName}}
                                                    </td>
                                                    <td>
                                                        {{product.StreetAddress}}
                                                    </td>
                                                    <td>
                                                        {{product.PaymentCode}}
                                                    </td>     
                                                </tr>
                                            </tbody>
                                            
                                        <tfoot>
                                                <tr>
                                                    <td colspan="9">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </md-card> 
                </div>

            </article>
        </div>
    </div>

</div>