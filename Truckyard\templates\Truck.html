<div class="row page" data-ng-controller="Truck">
    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h">                
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Truck Booking</span>
                        <div flex></div>
                           <a href="#!/TruckList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i>Back to List
                            </a>
                    </div>
                </md-toolbar>                
                <div class="row">
                    <div class="col-md-12">
                        <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityID" ng-model="Truck.FacilityID" required ng-disabled="true">
                                        <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Arrival Type</label>
                                    <md-select name="ArrivalType" ng-model="Truck.ArrivalType" required aria-label="select">
                                        <md-option value="Inbound"> Inbound </md-option>
                                        <md-option value="Outbound"> Outbound </md-option>
                                    </md-select>  
                                    <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.ArrivalType.$error" multiple ng-if='material_signup_form.ArrivalType.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Parking Location</label>
                                    <md-select name="ParkingLocationID" ng-model="Truck.ParkingLocationID" required>
                                        <md-option value="{{ParkingLocation.ParkingLocationID}}" ng-repeat="ParkingLocation in ParkingLocations">{{ParkingLocation.ParkingLocationName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ParkingLocationID.$error" multiple ng-if='material_signup_form.ParkingLocationID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Carrier</label>
                                    <md-select name="CarrierID"
                                               ng-model="Truck.CarrierID"
                                               required
                                               ng-change="onCarrierChange()">
                                        <md-option ng-value="Carrier.CarrierID" ng-repeat="Carrier in Carriers">
                                            {{Carrier.CarrierName}}
                                        </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.CarrierID.$error" multiple ng-if='material_signup_form.CarrierID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Date</label>
                                    <md-datepicker ng-model="Truck.ArrivalDate" required
                                    input-aria-describedby="datepicker-description"
                                    input-aria-labelledby="datepicker-header "></md-datepicker>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Expected Arrival Time</label>
                                    <input type="time" required name="ArrivalTime" ng-model="Truck.ArrivalTime" >
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ArrivalTime.$error" multiple ng-if='material_signup_form.ArrivalTime.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <!--  <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Departure Time</label>
                                    <input type="time" required name="DepartureTime" ng-model="Truck.DepartureTime" >
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.DepartureTime.$error" multiple ng-if='material_signup_form.DepartureTime.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                        </div>
                                    </div>
                                </md-input-container>
                            </div> -->
                             <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Load Type</label>
                                    <md-select name="LoadType" ng-model="Truck.LoadType" required aria-label="select">
                                        <md-option value="Pallet"> Pallet </md-option>
                                        <md-option value="Rack"> Rack </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.LoadType.$error" multiple ng-if='material_signup_form.LoadType.$dirty'>
                                            <div ng-message="required">This is required.</div>                                           
                                        </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Load Number</label>
                                    <input type="text" name="LoadNumber"  ng-model="Truck['LoadNumber']"  required ng-maxlength="50" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.LoadNumber.$error" multiple ng-if='material_signup_form.LoadNumber.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Vehicle Type</label>
                                    <md-select name="TruckTypeID" ng-model="Truck.TruckTypeID" required>
                                        <md-option value="{{TruckType.TruckTypeID}}" ng-repeat="TruckType in TruckTypes">{{TruckType.TruckTypeName}}</md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.TruckTypeID.$error" multiple ng-if='material_signup_form.TruckTypeID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Truck Reg</label>
                                    <input type="text" name="TruckReg"  ng-model="Truck['TruckReg']"  required ng-maxlength="50" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.TruckReg.$error" multiple ng-if='material_signup_form.TruckReg.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Trailer Number</label>
                                    <input type="text" name="TrailerNumber"  ng-model="Truck['TrailerNumber']"  required ng-maxlength="50" />
                                     <div class="error-sapce">
                                    <div ng-messages="material_signup_form.TrailerNumber.$error" multiple ng-if='material_signup_form.TrailerNumber.$dirty'>                            
                                        <div ng-message="required">This is required.</div> 
                                        <div ng-message="minlength">Min length 3.</div>
                                        <div ng-message="maxlength">Max length 50.</div>                           
                                    </div>
                                </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Driver</label>
                                    <input type="text" name="DriverName"  ng-model="Truck['DriverName']"  required ng-maxlength="50" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.DriverName.$error" multiple ng-if='material_signup_form.DriverName.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 50.</div>                           
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Driver ID</label>
                                    <input type="text" name="DriverID"  ng-model="Truck['DriverID']" ng-maxlength="50" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.DriverID.$error" multiple ng-if='material_signup_form.DriverID.$dirty'>                            
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Shipment Ticket ID's</label>
                                    <input type="text" name="ShipmentTicketID"  ng-model="Truck['ShipmentTicketID']"  ng-maxlength="50" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ShipmentTicketID.$error" multiple ng-if='material_signup_form.ShipmentTicketID.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 50.</div>                           
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Classification Type</label>
                                    <md-select name="ClassificationType"
                                               ng-model="Truck.ClassificationType"
                                               required
                                               ng-change="onCarrierChange()">
                                        <md-option value="All">All</md-option>
                                        <md-option value="UEEE">UEEE</md-option>
                                        <md-option value="WEEE" ng-disabled="selectedCarrier?.WasteCollectionEligible != 1">WEEE</md-option>
                                    </md-select>
                                     <div class="error-sapce"> 
                                        <div ng-messages="material_signup_form.ClassificationType.$error" multiple ng-if='material_signup_form.ClassificationType.$dirty'>
                                            <div ng-message="required">WEEE classification requires waste collection eligibility for the selected carrier.</div>
                                        </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Waste Collection Permit</label>
                                    <input type="text"
                                       name="WasteCollectionPermit"
                                       ng-model="Truck.WasteCollectionPermit"
                                       ng-maxlength="250"
                                       ng-required="Truck.ClassificationType === 'WEEE' && Truck.FacilityID == 14" disabled />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.WasteCollectionPermit.$error" multiple ng-if='material_signup_form.WasteCollectionPermit.$dirty'>
                                            <div ng-message="required">This is required when Classification Type is WEEE and Facility is DUB210.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 250.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Notes</label>
                                    <input type="text" name="Notes"  ng-model="Truck['Notes']"  ng-maxlength="250" />
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.Notes.$error" multiple ng-if='material_signup_form.Notes.$dirty'>                            
                                            <div ng-message="maxlength">Max length 250.</div>                           
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">                                            
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="Truck.Status" required aria-label="select">
                                        <md-option value="Reserved"> Reserved </md-option>
                                        <md-option value="Requested"> Requested </md-option>
                                    </md-select>  
                                     <div class="error-sapce"> 
                                    <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>                                           
                                    </div> 
                                    </div>                                            
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/TruckList" style="text-decoration: none;">
                                    <md-button class="md-raised btn-w-md md-default btn-w-md">Cancel</md-button>
                                </a>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="TruckSave()">
                                <span ng-show="! Truck.busy">Save</span>
                                <span ng-show="Truck.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
        </article>        
    </div>
</div>