<div class="page" data-ng-controller="removed_shipments">
    <div class="row ui-section">            
        <div class="col-md-12">
            <article class="article">

                <!--Alert Start-->
                <!-- <div class="alert alert-warning" role="alert" ng-init="showAlert9=true;" ng-show="showAlert9">
                    <i class="material-icons">error</i> <strong class="mr-5">Warning!</strong> There was a problem with your network connection.
                    <i class="material-icons alert-close" ng-click="showAlert9 = ! showAlert9">close</i>
                </div> -->
                <!--Alert End-->

                <md-card class="no-margin-h">
                    
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ShipmentremovallistPanel = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" >
                            <i class="material-icons md-primary" ng-show="ShipmentremovallistPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ShipmentremovallistPanel">keyboard_arrow_down</i>
                            <span ng-click="ShipmentremovallistPanel = !ShipmentremovallistPanel">Removed Shipments</span>
                            <div flex></div>
                            <a ng-click="RemoveshipXLS()" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                <md-icon class="excel_icon mr-5" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                            </a>
                        </div>
                    </md-toolbar>
                    
                    <md-card-content style="padding: 0px 16px;" ng-show="ShipmentremovallistPanel">
                        <!-- <md-table-container> -->
                            <div class="row"  ng-show="Shipments.length > 0">
                                <div class="col-md-12">         
                                    <div ng-show="Shipments.length > 0" class="pull-right mt-10">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>                            
                                    <div class="table-responsive" style="overflow: auto;">                                        
                                        
                                                    
                                        <table class="table mb-0 multi-tables" md-table>
        
                                            <thead md-head>
        
                                                <tr class="th_sorting" md-row>
                                                    <th>
                                                        Action
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ShippingID')" ng-class="{'orderby' : OrderBy == 'ShippingID'}">
                                                        <div>                               
                                                            Ticket ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ShippingID'"></i>                                 
                                                            <span ng-show="OrderBy == 'ShippingID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">                           
                                                        <div>                               
                                                            Removal Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>                                    
                                                            <span ng-show="OrderBy == 'disposition'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                           
                                                        <div>                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                    
                                                            <span ng-show="OrderBy == 'FacilityName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('VendorName')" ng-class="{'orderby' : OrderBy == 'VendorName'}">                           
                                                        <div>                               
                                                            Destination <i class="fa fa-sort pull-right" ng-show="OrderBy != 'VendorName'"></i>                                    
                                                            <span ng-show="OrderBy == 'VendorName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('DestinationFacilityName')" ng-class="{'orderby' : OrderBy == 'DestinationFacilityName'}">                           
                                                        <div>                               
                                                            Destination Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DestinationFacilityName'"></i>                                    
                                                            <span ng-show="OrderBy == 'DestinationFacilityName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ContactName')" ng-class="{'orderby' : OrderBy == 'ContactName'}"> 
                                                        <div style="min-width: 180px;">                               
                                                            Destination POC <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ContactName'"></i>                                    
                                                            <span ng-show="OrderBy == 'ContactName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>   
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ApprovedDate')" ng-class="{'orderby' : OrderBy == 'ApprovedDate'}">
                                                        <div>                               
                                                            Approved Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ApprovedDate'"></i>                                    
                                                            <span ng-show="OrderBy == 'ApprovedDate'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ShippedDate')" ng-class="{'orderby' : OrderBy == 'ShippedDate'}">
                                                        <div>                               
                                                            Removal Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ShippedDate'"></i>                                    
                                                            <span ng-show="OrderBy == 'ShippedDate'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th> 
                                                     <th>
                                                        <div style="width:80px; min-width: 80px;">Export</div>
                                                    </th>                                                
                                                </tr>
                                                
                                                <tr md-row class="errornone">
                                                    <td></td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ShippingID" ng-model="filter_text[0].ShippingID" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <!-- <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container> -->
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="VendorName" ng-model="filter_text[0].VendorName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DestinationFacilityName" ng-model="filter_text[0].DestinationFacilityName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ContactName" ng-model="filter_text[0].ContactName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ApprovedDate" ng-model="filter_text[0].ApprovedDate" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ShippedDate" ng-model="filter_text[0].ShippedDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td></td>
                                                </tr>
                                            </thead>
        
                                            <tbody md-body ng-show="Shipments.length > 0" ng-repeat="ship in Shipments">
                                                <tr md-row>
                                                    <td md-cell class="actionicons" style="min-width: 100px;" ng-init="ship.showDetails = false;">  
                                                        <i class="material-icons add text-warning" ng-click="ship.showDetails = !ship.showDetails;GetShipmentContainers(ship)" ng-show="ship.showDetails">remove</i>
                                                        <i class="material-icons add text-success" ng-click="ship.showDetails = !ship.showDetails;GetShipmentContainers(ship)" ng-show="! ship.showDetails">add</i>                                           
                                                        <!-- <i class="material-icons edit text-danger">edit</i> -->
                                                        <a href="{{host}}label/master/examples/shipmentlabel.php?id={{ship.ShippingID}}" target="_blank">
                                                        <i class="material-icons print">print</i></a>
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.ShippingID}}                            
                                                    </td>                       
                                                    <td md-cell>
                                                        <!-- {{ship.disposition}} -->
                                                        <span ng-if="ship.disposition.includes(',')"><b>Multiple Dispositions</b></span>
                                                        <span ng-if="!ship.disposition.includes(',')">{{ ship.disposition }}</span>
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.FacilityName}}
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.VendorName}}
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.DestinationFacilityName}}                            
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.ContactName}}
                                                    </td>     
                                                    <td md-cell>
                                                        {{ship.ApprovedDate}}
                                                        <!-- {{ship.ApprovedDate | toDate | date:'MMM dd, yyyy'}} -->
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.ShippedDate}}
                                                        <!-- {{ship.ShippedDate  | toDate | date:'MMM dd, yyyy'}} -->
                                                    </td> 
                                                     <td md-cell>
                                                        <a class="md-button md-raised md-default" ng-click="exportshipment(ship.ShippingID)" style="min-height:30px; line-height:30px;">
                                                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon> <span>Export</span>
                                                        </a>
                                                    </td>
                                                </tr>
                                                <tr ng-show="ship.showDetails && !ship.loading">
                                                    <td></td>
                                                    <td colspan="8">
                                                        <div class="callout callout-warning" ng-show="ship.Containers.length == 0 && !ship.loading">
                                                            <p>No Containers Available.</p>
                                                        </div>
                                                        <table md-table class="table" style="background-color: transparent;" ng-show="ship.Containers.length > 0">
                                                            <thead md-head>
                                                                <tr md-row>
                                                                    <th md-column>Action</th>
                                                                    <th md-column>Container ID</th>
                                                                    <th md-column>Container Type</th>
                                                                    <th md-column>Removal Type</th>
                                                                    <th md-column>Custom ID</th>
                                                                    <th md-column>Seal ID</th>                                                                    
                                                                    <th md-column>Container Notes</th>                                                                    
                                                                </tr>
                                                            </thead>
                                                            <tbody md-body>
                                                                <tr md-row ng-repeat="cont in ship.Containers">
                                                                    <td md-cell class="actionicons" style="min-width: 50px;">
                                                                        <i class="material-icons print">print</i>
                                                                    </td>
                                                                    <td md-cell>{{cont.ShippingContainerID}}</td>
                                                                    <td md-cell>{{cont.packageName}}</td>
                                                                    <td md-cell>{{cont.disposition}}</td>
                                                                    <td md-cell>{{cont.CustomID}}</td>
                                                                    <td md-cell>{{cont.SealID}}</td>                                                                    
                                                                    <td md-cell>{{cont.ContainerNotes}}</td>                                                                    
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>

                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="10">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>
                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        <!-- </md-table-container> -->
                    </md-card-content>
                </md-card>



                <!--List Start-->                
                <md-card class="no-margin-h" style="display: none;">
                    
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ShipmentremovallistPanel = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="ShipmentremovallistPanel = !ShipmentremovallistPanel">
                            <i class="material-icons md-primary" ng-show="ShipmentremovallistPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ShipmentremovallistPanel">keyboard_arrow_down</i>
                            <span>list Of Shipment Removal</span>
                        </div>
                    </md-toolbar>
                    
                    <md-card-content style="padding: 0px 16px;" ng-show="ShipmentremovallistPanel">
                        <md-table-container>
                            <table md-table class="table mb-0 multi-tables">
                                <thead md-head>
                                    <tr>
                                        <th md-column>Action</th>
                                        <th md-column style="min-width: 160px;">Ticket ID</th>
                                        <th md-column>Removal Type</th>
                                        <th md-column>Destination Location</th>
                                        <th md-column>Destination POC</th> 
                                        <th md-column></th>
                                        <th md-column></th>
                                        <th md-column>Delete</th>                                                                                                                                                
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row>
                                        <td md-cell class="actionicons">  
                                            <i class="material-icons add text-warning" ng-click="ContainerPanel0 = !ContainerPanel0" ng-show="ContainerPanel0">remove</i>
                                            <i class="material-icons add text-success" ng-click="ContainerPanel0 = !ContainerPanel0" ng-show="! ContainerPanel0">add</i>                                           
                                            <i class="material-icons edit text-danger">edit</i>
                                            <i class="material-icons print">print</i>
                                        </td>
                                        <td md-cell>WW-RZRR-123</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>CVG110</td>
                                        <td md-cell>caseyc</td>
                                        <td md-cell>
                                            <button class="md-button md-raised md-primary" style="display: flex; min-height: 30px; min-width: 100px;">
                                                <i class="material-icons mr-5">local_shipping</i> Remove
                                            </button>
                                        </td>
                                        <td md-cell>
                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                <md-select class="bordered" name="Deleteshipment0" placeholder="Choose Bin" ng-model="shipmentprepremoval.Deleteshipment0" required>
                                                    <md-option value="dlship1"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                    <md-option value="dlship12"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                </md-select>
                                            </md-input-container>
                                        </td>
                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>                              
                                    </tr>

                                    <tr ng-show="ContainerPanel0">
                                        <td colspan="8">
                                            
                                            <table md-table class="table" style="background-color: transparent;">
                                                <thead md-head>
                                                    <tr md-row>
                                                        <th style="min-width:100px" md-column>Action</th>
                                                        <th md-column>Container ID</th>
                                                        <th md-column>Container Type</th>
                                                        <th md-column>Custom ID</th>
                                                        <th md-column>Seal ID</th>
                                                        <th md-column>Controller Login</th>
                                                        <th md-column>Container Notes</th>
                                                        <th md-column></th>
                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                    </tr>
                                                </thead>
                                                <tbody md-body>
                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel00 = !SerialIdsPanel00" ng-show="SerialIdsPanel00">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel00 = !SerialIdsPanel00" ng-show="! SerialIdsPanel00">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003444</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>22334455</td>
                                                        <td md-cell>B003456</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                <md-select class="bordered" name="DeleteLocation1122" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation1122" required>
                                                                    <md-option value="dlbin1122"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin11222"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr>

                                                    <tr ng-show="SerialIdsPanel00">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th> 
                                                                        <th md-column></th> 
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341920</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-NS1</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput"  style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationOne1" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationOne1" required>
                                                                                    <md-option value="dlbinone1"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4one12"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>

                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel11 = !SerialIdsPanel11" ng-show="SerialIdsPanel11">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel11 = !SerialIdsPanel11" ng-show="! SerialIdsPanel11">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003466</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>24334456</td>
                                                        <td md-cell>B003458</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                <md-select class="bordered" name="DeleteLocation1103" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation1103" required>
                                                                    <md-option value="dlbin1103"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin11032"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr> 
                
                                                    <tr ng-show="SerialIdsPanel11">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th> 
                                                                        <th md-column></th>  
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341909</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-QB</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationTwo2" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationTwo2" required>
                                                                                    <md-option value="dlbintwo2"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4two22"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>
                                                                                  
                                                </tbody>
                
                                            </table>

                                        </td>
                                    </tr>

                                    <tr md-row>
                                        <td md-cell class="actionicons">  
                                            <i class="material-icons add text-warning" ng-click="ContainerPanel1 = !ContainerPanel1" ng-show="ContainerPanel1">remove</i>
                                            <i class="material-icons add text-success" ng-click="ContainerPanel1 = !ContainerPanel1" ng-show="! ContainerPanel1">add</i>                                           
                                            <i class="material-icons edit text-danger">edit</i>
                                            <i class="material-icons print">print</i>
                                        </td>
                                        <td md-cell>WW-RZRR-125</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>CVG110</td>
                                        <td md-cell>caseyc</td>
                                        <td md-cell>
                                            <button class="md-button md-raised md-primary" style="display: flex; min-height: 30px; min-width: 100px;">
                                                <i class="material-icons mr-5">local_shipping</i> Remove
                                            </button>
                                        </td>
                                        <td md-cell>
                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                <md-select class="bordered" name="Deleteshipment1" placeholder="Choose Bin" ng-model="shipmentprepremoval.Deleteshipment1" required>
                                                    <md-option value="dlship21"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                    <md-option value="dlship22"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                </md-select>
                                            </md-input-container>
                                        </td>
                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>                              
                                    </tr>
                                    
                                    <tr ng-show="ContainerPanel1">
                                        <td colspan="8">
                                            
                                            <table md-table class="table" style="background-color: transparent;">
                                                <thead md-head>
                                                    <tr md-row>
                                                        <th style="min-width:100px" md-column>Action</th>
                                                        <th md-column>Container ID</th>
                                                        <th md-column>Container Type</th>
                                                        <th md-column>Custom ID</th>
                                                        <th md-column>Seal ID</th>
                                                        <th md-column>Controller Login</th>
                                                        <th md-column>Container Notes</th>
                                                        <th md-column></th>
                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                    </tr>
                                                </thead>
                                                <tbody md-body>
                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel0 = !SerialIdsPanel0" ng-show="SerialIdsPanel0">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel0 = !SerialIdsPanel0" ng-show="! SerialIdsPanel0">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003456</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>22334455</td>
                                                        <td md-cell>B003456</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style=" display: none;">
                                                                <md-select class="bordered" name="DeleteLocation112" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation112" required>
                                                                    <md-option value="dlbin1"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin2"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr>

                                                    <tr ng-show="SerialIdsPanel0">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th>
                                                                        <th md-column></th>  
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341920</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-NS1</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationOne" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationOne" required>
                                                                                    <md-option value="dlbin3"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>

                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel1 = !SerialIdsPanel1" ng-show="SerialIdsPanel1">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel1 = !SerialIdsPanel1" ng-show="! SerialIdsPanel1">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003458</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>24334456</td>
                                                        <td md-cell>B003458</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style=" display: none;">
                                                                <md-select class="bordered" name="DeleteLocation110" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation110" required>
                                                                    <md-option value="dlbin11"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin22"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr> 
                
                                                    <tr ng-show="SerialIdsPanel1">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th>
                                                                        <th md-column></th>  
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341909</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-NS2</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationTwo" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationTwo" required>
                                                                                    <md-option value="dlbin3"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>
                                                                                  
                                                </tbody>
                
                                            </table>

                                        </td>
                                    </tr>
                                                                  
                                </tbody>

                            </table>
                        </md-table-container>
                    </md-card-content>

                </md-card>
                <!--List Close-->

                <md-card class="no-margin-h" ng-show="CurrentShipment.ShippingID">          
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ShipmentremovalPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="ShipmentremovalPanel = !ShipmentremovalPanel">                            
                            <i class="material-icons md-primary" ng-show="ShipmentremovalPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ShipmentremovalPanel">keyboard_arrow_down</i>
                            <span>Shipment Removal</span>
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="ShipmentremovalPanel">  

                        <form name="shipmentForm">
                            <div class="col-md-12">
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Ticket ID</label>
                                        <input name="ShippingID" ng-disabled="true" ng-model="CurrentShipment.ShippingID" />
                                    </md-input-container>
                                </div>                                
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Removal Type</label>
                                        <input name="disposition" ng-disabled="true" ng-model="CurrentShipment.disposition" />
                                    </md-input-container>
                                </div>
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Destination</label>
                                        <input name="VendorName" ng-disabled="true" ng-model="CurrentShipment.VendorName" />
                                    </md-input-container>
                                </div>
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Destination POC</label>
                                        <input name="ContactName" ng-disabled="true" ng-model="CurrentShipment.ContactName" />
                                    </md-input-container>
                                </div>  
                                <div class="col-md-3">
                                    <md-input-container class="md-block" flex-gt-sm>
                                        <label>Next Step Action</label>
                                        <input required name="NextStep" ng-model="CurrentShipment.NextStep" ng-maxlength="1000" >
                                        <div ng-messages="shipmentForm.NextStep.$error" multiple ng-if='shipmentForm.NextStep.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 1000.</div> 
                                        </div>
                                    </md-input-container>
                                </div>                         
                            </div>                        
                            <div class="col-md-12">
                                <div class="bg-grey-light">
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Approver Login</label>
                                            <input type="text" required name="ApproverLogin" ng-model="CurrentShipment.ApproverLogin" ng-maxlength="100" >
                                            <div ng-messages="shipmentForm.ApproverLogin.$error" multiple ng-if='shipmentForm.ApproverLogin.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div> 
                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Approval Date</label>
                                            <md-datepicker name="ApprovedDate" ng-model="CurrentShipment.ApprovedDate" aria-label="Enter date" required ></md-datepicker>
                                            <div ng-messages="shipmentForm.ApprovedDate.$error" multiple ng-if='shipmentForm.ApprovedDate.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>                                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Removal Date</label>
                                            <md-datepicker name="ShippedDate" ng-model="CurrentShipment.ShippedDate" aria-label="Enter date" required ></md-datepicker>
                                            <div ng-messages="shipmentForm.ShippedDate.$error" multiple ng-if='shipmentForm.ShippedDate.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-3">
                                        <!-- <md-input-container class="md-block">
                                            <label>Removal Time</label>
                                            <input type="time" name="RemovalTime" />
                                        </md-input-container> -->

                                        <md-input-container class="md-block">
                                            <label>Removal Time</label>
                                            <input type="time" required name="ShippedTime" ng-model="CurrentShipment.ShippedTime" >
                                            <div ng-messages="shipmentForm.ShippedTime.$error" multiple ng-if='shipmentForm.ShippedTime.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Escort Login</label>
                                            <input type="text" required name="EscortLogin" ng-model="CurrentShipment.EscortLogin" ng-maxlength="100" >
                                            <div ng-messages="shipmentForm.EscortLogin.$error" multiple ng-if='shipmentForm.EscortLogin.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="col-md-12 btns-row">
                                    <button class="md-button md-raised btn-w-md md-default" ng-click="CurrentShipment = {}">
                                        Cancel
                                    </button>                                            
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                        data-ng-disabled="shipmentForm.$invalid || CurrentShipment.busy" ng-click="ShipShipment()">
                                        <span ng-show="! CurrentShipment.busy">Ship</span>
                                        <span ng-show="CurrentShipment.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </div>
                            </div>
                        </form>

                    </div>                    


                </md-card>                


            </article>
        </div>
    </div>
</div>