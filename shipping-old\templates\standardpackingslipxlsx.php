<?php
session_start();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$ShippingID = $_GET['ShippingID'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "Standard Packing Slip"." - ".$ShippingID.".xlsx" ;
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();

$sqlship = "Select s.ShippingID,d.disposition,v.VendorName,F.WeightUnit,df.FacilityName as DestinationFacilityName from shipping s 
            left join disposition d on s.disposition_id = d.disposition_id
            left join vendor v on s.VendorID = v.VendorID
            left join facility F on F.FacilityID = s.FacilityID
            left join facility df on s.DestinationFacilityID = df.FacilityID 
            where ShippingID = '".$ShippingID."'";
$queryship = mysqli_query($connectionlink1,$sqlship);
$rowship = mysqli_fetch_assoc($queryship);

$shipingID = array('Ticket/Shipment/Booking ID',$rowship['ShippingID']);
$shipingType = array('Shipment Type',$rowship['disposition']);
$Destination = array('Destination',$rowship['VendorName']);
$DestinationFacility = array('Destination Facility',$rowship['DestinationFacilityName']);
$Carrier = array('Carrier','');
$TrailerID = array('Trailer ID','');
$TrailerSerial = array('Trailer Seals','');
$sql = "Select sc.ShippingContainerID,sc.SealID,sc.ContainerNotes,sc.ContainerWeight,sc.disposition_id,sc.ReferenceID,sc.ReferenceType FROM shipping_containers sc 
 WHERE sc.ShippingID = '".$ShippingID."'";
$query = mysqli_query($connectionlink1,$sql);
$i = 1;
while($row = mysqli_fetch_assoc($query))
{
    $parttype = '';
    $sqlcount = "Select count(*) as assetcount from shipping_container_serials where ShippingContainerID = '".$row['ShippingContainerID']."'";
    $querycount = mysqli_query($connectionlink1,$sqlcount);
    $rowcount = mysqli_fetch_assoc($querycount);
    $sqldisposition = "Select disposition from disposition where disposition_id = '".$row['disposition_id']."'";
    $querydisposition = mysqli_query($connectionlink1,$sqldisposition);
    while($rowdisposition = mysqli_fetch_assoc($querydisposition))
    {
        $disposition = $rowdisposition['disposition'];
    }
    $sqlparttype = "Select distinct(part_type) as part_type from shipping_container_serials where
    ShippingContainerID = '".$row['ShippingContainerID']."'";
    $queryparttype = mysqli_query($connectionlink1,$sqlparttype);
    while($rowparttype = mysqli_fetch_assoc($queryparttype))
    {
        $parttype = $parttype.$rowparttype['part_type'].",";
    }
    //$row['ContainerWeight'] = $row['ContainerWeight']." ".$rowship['WeightUnit'];
    $parttype = substr($parttype, 0, -1);
    $row2  = array($i,$row['ShippingContainerID'],$row['SealID'],$parttype,$rowcount['assetcount'],$row['ContainerWeight'],$row['ContainerNotes'],$disposition,$row['ReferenceType'],$row['ReferenceID']);
    $rows[] = $row2;
    $i = $i+1;
}
$header = array('#','Container ID','Seal ID','Description','Qty','Container Weight ('.$rowship['WeightUnit'].')','Comments','Removal Type','Reference Type','Reference ID');
$sheet_name = 'Standard Packing Slip';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->writeSheetRow($sheet_name, $shipingID, $style1);
$writer->writeSheetRow($sheet_name, $shipingType, $style1);
$writer->writeSheetRow($sheet_name, $Destination, $style1);
$writer->writeSheetRow($sheet_name, $DestinationFacility, $style1);
$writer->writeSheetRow($sheet_name, $Carrier, $style1);
$writer->writeSheetRow($sheet_name, $TrailerID, $style1);
$writer->writeSheetRow($sheet_name, $TrailerSerial, $style1);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 