<?php
/**
 * Fix Converted Bins Data Script
 * This script fixes missing custompallet_items and updates AssetsCount for converted bins
 * Run this after the main conversion script to ensure data consistency
 */

// Database connection
session_start();
include_once("../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
// Configuration
$BATCH_SIZE = 500;  // Process in smaller batches
$systemUserId = 712; // System user ID for tracking

echo "=== FIX CONVERTED BINS DATA SCRIPT ===\n";
echo "This script will fix missing custompallet_items and update AssetsCount\n";
echo "Batch size: $BATCH_SIZE\n\n";

// Step 1: Fix missing custompallet_items for active shipments
function fixMissingCustomPalletItems($connectionlink, $systemUserId, $batchSize) {
    echo "\n=== STEP 1: Creating Missing CustomPallet Items (Active Shipments) ===\n";
    
    // Get total missing items to create
    $totalResult = mysqli_query($connectionlink, "
        SELECT COUNT(DISTINCT scs.SerialID) as total
        FROM shipping_container_serials scs
        JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping_containers sc ON sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
          AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)
          AND NOT EXISTS (
              SELECT 1 FROM custompallet_items cpi 
              WHERE cpi.CustomPalletID = cp.CustomPalletID 
                AND (
                    (scs.AssetScanID IS NOT NULL AND cpi.AssetScanID = scs.AssetScanID) OR
                    (scs.ServerID IS NOT NULL AND cpi.ServerID = scs.ServerID) OR
                    (scs.MediaID IS NOT NULL AND cpi.MediaID = scs.MediaID)
                )
          )
    ");
    
    if (!$totalResult) {
        die("✗ Error getting total missing items: " . mysqli_error($connectionlink) . "\n");
    }
    
    $totalRow = mysqli_fetch_assoc($totalResult);
    $totalMissingItems = $totalRow['total'];
    
    echo "Total missing custompallet_items to create: $totalMissingItems\n";
    
    if ($totalMissingItems == 0) {
        echo "✓ No missing custompallet_items found\n";
        return;
    }
    
    $created = 0;
    $offset = 0;
    
    while ($created < $totalMissingItems) {
        echo "Creating missing items batch: " . ($created + 1) . " to " . min($created + $batchSize, $totalMissingItems) . "\n";
        
        $sql = "INSERT IGNORE INTO custompallet_items (
            CustomPalletID, AssetScanID, DateCreated, CreatedBy, status, ServerID, MediaID, Quantity
        )
        SELECT DISTINCT
            cp.CustomPalletID, 
            scs.AssetScanID, 
            COALESCE(scs.CreatedDate, NOW()) as DateCreated,
            COALESCE(scs.CreatedBy, $systemUserId) as CreatedBy, 
            COALESCE(scs.StatusID, 8) as status, 
            scs.ServerID, 
            scs.MediaID, 
            COALESCE(scs.Quantity, 1) as Quantity
        FROM shipping_container_serials scs
        JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping_containers sc ON sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
          AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)
          AND NOT EXISTS (
              SELECT 1 FROM custompallet_items cpi 
              WHERE cpi.CustomPalletID = cp.CustomPalletID 
                AND (
                    (scs.AssetScanID IS NOT NULL AND cpi.AssetScanID = scs.AssetScanID) OR
                    (scs.ServerID IS NOT NULL AND cpi.ServerID = scs.ServerID) OR
                    (scs.MediaID IS NOT NULL AND cpi.MediaID = scs.MediaID)
                )
          )
        LIMIT $batchSize";
        
        if (mysqli_query($connectionlink, $sql)) {
            $batchCreated = mysqli_affected_rows($connectionlink);
            $created += $batchCreated;
            echo "✓ Created $batchCreated missing items (Total: $created)\n";
            
            if ($batchCreated == 0) {
                break; // No more items to create
            }
        } else {
            die("✗ Error creating missing items: " . mysqli_error($connectionlink) . "\n");
        }
        
        // Small delay to prevent overwhelming the database
        usleep(50000); // 0.05 second delay
    }
    
    echo "✓ Total missing custompallet_items created: $created\n";
}

// Step 2: Update AssetsCount for all converted bins
function updateAssetsCount($connectionlink, $systemUserId) {
    echo "\n=== STEP 2: Updating AssetsCount for Converted Bins ===\n";
    
    // Get total bins that need AssetsCount update
    $totalResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as total
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
    ");
    
    if (!$totalResult) {
        die("✗ Error getting total bins for AssetsCount update: " . mysqli_error($connectionlink) . "\n");
    }
    
    $totalRow = mysqli_fetch_assoc($totalResult);
    $totalBins = $totalRow['total'];
    
    echo "Total converted bins to update AssetsCount: $totalBins\n";
    
    if ($totalBins == 0) {
        echo "✓ No converted bins found\n";
        return;
    }
    
    // Update AssetsCount based on actual custompallet_items count
    $sql = "UPDATE custompallet cp
            JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
            JOIN shipping s ON sc.ShippingID = s.ShippingID
            SET cp.AssetsCount = (
                SELECT COUNT(*) 
                FROM custompallet_items cpi 
                WHERE cpi.CustomPalletID = cp.CustomPalletID
            ),
            cp.LastModifiedDate = NOW(),
            cp.LastModifiedBy = $systemUserId
            WHERE s.ShipmentStatusID = 1
              AND cp.ConvertedFromShippingContainer = 1";
    
    if (mysqli_query($connectionlink, $sql)) {
        $updated = mysqli_affected_rows($connectionlink);
        echo "✓ Updated AssetsCount for $updated bins\n";
    } else {
        die("✗ Error updating AssetsCount: " . mysqli_error($connectionlink) . "\n");
    }
}

// Step 3: Validation and reporting
function generateValidationReport($connectionlink) {
    echo "\n=== STEP 3: Validation Report ===\n";
    
    // Check bins with mismatched AssetsCount
    $mismatchResult = mysqli_query($connectionlink, "
        SELECT 
            cp.CustomPalletID,
            cp.BinName,
            cp.AssetsCount as Recorded_Count,
            (SELECT COUNT(*) FROM custompallet_items cpi WHERE cpi.CustomPalletID = cp.CustomPalletID) as Actual_Count
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
          AND cp.AssetsCount != (SELECT COUNT(*) FROM custompallet_items cpi WHERE cpi.CustomPalletID = cp.CustomPalletID)
        LIMIT 10
    ");
    
    if (mysqli_affected_rows($connectionlink) > 0) {
        echo "⚠ Found bins with mismatched AssetsCount:\n";
        while ($row = mysqli_fetch_assoc($mismatchResult)) {
            echo "  - Bin: {$row['BinName']}, Recorded: {$row['Recorded_Count']}, Actual: {$row['Actual_Count']}\n";
        }
    } else {
        echo "✓ All bins have correct AssetsCount\n";
    }
    
    // Summary statistics
    $summaryResult = mysqli_query($connectionlink, "
        SELECT 
            COUNT(*) as Total_Converted_Bins,
            SUM(cp.AssetsCount) as Total_Assets,
            AVG(cp.AssetsCount) as Avg_Assets_Per_Bin,
            MAX(cp.AssetsCount) as Max_Assets_In_Bin
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
    ");
    
    if ($summaryRow = mysqli_fetch_assoc($summaryResult)) {
        echo "\nSUMMARY STATISTICS:\n";
        echo "- Total Converted Bins: " . $summaryRow['Total_Converted_Bins'] . "\n";
        echo "- Total Assets: " . $summaryRow['Total_Assets'] . "\n";
        echo "- Average Assets per Bin: " . round($summaryRow['Avg_Assets_Per_Bin'], 2) . "\n";
        echo "- Maximum Assets in a Bin: " . $summaryRow['Max_Assets_In_Bin'] . "\n";
    }
}

// Main execution
try {
    // Step 1: Fix missing custompallet_items
    fixMissingCustomPalletItems($connectionlink, $systemUserId, $BATCH_SIZE);
    
    // Step 2: Update AssetsCount
    updateAssetsCount($connectionlink, $systemUserId);
    
    // Step 3: Generate validation report
    generateValidationReport($connectionlink);
    
    echo "\n✓ Data fix completed successfully!\n";
    echo "All converted bins should now have correct custompallet_items and AssetsCount.\n";
    
} catch (Exception $e) {
    echo "\n✗ Error during data fix: " . $e->getMessage() . "\n";
    exit(1);
}

?>
