<?php
session_start();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$data = $_SESSION['shipmentremovaldata'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "RemovedShipments.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Removed Shipments');
$sql = "select s.*,GROUP_CONCAT(DISTINCT d.disposition ORDER BY d.disposition ASC SEPARATOR ', ') AS disposition,v.VendorName,v.ContactName,f.FacilityName,df.FacilityName as DestinationFacilityName from shipping s  
			left join shipping_containers sc on s.ShippingID = sc.ShippingID 
			left join disposition d on sc.disposition_id = d.disposition_id 
			left join vendor v on s.VendorID = v.VendorID 
			left join facility f on s.FacilityID = f.FacilityID 
			left join facility df on s.DestinationFacilityID = df.FacilityID
			where ShipmentStatusID != '1' ";
			if(count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'ShippingID') {
							$sql = $sql . " AND s.ShippingID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
						}
						if($key == 'disposition') {
							$sql = $sql . " AND d.disposition like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
						}
						if($key == 'VendorName') {
							$sql = $sql . " AND v.VendorName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
						}
						if($key == 'ContactName') {
							$sql = $sql . " AND v.ContactName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
						}
						if($key == 'ApprovedDate') {
							$sql = $sql . " AND s.ApprovedDate like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
						}
						if($key == 'ShippedDate') {
							$sql = $sql . " AND s.ShippedDate like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$sql = $sql . " AND f.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
						}
						if($key == 'DestinationFacilityName') {
							$sql = $sql . " AND df.FacilityName like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
						}
					}
				}
			}
			$sql = $sql. " group by s.ShippingID ";
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				if($data['OrderBy'] == 'ShippingID') {
					$sql = $sql . " order by s.ShippingID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$sql = $sql . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'VendorName') {
					$sql = $sql . " order by v.VendorName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ContactName') {
					$sql = $sql . " order by v.ContactName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ApprovedDate') {
					$sql = $sql . " order by s.ApprovedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ShippedDate') {
					$sql = $sql . " order by s.ShippedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$sql = $sql . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'DestinationFacilityName') {
					$sql = $sql . " order by df.FacilityName ".$order_by_type." ";
				}
			} else {
				$sql = $sql . " order by s.CreatedDate desc ";
			}
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
while($row = mysqli_fetch_assoc($query))
{
    $row2  = array($row['ShippingID'],$row['disposition'],$row['FacilityName'],$row['VendorName'],$row['DestinationFacilityName'],$row['ContactName'],$row['ApprovedDate'],$row['ShippedDate']);
    $rows[] = $row2;
}
$header = array('Ticket ID','Removal Type','Facility','Destination','Destination Facility','Destination POC','Approved Date','Removal Date');
$sheet_name = 'Removed Shipments';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 