<?php
session_start();
include_once("shipping.class.php");
class PendingOutboundShipments extends ShippingClass {
    
    public function GetPendingOutboundContainers($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No Data'
			);
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}

			$query = "select s.*,ss.ShippingContainerID,ss.ASNContainer,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,g.GroupName,g.<PERSON>ame as `group`,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PalletID,ss.PartTypeSummary,ss.RemovalCode,ss.RecentSealDate,ss.ReferenceID,ss.ReferenceType,ss.BatchRecovery from shipping_containers ss 
            left join shipping s  on ss.ShippingID = s.ShippingID  
			left join disposition d on ss.disposition_id = d.disposition_id 
			left join vendor v on s.VendorID = v.VendorID 
			left join facility f on ss.FacilityID = f.FacilityID 
            left join package p on ss.idPackage = p.idPackage  
            left join location l on ss.LocationID = l.LocationID  
			LEFT JOIN location_group g on l.GroupID = g.GroupID 
            left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
			where ss.StatusID != '3' and ss.FacilityID = '".$_SESSION['user']['FacilityID']."' ";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'ShippingID') {
							$query = $query . " AND s.ShippingID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'ShippingContainerID') {
							$query = $query . " AND ss.ShippingContainerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'packageName') {
							$query = $query . " AND p.packageName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'SealID') {
							$query = $query . " AND ss.SealID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'ContainerWeight') {
							$query = $query . " AND ss.ContainerWeight like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'LocationName') {
							$query = $query . " AND l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'Status') {
							$query = $query . " AND st.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'VendorName') {
							$query = $query . " AND v.VendorName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

                        if($key == 'ContainerNotes') {
							$query = $query . " AND ss.ContainerNotes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'ContactName') {
							$query = $query . " AND v.ContactName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ApprovedDate') {
							$query = $query . " AND s.ApprovedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ShippedDate') {
							$query = $query . " AND s.ShippedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'ShippingNotes') {
							$query = $query . " AND ss.ShippingNotes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'PalletID') {
							$query = $query . " AND ss.PalletID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'PartTypeSummary') {
							$query = $query . " AND ss.PartTypeSummary like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'RemovalCode') {
							$query = $query . " AND ss.RemovalCode like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'GroupName') {
							$query = $query . " AND g.GroupName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ReferenceID') {
							$query = $query . " AND ss.ReferenceID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ReferenceType') {
							$query = $query . " AND ss.ReferenceType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}

			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				if($data['OrderBy'] == 'ShippingID') {
					$query = $query . " order by s.ShippingID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ShippingContainerID') {
					$query = $query . " order by ss.ShippingContainerID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'packageName') {
					$query = $query . " order by p.packageName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SealID') {
					$query = $query . " order by ss.SealID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ContainerWeight') {
					$query = $query . " order by ss.ContainerWeight ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'VendorName') {
					$query = $query . " order by v.VendorName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'LocationName') {
					$query = $query . " order by l.LocationName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'Status') {
					$query = $query . " order by st.Status ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ContactName') {
					$query = $query . " order by v.ContactName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ApprovedDate') {
					$query = $query . " order by s.ApprovedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ShippedDate') {
					$query = $query . " order by s.ShippedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ContainerNotes') {
					$query = $query . " order by ss.ContainerNotes ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ShippingNotes') {
					$query = $query . " order by ss.ShippingNotes ".$order_by_type." ";
				} else if($data['OrderBy'] == 'PalletID') {
					$query = $query . " order by ss.PalletID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'PartTypeSummary') {
					$query = $query . " order by ss.PartTypeSummary ".$order_by_type." ";
				} else if($data['OrderBy'] == 'RemovalCode') {
					$query = $query . " order by ss.RemovalCode ".$order_by_type." ";
				} else if($data['OrderBy'] == 'GroupName') {
					$query = $query . " order by g.GroupName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ReferenceID') {
					$query = $query . " order by ss.ReferenceID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ReferenceType') {
					$query = $query . " order by ss.ReferenceType ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by s.CreatedDate desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink))	{
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$ApprovedDate = strtotime( $row['ApprovedDate'] );
					$row['ApprovedDate'] = date( 'M d, Y', $ApprovedDate );

					$ShippedDate = strtotime( $row['ShippedDate'] );
					$row['ShippedDate'] = date( 'M d, Y', $ShippedDate );

					$row['Containers'] = array();
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tickets Available";
			}
			
			if($data['skip'] == 0) {
                $query1 = "select count(*) from shipping_containers ss                 
                left join shipping s  on ss.ShippingID = s.ShippingID  
                left join disposition d on ss.disposition_id = d.disposition_id 
                left join vendor v on s.VendorID = v.VendorID 
                left join facility f on ss.FacilityID = f.FacilityID 
                left join package p on ss.idPackage = p.idPackage  
                left join location l on ss.LocationID = l.LocationID  
				LEFT JOIN location_group g on l.GroupID = g.GroupID 
                left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
                where ss.StatusID != '3' and ss.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							if($key == 'ShippingID') {
								$query1 = $query1 . " AND s.ShippingID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

                            if($key == 'ShippingContainerID') {
                                $query1 = $query1 . " AND ss.ShippingContainerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

                            if($key == 'packageName') {
                                $query1 = $query1 . " AND p.packageName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

                            if($key == 'SealID') {
                                $query1 = $query1 . " AND ss.SealID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
    
                            if($key == 'ContainerWeight') {
                                $query1 = $query1 . " AND ss.ContainerWeight like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

							if($key == 'disposition') {
								$query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'VendorName') {
								$query1 = $query1 . " AND v.VendorName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

                            if($key == 'LocationName') {
                                $query1 = $query1 . " AND l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

                            if($key == 'Status') {
                                $query1 = $query1 . " AND st.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

							if($key == 'ContactName') {
								$query1 = $query1 . " AND v.ContactName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ApprovedDate') {
								$query1 = $query1 . " AND s.ApprovedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ShippedDate') {
								$query1 = $query1 . " AND s.ShippedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'FacilityName') {
								$query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
                            
                            if($key == 'ContainerNotes') {
                                $query1 = $query1 . " AND ss.ContainerNotes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

							if($key == 'ShippingNotes') {
								$query1 = $query1 . " AND ss.ShippingNotes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'PalletID') {
								$query1 = $query1 . " AND ss.PalletID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'PartTypeSummary') {
								$query1 = $query1 . " AND ss.PartTypeSummary like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'RemovalCode') {
								$query1 = $query1 . " AND ss.RemovalCode like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'GroupName') {
								$query1 = $query1 . " AND g.GroupName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ReferenceID') {
								$query1 = $query1 . " AND ss.ReferenceID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ReferenceType') {
								$query1 = $query1 . " AND ss.ReferenceType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function AddContainerToShipment1 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}

			//start validating container
			$query = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['ShippingNotes'] == '') {
					$json['Success'] = false;		
					$json['Result'] = 'No Shipping notes available for the container';			
					return json_encode($json);	
				}
				$data['ShippingID'] = $row['ShippingNotes'];
				if($row['ShippingID'] != '') {
					$json['Success'] = false;		
					$json['Result'] = 'Container already added to Shipment '.$row['ShippingID'];			
					return json_encode($json);			
				}

                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;		
					$json['Result'] = 'Container Facility is different from User Facility';			
					return json_encode($json);
                }

				// if($row['StatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Container status is not Active';			
				// 	return json_encode($json);			
				// }
			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Container ID not in eViridis';			
				return json_encode($json);			
			}
			//end validating container

			//Start validate shipping
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}
				// if($row1['disposition_id'] != $row['disposition_id']) {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Shipment Removal Type is not matching with Container Removal Type';
				// 	return json_encode($json);
				// }

				//Start check if Shipment Vendor accepts container removal type

				$query13 = "select count(*) from vendor_removal_types m where m.VendorID = '".mysqli_real_escape_string($this->connectionlink,$row1['VendorID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$row['disposition_id'])."'";
				$q13 = mysqli_query($this->connectionlink,$query13);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row13 = mysqli_fetch_assoc($q13);
					if($row13['count(*)'] == '0') {
						$json['Success'] = false;
						$json['Result'] = 'Container Removal Type is not mapped to Shipment Destination';
						return json_encode($json);	
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid';
					return json_encode($json);
				}

				//End check if Shipment Vendor accepts container removal type

				// if($row1['FacilityID'] != $row['FacilityID']) {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Shipment Facility is Different from Container Facility';
				// 	return json_encode($json);
				// }

				$query2 = "update shipping_containers set ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q2 = mysqli_query($this->connectionlink,$query2);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				//Start get Container Details
				$query3 = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PartTypeSummary,ss.RemovalCode from shipping_containers ss 
				left join shipping s  on ss.ShippingID = s.ShippingID  
				left join disposition d on ss.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on ss.FacilityID = f.FacilityID 
				left join package p on ss.idPackage = p.idPackage  
				left join location l on ss.LocationID = l.LocationID  
				left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
				where ss.FacilityID = '".$_SESSION['user']['FacilityID']."' and ss.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					$json['Container'] = $row3;
				}
				//End get Container Details

				$json['Success'] = true;	
				$json['Result'] = 'Container added to Shipment';
				return json_encode($json);

			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Invalid Ticket ID';
				return json_encode($json);
			}

			//End validate shipping

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ClosePendingContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}

			//Start validate Controller
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['RemovalController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Removal Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Controller or Password";
				return json_encode($json);
			}
			//End validate Controller

			//Start get Shipping Disposition
				// $query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
				// $q1 = mysqli_query($this->connectionlink,$query1);	
				// if(mysqli_error($this->connectionlink)) {			
				// 	$json['Success'] = false;		
				// 	$json['Result'] = mysqli_error($this->connectionlink);			
				// 	return json_encode($json);			
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$shipping = mysqli_fetch_assoc($q1);
				// 	if($shipping['ShipmentStatusID'] != '1') {
				// 		$json['Success'] = false;		
				// 		$json['Result'] = 'Shipment Status is not active';			
				// 		return json_encode($json);
				// 	}				
				// } else {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Invalid Shipment';
				// 	return json_encode($json);		
				// }
			//End get Shiping Disposition

			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				
				if($container['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not active';			
					return json_encode($json);
				}


				//Start check for min weight
				$query4 = "select packageWeight from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$container['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					if($row4['packageWeight'] >= $data['ContainerWeight']) {
						$json['Success'] = false;			
						$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight

				$query2 = "update shipping_containers set StatusID = '6',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['NewSealID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Result'] = 'Container Closed';

				$this->UpdateShipmentContainerPartTypeSummary($data['ShippingContainerID']);

				//Start get Container Details
				$query3 = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PartTypeSummary,ss.RemovalCode from shipping_containers ss 
				left join shipping s  on ss.ShippingID = s.ShippingID  
				left join disposition d on ss.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on ss.FacilityID = f.FacilityID 
				left join package p on ss.idPackage = p.idPackage  
				left join location l on ss.LocationID = l.LocationID  
				left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
				where ss.FacilityID = '".$_SESSION['user']['FacilityID']."' and ss.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					$json['Container'] = $row3;
				}
				//End get Container Details


			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Shipment Container is Closed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function ReopenOutboundContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}
			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				
				if($container['StatusID'] != '6') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not Closed';			
					return json_encode($json);
				}
				
				$query2 = "update shipping_containers set StatusID = '1',SealID = NULL,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Result'] = 'Container Reopened';
				$this->ResetShipmentContainerPartTypeSummary($data['ShippingContainerID']);

				//Start get Container Details
				$query3 = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PartTypeSummary,ss.RemovalCode from shipping_containers ss 
				left join shipping s  on ss.ShippingID = s.ShippingID  
				left join disposition d on ss.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on ss.FacilityID = f.FacilityID 
				left join package p on ss.idPackage = p.idPackage  
				left join location l on ss.LocationID = l.LocationID  
				left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
				where ss.FacilityID = '".$_SESSION['user']['FacilityID']."' and ss.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					$json['Container'] = $row3;
				}
				//End get Container Details

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Shipment Container Reopened';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function UpdateShippingNotes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}
			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);

				$query2 = "update shipping_containers set ShippingNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingNotes'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Result'] = 'Shipping Notes Updated';

				//Start get Container Details
				$query3 = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PartTypeSummary,ss.RemovalCode from shipping_containers ss 
				left join shipping s  on ss.ShippingID = s.ShippingID  
				left join disposition d on ss.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on ss.FacilityID = f.FacilityID 
				left join package p on ss.idPackage = p.idPackage  
				left join location l on ss.LocationID = l.LocationID  
				left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
				where ss.FacilityID = '".$_SESSION['user']['FacilityID']."' and ss.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					$json['Container'] = $row3;
				}
				//End get Container Details

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Shipping Notes Updated';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function UpdateContainerLocation ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}
			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);


				$query20 = "select LocationID,Locked,LocationType from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['LocationName'])."'";							
				$q20 = mysqli_query($this->connectionlink,$query20);
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row20 = mysqli_fetch_assoc($q20);
					if($row20['Locked'] == '1') { 
						$json['Success'] = false;
						$json['Result'] = 'Location is Locked';
						return json_encode($json);
					}

					if($row20['LocationType'] != 'Outbound Storage') {
						$json['Success'] = false;
						$json['Result'] = 'Selected Location is not for Outbound Storage';
						return json_encode($json);
					}
					$data['NewLocationID'] = $row20['LocationID'];
					$LocationType = $row20['LocationType'];
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location';
					return json_encode($json);
				}

				if($data['NewLocationID'] != $container['LocationID']) {// Location changed
					$query2 = "update shipping_containers set LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}

					$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$container['LocationID']."'";
					$querylocold = mysqli_query($this->connectionlink,$sqllocold);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}

					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Shipment Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}

				} else {
					$json['Success'] = false;
					$json['Result'] = 'Location not changed';
					return json_encode($json);
				}

				$json['Result'] = 'Location Updated';

				//Start get Container Details
				$query3 = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PartTypeSummary,ss.RemovalCode from shipping_containers ss 
				left join shipping s  on ss.ShippingID = s.ShippingID  
				left join disposition d on ss.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on ss.FacilityID = f.FacilityID 
				left join package p on ss.idPackage = p.idPackage  
				left join location l on ss.LocationID = l.LocationID  
				left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
				where ss.FacilityID = '".$_SESSION['user']['FacilityID']."' and ss.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					$json['Container'] = $row3;
				}
				//End get Container Details

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Location Updated';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function RemovePalletFromOutboundContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}


			$query = "update shipping_containers set PalletID = NULL where  ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Container removed from Pallet';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function MoveContainerToPallet ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}

			$PalletID = $this->GetRandomPalletID();			
			$json['PalletID'] = $PalletID;			

			//Start updating PalletID to Shipment Container
			$query = "update shipping_containers set PalletID = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End updating PalletID to Shipment Container

			$json['Success'] = true;
			$json['Result'] = 'Pallet ID ('.$PalletID.') generated';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function DeleteOutboundContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}
			
			$query2 = "select count(*) from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and (AssetScanID > 0 or InventoryID > 0 or ServerID > 0 or MediaID > 0)";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row2 = mysqli_fetch_assoc($q2);
				if($row2['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Only containers with Byproducts can be removed';
					return json_encode($json);
				}

				$query8 = "delete from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q8 = mysqli_query($this->connectionlink,$query8);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container';
				return json_encode($json);
			}


			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				if($container['LocationID'] > 0) {
					$query11 = "update location set Locked='2',currentItemType='',currentItemID='' where LocationID = '".mysqli_real_escape_string($this->connectionlink,$container['LocationID'])."' ";
					$q11 = mysqli_query($this->connectionlink,$query11);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}	
				}

			}
			//End get shipping container details

			
			$query8 = "delete from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q8 = mysqli_query($this->connectionlink,$query8);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Shipment Container is removed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GenerateRemoveshipXLS($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$transaction = 'Shipping ---> Pending Outbound Containers';
		$description = 'Pending Outbound Shipments Exported';
		$this->RecordUserTransaction($transaction, $description);

		$_SESSION['RemoveshipXLS'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}


	public function MakeContainerActive ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}
			
			//Start updating PalletID to Shipment Container
			$query = "update shipping_containers set StatusID = '1',QuarantineToActiveAuditController = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End updating PalletID to Shipment Container


			//Start get Container Details
			$query3 = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PartTypeSummary,ss.RemovalCode from shipping_containers ss 
			left join shipping s  on ss.ShippingID = s.ShippingID  
			left join disposition d on ss.disposition_id = d.disposition_id 
			left join vendor v on s.VendorID = v.VendorID 
			left join facility f on ss.FacilityID = f.FacilityID 
			left join package p on ss.idPackage = p.idPackage  
			left join location l on ss.LocationID = l.LocationID  
			left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
			where ss.FacilityID = '".$_SESSION['user']['FacilityID']."' and ss.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row3 = mysqli_fetch_assoc($q3);
				$json['Container'] = $row3;
			}
			//End get Container Details

			$json['Success'] = true;
			$json['Result'] = 'Container moved out of Quarantine';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function UpdateContainerLocationGroup ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Outbound Containers Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Outbound Containers')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Pending Outbound Containers';
				return json_encode($json);
			}
			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);

				if($data['group'] != $data['GroupName'])	{ // Location Changed
					if($data['group'] != '' && $data['group'] != null && $data['group'] != 'group') {
	
						//Start check if valid Group
						$query10 = "select GroupID,LocationType from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
						$q10 = mysqli_query($this->connectionlink,$query10);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row10 = mysqli_fetch_assoc($q10);
							if($row10['LocationType'] != 'Outbound Storage') {
								$json['Success'] = false;
								$json['Result'] = 'Only Outbound Storage Location groups are allowed';
								return json_encode($json);
							}
							$data['GroupID'] = $row10['GroupID'];
						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid Location Group';
							return json_encode($json);
						}
						//End check if valid Group
	
						//Start get free location from group selected
						$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$data['GroupID'])."'";
						$q112 = mysqli_query($this->connectionlink,$query112);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row112 = mysqli_fetch_assoc($q112);
							$data['NewLocationID'] = $row112['LocationID'];
							$newLocationName = $row112['LocationName'];
						} else {
							$json['Success'] = false;
							$json['Result'] = 'No locations available, in selected group';
							return json_encode($json);
						}
						//End get free location from group selected							
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Nothing Changed';
					return json_encode($json);
				}

				if($data['NewLocationID'] != $container['LocationID']) {// Location changed
					$query2 = "update shipping_containers set LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}

					$sqllocold = "UPDATE `location` SET `Locked` = '2', `currentItemType` = '', `currentItemID` = '' WHERE `LocationID` = '".$container['LocationID']."'";
					$querylocold = mysqli_query($this->connectionlink,$sqllocold);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}

					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Shipment Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['NewLocationID'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}

				} else {
					$json['Success'] = false;
					$json['Result'] = 'Nothing Changed';
					return json_encode($json);
				}

				$json['Result'] = 'Location Updated';

				//Start get Container Details
				$query3 = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,g.GroupName,g.GroupName as `group`,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PartTypeSummary,ss.RemovalCode from shipping_containers ss 
				left join shipping s  on ss.ShippingID = s.ShippingID  
				left join disposition d on ss.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on ss.FacilityID = f.FacilityID 
				left join package p on ss.idPackage = p.idPackage  
				left join location l on ss.LocationID = l.LocationID  
				LEFT JOIN location_group g on l.GroupID = g.GroupID 
				left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
				where ss.FacilityID = '".$_SESSION['user']['FacilityID']."' and ss.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					$json['Container'] = $row3;
				}
				//End get Container Details

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Location Updated';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>