<?php
	session_start();
	include_once("../database/inter_facility_transfer.class.php");
	$obj = new InterFacilityTransfer();
	
	if($_POST['ajax'] == "GetInboundContainerDetails") {
		$result = $obj->GetInboundContainerDetails($_POST);
		echo $result;
	}    

    if($_POST['ajax'] == "ConvertInboundToOutboundContainer") {
		$result = $obj->ConvertInboundToOutboundContainer($_POST);
		echo $result;
	}
?>