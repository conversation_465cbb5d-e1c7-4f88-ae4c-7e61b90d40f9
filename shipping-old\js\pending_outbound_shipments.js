(function () {
    'use strict';

    angular.module('app').controller("pending_outbound_shipments", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pending Outbound Containers',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.Shipments = [];
        $scope.CurrentShipment = {};
        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];

        $scope.GetHoursDifference = function (date) {

            var duration = moment.duration(moment(new Date()).diff(date));
            var days = duration.asDays();
            return days;

            var hours = duration.asHours();
            return hours;

            return moment.duration(end.diff(date)).asHours();
            return moment(date).hours();
            moment().hours()
            return date;
        };


        $scope.RecordUserNavigationTransaction = function (TransactionType,Description,PageURL,id) {

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserNavigationTransaction&TransactionType=' + TransactionType + '&Description=' + Description + '&PageURL=' + PageURL+id,
                success: function (data) {
                    
                    if (data.Success) {                        
                    } else {                        
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {                    
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.EditContainer = function (container) {            
            if($scope.filter_text[0].ShippingContainerID != '' && $scope.filter_text[0].ShippingContainerID) {
    
                if($scope.filter_text[0].ShippingContainerID == container.ShippingContainerID) {
                    if(container.ShippingID) {
                        $scope.RecordUserNavigationTransaction('Shipping ---> Pending Outbound Containers','Hit on Edit Button','shipping/#!/ShipmentPrep/',container.ShippingID);
                        window.location = '#!/ShipmentPrep/'+container.ShippingID;
                    } else {
                        $scope.RecordUserNavigationTransaction('Shipping ---> Pending Outbound Containers','Hit on Edit Button','shipping/#!/ShipmentContainer/',container.ShippingContainerID);
                        window.location = '#!/ShipmentContainer/'+container.ShippingContainerID;
                    }
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Container ID is different from Processed Container ID')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("ShippingContainerID").focus();
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Container ID in Container ID Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("ShippingContainerID").focus();
            }
        };

        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPendingOutboundContainers&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.Shipments = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPendingOutboundContainers&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.Shipments = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        $scope.RemoveshipXLS = function () {
            //alert("1");
            jQuery.ajax({
                url: host+'shipping/includes/pending_outbound_shipments_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateRemoveshipXLS&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),

                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        //console.log(data.Result);
                        window.location="templates/RemoveshipXLS.php";
                    } else {
                    // alert("4");
                    $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        //End Pagination Logic


        $scope.MoveContainer = function (ship,ev) {
            if(ship.MoveToShipment != '') {    
                
                if($scope.filter_text[0].ShippingContainerID != '' && $scope.filter_text[0].ShippingContainerID) {

                    if($scope.filter_text[0].ShippingContainerID == ship.ShippingContainerID) {
                        var confirm = $mdDialog.confirm()
                            .title('Are you sure, You want to add Container to Shipment?')
                            .content('')
                            .ariaLabel('Lucky day')
                            .targetEvent(ev)
                            .ok('ADD')
                            .cancel('Cancel');
                        $mdDialog.show(confirm).then(function () {
        
                            //if(ship.MoveToShipment == ship.ShippingNotes) {
                            if(true) {
        
                                $scope.CurrentShipment = {};
                                $rootScope.$broadcast('preloader:active');
                                ship.busy = true;
                                jQuery.ajax({                        
                                    url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                                    dataType: 'json',
                                    type: 'post',
                                    data: 'ajax=AddContainerToShipment1&ShippingID=' + ship.MoveToShipment+'&ShippingContainerID='+ship.ShippingContainerID,
                                    success: function (data) {
                                        ship.busy = false;
                                        $rootScope.$broadcast('preloader:hide');
                                        if (data.Success) {                                
                                            $mdToast.show(
                                                $mdToast.simple()
                                                    .content(data.Result)
                                                    .action('OK')
                                                    .position('right')
                                                    .hideDelay(0)
                                                    .toastClass('md-toast-success md-block')
                                            );   
                                            ship.ShippingID = ship.MoveToShipment;
                                            ship.MoveToShipment = '';
                                            if(data.Container) {
                                                for(var i=0;i<$scope.Shipments.length;i++) {
                                                    if($scope.Shipments[i]['ShippingContainerID'] == data.Container.ShippingContainerID) {
                                                        $scope.Shipments[i] = data.Container;
                                                    }
                                                }
                                                //container = data.Container;
                                            }                                    
                                        } else {
                                            $mdToast.show(
                                                $mdToast.simple()
                                                    .content(data.Result)
                                                    .action('OK')
                                                    .position('right')
                                                    .hideDelay(0)
                                                    .toastClass('md-toast-danger md-block')
                                            );
                                        }                            
                                        initSessionTime(); $scope.$apply();;
                                    }, error: function (data) {
                                        ship.busy = false;
                                        $rootScope.$broadcast('preloader:hide');
                                        initSessionTime(); $scope.$apply();;
                                    }
                                });
        
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Shipping notes and Shipping ID should be same.')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
        
                        }, function () {
        
                        });
                    } else {

                        $mdToast.show (
                            $mdToast.simple()
                            .content('Searched Container ID is different from Processed Container ID')                    
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                        );
                        $window.document.getElementById("ShippingContainerID").focus();

                    }
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Enter Container ID in Container ID Filter')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("ShippingContainerID").focus();

                }
                
                
            }
        };



        function CloseContainerTPVRController($scope,$mdDialog,CurrentContainer,$mdToast,$window) {
            $scope.CurrentContainer = CurrentContainer;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };   
            
            $scope.FocusNextField = function (nextid,wait) {            
                if(wait == '1') {
                    setTimeout(function () {
                        $window.document.getElementById(nextid).focus();
                    }, 100);
                } else {
                    $window.document.getElementById(nextid).focus();
                }
            };
            
        }

        $scope.CurrentContainer = {};
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();            
        }

        $scope.CloseContainer = function (container, ev) {

            if($scope.filter_text[0].ShippingContainerID != '' && $scope.filter_text[0].ShippingContainerID) {

                if($scope.filter_text[0].ShippingContainerID == container.ShippingContainerID) {


                    $mdDialog.show({
                        controller: CloseContainerTPVRController,
                        templateUrl: 'password.html',
                        parent: angular.element(document.body),
                        targetEvent: ev,
                        onComplete: afterShowAnimation,
                        clickOutsideToClose:true,
                        resolve: {
                            CurrentContainer: function () {
                                return container;
                            }
                        }
                    })
                    .then(function(confirmDetails) {
                        
                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                            dataType: 'json',
                            type: 'post',
                            //data: 'ajax=CloseContainer&' + $.param(container) + '&ShippingID=' + $scope.shipping.ShippingID+'&'+$.param(confirmDetails),
                            data: 'ajax=ClosePendingContainer&' + $.param(container) + '&'+$.param(confirmDetails),
                            success: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );    
                                    if(data.Container) {
                                        for(var i=0;i<$scope.Shipments.length;i++) {
                                            if($scope.Shipments[i]['ShippingContainerID'] == data.Container.ShippingContainerID) {
                                                $scope.Shipments[i] = data.Container;
                                            }
                                        }
                                        //container = data.Container;
                                    }                            
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                }
                                initSessionTime(); $scope.$apply();;
                            }, error: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();;
                            }
                        });
                        
                        
                    }, function(confirmDetails) {
                        $scope.confirmDetails = confirmDetails;
                    });

                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Container ID is different from Processed Container ID')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("ShippingContainerID").focus();
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Container ID in Container ID Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("ShippingContainerID").focus();
            }

        };


        $scope.ReopenContainer = function (container, ev) {

            if($scope.filter_text[0].ShippingContainerID != '' && $scope.filter_text[0].ShippingContainerID) {

                if($scope.filter_text[0].ShippingContainerID == container.ShippingContainerID) {
            
                    var confirm = $mdDialog.confirm()
                        .title('Are you sure, You want to Reopen the container ('+container.ShippingContainerID+') ?')
                        .content('')
                        .ariaLabel('Lucky day')
                        .targetEvent(ev)
                        .ok('Reopen')
                        .cancel('Cancel');
                    $mdDialog.show(confirm).then(function () {
                        $scope.CurrentShipment = {};
                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=ReopenOutboundContainer&'+$.param(container),
                            success: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {
                                    $scope.SearchContainer = '';
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );
                                    
                                    if(data.Container) {
                                        for(var i=0;i<$scope.Shipments.length;i++) {
                                            if($scope.Shipments[i]['ShippingContainerID'] == data.Container.ShippingContainerID) {
                                                $scope.Shipments[i] = data.Container;
                                            }
                                        }                                
                                    } 
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                }
                                initSessionTime(); $scope.$apply();;
                            }, error: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();;
                            }
                        });

                    }, function () {

                    });

                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Container ID is different from Processed Container ID')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("ShippingContainerID").focus();
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Container ID in Container ID Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("ShippingContainerID").focus();
            }

        };


        $scope.UpdateShippingNotes = function (container, ev) {
            
            $rootScope.$broadcast('preloader:active');
            container.busy = true;
            jQuery.ajax({
                url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=UpdateShippingNotes&'+$.param(container),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    container.busy = false;
                    if (data.Success) {
                        $scope.SearchContainer = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        
                        if(data.Container) {
                            for(var i=0;i<$scope.Shipments.length;i++) {
                                if($scope.Shipments[i]['ShippingContainerID'] == data.Container.ShippingContainerID) {
                                    $scope.Shipments[i] = data.Container;
                                }
                            }                                
                        } 
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    container.busy = false;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        //Start Smart Search controls for Receive Container List
        function ContainerLocationChange(text, container) {
            container.LocationName = text;
        }

        function selectedContainerLocationChange(item, container) {
            if (item) {
                if (item.value) {
                    container.LocationName = item.value;
                } else {
                    container.LocationName = '';
                }
            } else {
                container.LocationName = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryContainerLocationSearch = queryContainerLocationSearch;
        $scope.ContainerLocationChange = ContainerLocationChange;
        $scope.selectedContainerLocationChange = selectedContainerLocationChange;
        function queryContainerLocationSearch(query, container) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + container.FacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }
        //End Smart Search controls for Receive Container List


         //Start Location Group Smart Search controls for Receive Container List
        function ContainerLocationChange1(text, container) {
            container.group = text;
        }

        function selectedContainerLocationChange1(item, container) {
            if (item) {
                if (item.value) {
                    container.group = item.value;
                } else {
                    container.group = '';
                }
            } else {
                container.group = '';
            }
            console.log('Item changed to ' + JSON.stringify(item));
        }

        $scope.queryContainerLocationSearch1 = queryContainerLocationSearch1;
        $scope.ContainerLocationChange1 = ContainerLocationChange1;
        $scope.selectedContainerLocationChange1 = selectedContainerLocationChange1;
        function queryContainerLocationSearch1(query, container) {
            if (query) {
                if (query != '' && query != 'undefined') {                    
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + container.FacilityID+'&LocationType=Outbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }
        //End Location Group Smart Search controls for Receive Container List


        $scope.UpdateContainerLocation = function (container,ev) {

            if($scope.filter_text[0].ShippingContainerID != '' && $scope.filter_text[0].ShippingContainerID) {

                if($scope.filter_text[0].ShippingContainerID == container.ShippingContainerID) {
                    $rootScope.$broadcast('preloader:active');
                    container.busy = true;
                    jQuery.ajax({
                        url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateContainerLocation&'+$.param(container),
                        success: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            container.busy = false;
                            if (data.Success) {                        
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                
                                if(data.Container) {
                                    for(var i=0;i<$scope.Shipments.length;i++) {
                                        if($scope.Shipments[i]['ShippingContainerID'] == data.Container.ShippingContainerID) {
                                            $scope.Shipments[i] = data.Container;
                                        }
                                    }                                
                                } 
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            container.busy = false;
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Container ID is different from Processed Container ID')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("ShippingContainerID").focus();
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Container ID in Container ID Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("ShippingContainerID").focus();
            }
        };


        $scope.UpdateContainerLocationGroup = function (container,ev) {

            if($scope.filter_text[0].ShippingContainerID != '' && $scope.filter_text[0].ShippingContainerID) {

                if($scope.filter_text[0].ShippingContainerID == container.ShippingContainerID) {
                    $rootScope.$broadcast('preloader:active');
                    container.busy = true;
                    jQuery.ajax({
                        url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateContainerLocationGroup&'+$.param(container),
                        success: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            container.busy = false;
                            if (data.Success) {                        
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                
                                if(data.Container) {
                                    for(var i=0;i<$scope.Shipments.length;i++) {
                                        if($scope.Shipments[i]['ShippingContainerID'] == data.Container.ShippingContainerID) {
                                            $scope.Shipments[i] = data.Container;
                                        }
                                    }                                
                                } 
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            container.busy = false;
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Container ID is different from Processed Container ID')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("ShippingContainerID").focus();
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Container ID in Container ID Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("ShippingContainerID").focus();
            }
        };


        $scope.RemovePalletFromContainer = function (container, ev) {

            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to remove Container from Pallet ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Yes')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $scope.CurrentShipment = {};
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=RemovePalletFromOutboundContainer&ShippingContainerID=' + container.ShippingContainerID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            container.PalletID = '';
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });

        };

        $scope.MoveContainerToPallet = function (container,ev) {

            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to generate Pallet ID for Container '+container.ShippingContainerID+' ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Generate')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                container.busy = true;
                jQuery.ajax({
                    url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveContainerToPallet&ShippingContainerID=' + container.ShippingContainerID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        container.busy = false;
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            if (data.PalletID) {
                                container.PalletID = data.PalletID;                                
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        container.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });


        };


        $scope.DeleteContainer = function (container, ev) {

            if($scope.filter_text[0].ShippingContainerID != '' && $scope.filter_text[0].ShippingContainerID) {

                if($scope.filter_text[0].ShippingContainerID == container.ShippingContainerID) {
                    var confirm = $mdDialog.confirm()
                        .title('Are you sure, You want to Delete Container ?')
                        .content('')
                        .ariaLabel('Lucky day')
                        .targetEvent(ev)
                        .ok('Delete')
                        .cancel('Cancel');
                    $mdDialog.show(confirm).then(function () {
                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=DeleteOutboundContainer&' + $.param(container) ,
                            success: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );
                                    //$scope.CallServerFunction($scope.currentPage);
                                    location.reload();
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                }
                                initSessionTime(); $scope.$apply();;
                            }, error: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();;
                            }
                        });

                    }, function () {

                    });
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Container ID is different from Processed Container ID')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("ShippingContainerID").focus();
                }
            } else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Container ID in Container ID Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("ShippingContainerID").focus();
            }         
        };


        function QuarantineToActiveTPVRController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide1 = function() {
                $mdDialog.hide($scope.confirmDetails1);
            };
            $scope.cancel1 = function() {
                $mdDialog.cancel($scope.confirmDetails1);
            };

            $scope.FocusPasswordField = function () {
                $window.document.getElementById("Password1").focus();
            };
        }
        $scope.CurrentPallet = {};
        $scope.confirmDetails1 = {};
        function afterShowAnimation1 () {            
            $window.document.getElementById("AuditController1").focus();
        }
        $scope.ValidateQuarantineToActiveTPVRControllerPopup = function (pallet,ev) {            
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: QuarantineToActiveTPVRController,
                templateUrl: 'password1.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation1,
                clickOutsideToClose:true,
                resolve: {
                    CurrentPallet: function () {
                      return pallet;
                    }
                }
            })
            .then(function(confirmDetails1) {
                $rootScope.$broadcast('preloader:active');
                $scope.confirmDetails1 = confirmDetails1;
                jQuery.ajax({
                    url: host + 'receive/includes/receive_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=ValidateSanitizationController&UserName='+$scope.asset.sanitization_controller_login_id+'&Password='+$scope.confirmDetails.Password,
                    data: 'ajax=ValidateAuditController&'+$.param($scope.confirmDetails1),
                    success: function(data){
                        if(data.Success) {
                            $scope.confirmDetails1.PasswordVerified = true;
                            //$window.document.getElementById('scan_for_save').focus();
                            $scope.MakeContainerActive(pallet,ev);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.confirmDetails1.PasswordVerified = false;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.confirmDetails1.PasswordVerified = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function(confirmDetails1) {
                $scope.confirmDetails1 = confirmDetails1;
            });
        };



        $scope.MakeContainerActive = function (container,ev) {
            $rootScope.$broadcast('preloader:active');
            container.busy = true;
            jQuery.ajax({
                url: host + 'shipping/includes/pending_outbound_shipments_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=MakeContainerActive&' + $.param(container)+'&'+$.param($scope.confirmDetails1),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    container.busy = false;
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        if(data.Container) {
                            for(var i=0;i<$scope.Shipments.length;i++) {
                                if($scope.Shipments[i]['ShippingContainerID'] == data.Container.ShippingContainerID) {
                                    $scope.Shipments[i] = data.Container;
                                }
                            }                                
                        } 
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    container.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


    });
})();
