<div ng-controller="ArchiveBinsList" class="page">
    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">

                <style>
                    .icon_btn{padding: 0 !important;  margin: 0 !important;  line-height: 24px !important;  min-height: 24px !important; height: 24px !important;}
                    .table > thead > tr > th, .table > thead > tr > td, .table > tbody > tr > th, .table > tbody > tr > td, .table > tfoot > tr > th, .table > tfoot > tr > td{padding: 4px 8px 4px 0px !important;}
                </style>

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="ArchiveBinsList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">
                                 <i ng-click="ArchiveBinsList = !ArchiveBinsList" class="material-icons md-primary" ng-show="ArchiveBinsList">keyboard_arrow_up</i>
                                <i ng-click="ArchiveBinsList = !ArchiveBinsList" class="material-icons md-primary" ng-show="! ArchiveBinsList">keyboard_arrow_down</i>
                                <span ng-click="ArchiveBinsList = !ArchiveBinsList">Archive Bins List</span>
                                <div flex></div>

                                 <a href="#!/ArchiveBinsList" ng-click="ArchiveBinsListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>

                                <a href="#!/ArchiveBinsList" ng-click="ArchiveBinsListxls()" class="md-button md-raised md-default dis_open_v mr-5" style="display:none; min-width: 40px;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon>
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="ArchiveBinsList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>
                                    <div class="table-responsive" style="overflow: auto;">
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('BinName')" ng-class="{'orderby' : OrderBy == 'BinName'}">
                                                        <div>
                                                            Bin Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinName'"></i>
                                                            <span ng-show="OrderBy == 'BinName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ContainerType')" ng-class="{'orderby' : OrderBy == 'ContainerType'}">
                                                        <div>
                                                            Container Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ContainerType'"></i>
                                                            <span ng-show="OrderBy == 'ContainerType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="min-width: 120px;">
                                                        <div>
                                                            Actions
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div>
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">
                                                        <div>
                                                            Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>
                                                            <span ng-show="OrderBy == 'Description'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">
                                                        <div style="min-width: 80px;">
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                            <span ng-show="OrderBy == 'Status'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('MobilityName')" ng-class="{'orderby' : OrderBy == 'MobilityName'}">
                                                        <div>
                                                            Mobility Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'MobilityName'"></i>
                                                            <span ng-show="OrderBy == 'MobilityName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ReferenceType')" ng-class="{'orderby' : OrderBy == 'ReferenceType'}">
                                                        <div>
                                                            Reference Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ReferenceType'"></i>
                                                            <span ng-show="OrderBy == 'ReferenceType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ReferenceID')" ng-class="{'orderby' : OrderBy == 'ReferenceID'}">
                                                        <div>
                                                            Reference ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ReferenceID'"></i>
                                                            <span ng-show="OrderBy == 'ReferenceID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">
                                                        <div>
                                                            Created Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>
                                                            <span ng-show="OrderBy == 'CreatedDate'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DeletedDate')" ng-class="{'orderby' : OrderBy == 'DeletedDate'}">
                                                        <div>
                                                            Deleted Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DeletedDate'"></i>
                                                            <span ng-show="OrderBy == 'DeletedDate'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DeletedBy')" ng-class="{'orderby' : OrderBy == 'DeletedBy'}">
                                                        <div>
                                                            Deleted By <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DeletedBy'"></i>
                                                            <span ng-show="OrderBy == 'DeletedBy'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                </tr>

                                                <tr class="errornone">
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="BinName" ng-model="filter_text[0].BinName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ContainerType" ng-model="filter_text[0].ContainerType" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td></td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Notes" ng-model="filter_text[0].Description" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" placeholder="Filter by Notes" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="MobilityName" ng-model="filter_text[0].MobilityName" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ReferenceType" ng-model="filter_text[0].ReferenceType" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ReferenceID" ng-model="filter_text[0].ReferenceID" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DeletedDate" ng-model="filter_text[0].DeletedDate" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DeletedBy" ng-model="filter_text[0].DeletedBy" ng-change="MakeFilter()" ng-model-options='{ debounce: 1000 }' aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody>
                                                <tr ng-repeat="item in pagedItems" ng-class="{'danger': item.StatusID == '5'}">
                                                    <td>{{item.BinName}}</td>
                                                    <td>{{item.ContainerType}}</td>
                                                    <td>
                                                        <md-button class="md-raised md-primary" ng-click="ReActivateBin(item, $event)" style="min-width: 80px;">
                                                            ReActivate
                                                        </md-button>
                                                    </td>
                                                    <td>{{item.disposition}}</td>
                                                    <td>{{item.Description}}</td>
                                                    <td>{{item.Status}}</td>
                                                    <td>{{item.MobilityName}}</td>
                                                    <td>{{item.ReferenceType}}</td>
                                                    <td>{{item.ReferenceID}}</td>
                                                    <td>{{item.CreatedDate | date:'MM/dd/yyyy HH:mm:ss'}}</td>
                                                    <td>{{item.DeletedDate | date:'MM/dd/yyyy HH:mm:ss'}}</td>
                                                    <td>{{item.FirstName}} {{item.LastName}}</td>
                                                </tr>
                                                <tr ng-show="pagedItems.length == 0">
                                                    <td colspan="11" style="text-align: center; padding: 20px;">No archive bins found</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    <div ng-show="pagedItems.length > 0">
                                        <ul class="pagination">
                                            <li ng-class="prevPageDisabled()">
                                                <a href ng-click="firstPage()"><< First</a>
                                            </li>
                                            <li ng-class="prevPageDisabled()">
                                                <a href ng-click="prevPage()"><< Prev</a>
                                            </li>
                                            <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                <a style="cursor:pointer;">{{n+1}}</a>
                                            </li>
                                            <li ng-class="nextPageDisabled()">
                                                <a href ng-click="nextPage()">Next >></a>
                                            </li>
                                            <li ng-class="nextPageDisabled()">
                                                <a href ng-click="lastPage()">Last >></a>
                                            </li>
                                        </ul>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </md-card>

                </div>

            </article>
        </div>
    </div>
</div>

<!-- ReActivate Bin Modal -->
<script type="text/ng-template" id="reactivateBinModal.html">
    <div style="max-width:600px">
        <md-toolbar>
            <div class="md-toolbar-tools">
                <h2>ReActivate Bin: {{reactivateBinData.BinName}}</h2>
                <span flex></span>
                <md-button class="md-icon-button" ng-click="cancel()">
                    <md-icon class="material-icons">close</md-icon>
                </md-button>
            </div>
        </md-toolbar>

        <md-dialog-content>
            <div class="md-dialog-content">
                <div class="form-horizontal verification-form">
                    <form name="reactivateBinForm">
                        <div class="row">
                            <div class="col-md-12">
                                <md-input-container class="md-block">
                                    <label>Select WIP Location Group</label>
                                    <md-autocomplete required
                                        md-no-cache="noCache"
                                        md-search-text-change="LocationGroupChange(reactivateBinData.locationGroup)"
                                        md-search-text="reactivateBinData.locationGroup"
                                        md-items="item in queryLocationGroupSearch(reactivateBinData.locationGroup)"
                                        md-item-text="item.GroupName"
                                        md-selected-item-change="selectedLocationGroupChange(item)"
                                        md-min-length="0"
                                        ng-model-options='{ debounce: 1000 }'
                                        placeholder="Search WIP Location Group">
                                        <md-item-template>
                                            <span md-highlight-text="reactivateBinData.locationGroup" md-highlight-flags="^i">{{item.GroupName}}</span>
                                        </md-item-template>
                                        <md-not-found>
                                            No WIP Location Groups matching "{{reactivateBinData.locationGroup}}" were found.
                                        </md-not-found>
                                    </md-autocomplete>
                                    <div class="error-space">
                                        <div ng-messages="reactivateBinForm.locationGroup.$error" multiple ng-if='reactivateBinForm.locationGroup.$dirty'>
                                            <div ng-message="required">Location Group is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="col-md-12 text-center mb-10 mt-10">
                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md md-default" ng-click="cancel()">Cancel</button>
                    <button type="button" class="md-button md-raised btn-w-md md-primary" ng-click="confirmReActivateBin()" ng-disabled="!reactivateBinData.locationGroup || reactivateBinBusy">
                        <span ng-show="!reactivateBinBusy">ReActivate Bin</span>
                        <span ng-show="reactivateBinBusy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px"></md-progress-circular></span>
                    </button>
                </div>
            </div>
        </md-dialog-content>
    </div>
</script>