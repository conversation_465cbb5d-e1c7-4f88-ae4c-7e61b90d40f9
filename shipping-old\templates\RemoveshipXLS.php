<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['RemoveshipXLS'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "PendingOutboundContainers.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Pending Outbound Containers');
/*$header = array('Ticket ID','Pallet ID','Container ID','Container Type','Seal','Weight','Removal Type','Facility','Destination','Destination POC','Location','Status','Shipping Notes','Container Notes');*/

$header = array('Container ID','Ticket ID','Container Type','Part Type','Seal','Weight','Removal Type','Destination','Location','Status','Removal Code','Container Notes','Reference Type','Reference ID','Batch Recovery');

$sql = "select s.*,ss.ShippingContainerID,ss.SealID,ss.ContainerWeight,d.disposition,v.VendorName,v.ContactName,f.FacilityName,p.packageName,l.LocationName,st.Status,ss.ContainerNotes,ss.StatusID,ss.ShippingNotes,ss.PalletID,ss.PartTypeSummary,ss.RemovalCode,ss.ReferenceID,ss.ReferenceType,ss.BatchRecovery from shipping_containers ss 
            left join shipping s  on ss.ShippingID = s.ShippingID  
            left join disposition d on ss.disposition_id = d.disposition_id 
            left join vendor v on s.VendorID = v.VendorID 
            left join facility f on ss.FacilityID = f.FacilityID 
            left join package p on ss.idPackage = p.idPackage  
            left join location l on ss.LocationID = l.LocationID  
            left join shipping_status st on ss.StatusID = st.ShipmentStatusID 
            where ss.StatusID != '3' and ss.FacilityID = '".$_SESSION['user']['FacilityID']."' ";
if($data[0] && count($data[0]) > 0) {
            foreach ($data[0] as $key => $value) {
                if($value != '') {

                    if($key == 'ShippingID') {
                            $sql = $sql . " AND s.ShippingID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'ShippingContainerID') {
                            $sql = $sql . " AND ss.ShippingContainerID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'packageName') {
                            $sql = $sql . " AND p.packageName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'SealID') {
                            $sql = $sql . " AND ss.SealID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'ContainerWeight') {
                            $sql = $sql . " AND ss.ContainerWeight like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'disposition') {
                            $sql = $sql . " AND d.disposition like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'LocationName') {
                            $sql = $sql . " AND l.LocationName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'Status') {
                            $sql = $sql . " AND st.Status like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'VendorName') {
                            $sql = $sql . " AND v.VendorName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'ContainerNotes') {
                            $sql = $sql . " AND ss.ContainerNotes like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'ContactName') {
                            $sql = $sql . " AND v.ContactName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }
                        if($key == 'ApprovedDate') {
                            $sql = $sql . " AND s.ApprovedDate like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }
                        if($key == 'ShippedDate') {
                            $sql = $sql . " AND s.ShippedDate like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }
                        if($key == 'FacilityName') {
                            $sql = $sql . " AND f.FacilityName like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'ShippingNotes') {
                            $sql = $sql . " AND ss.ShippingNotes like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'PalletID') {
                            $sql = $sql . " AND ss.PalletID like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }
                        if($key == 'PartTypeSummary') {
                            $sql = $sql . " AND ss.PartTypeSummary like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }

                        if($key == 'RemovalCode') {
                            $sql = $sql . " AND ss.RemovalCode like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }
                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

                if($data['OrderBy'] == 'ShippingID') {
                    $sql = $sql . " order by s.ShippingID ".$order_by_type." ";
                } else if($data['OrderBy'] == 'ShippingContainerID') {
                    $sql = $sql . " order by ss.ShippingContainerID ".$order_by_type." ";
                } else if($data['OrderBy'] == 'packageName') {
                    $sql = $sql . " order by p.packageName ".$order_by_type." ";
                } else if($data['OrderBy'] == 'SealID') {
                    $sql = $sql . " order by ss.SealID ".$order_by_type." ";
                } else if($data['OrderBy'] == 'ContainerWeight') {
                    $sql = $sql . " order by ss.ContainerWeight ".$order_by_type." ";
                } else if($data['OrderBy'] == 'disposition') {
                    $sql = $sql . " order by d.disposition ".$order_by_type." ";
                } else if($data['OrderBy'] == 'VendorName') {
                    $sql = $sql . " order by v.VendorName ".$order_by_type." ";
                } else if($data['OrderBy'] == 'LocationName') {
                    $sql = $sql . " order by l.LocationName ".$order_by_type." ";
                } else if($data['OrderBy'] == 'Status') {
                    $sql = $sql . " order by st.Status ".$order_by_type." ";
                } else if($data['OrderBy'] == 'ContactName') {
                    $sql = $sql . " order by v.ContactName ".$order_by_type." ";
                } else if($data['OrderBy'] == 'ApprovedDate') {
                    $sql = $sql . " order by s.ApprovedDate ".$order_by_type." ";
                } else if($data['OrderBy'] == 'ShippedDate') {
                    $sql = $sql . " order by s.ShippedDate ".$order_by_type." ";
                } else if($data['OrderBy'] == 'FacilityName') {
                    $sql = $sql . " order by f.FacilityName ".$order_by_type." ";
                } else if($data['OrderBy'] == 'ContainerNotes') {
                    $sql = $sql . " order by ss.ContainerNotes ".$order_by_type." ";
                } else if($data['OrderBy'] == 'ShippingNotes') {
                    $sql = $sql . " order by ss.ShippingNotes ".$order_by_type." ";
                } else if($data['OrderBy'] == 'PalletID') {
                    $sql = $sql . " order by ss.PalletID ".$order_by_type." ";
                } else if($data['OrderBy'] == 'PartTypeSummary') {
                    $sql = $sql . " order by ss.PartTypeSummary ".$order_by_type." ";
                } else if($data['OrderBy'] == 'RemovalCode') {
                    $sql = $sql . " order by ss.RemovalCode ".$order_by_type." ";
                }
            } else {
                $sql = $sql . " order by s.CreatedDate desc ";
            }       
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
            while($row = mysqli_fetch_assoc($query))
            {
                if($row['BatchRecovery'] == 1)
                    {
                        $row['BatchRecovery'] = 'Yes';
                    }
                    else if($row['BatchRecovery'] == 0)
                    {
                        $row['BatchRecovery'] = 'No';
                    }
                $row2  = array($row['ShippingContainerID'],$row['ShippingID'],$row['packageName'],$row['PartTypeSummary'],$row['SealID'],$row['ContainerWeight'],$row['disposition'],$row['VendorName'],$row['LocationName'],$row['Status'],$row['RemovalCode'],$row['ContainerNotes'],$row['ReferenceType'],$row['ReferenceID'],$row['BatchRecovery']);
                $rows[] = $row2;
            }

$sheet_name = 'Pending Outbound Containers';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 