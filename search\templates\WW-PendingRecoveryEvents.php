<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";

$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'PendingRecoveryEvents.'.$today.'.csv';

$csv = "entity_id,unique_id,recovery_type,operator_login_id,origin_operator_login_id,recovery_location_id,origin_workstation_id,origin_recovery_datetime,pending_recovery_action\n";//Column headers
$sql = "select 'eV-Disposition-1' as entity_id,rr.TopLevelUniqueIdentifier as unique_id,rt.Recoverytype as recovery_type,u.UserName as operator_login_id,
        f.FacilityName as recovery_location_id,si.SiteName origin_workstation_id,rr.ID,rr.SerialNumber,pt.parttype,rr.CreatedDate,
        'Pending Save' as pending_recovery_action
			from parts_recovery_wip_records rr 			
			left join Recoverytype rt on rr.RecoveryTypeID = rt.Recoverytypeid 
			left join facility f on rt.FacilityID = f.FacilityID 
			left join site si on rr.SiteID = si.SiteID 
			left join parttype pt on rr.parttypeid = pt.parttypeid
			left join users u on rr.CreatedBy = u.UserId where rr.CreatedBy > 0
            UNION ALL
        select 'eV-Disposition-1' as entity_id,a.TopLevelUniqueIdentifier as unique_id,rt.Recoverytype as recovery_type,u.UserName as operator_login_id,
        f.FacilityName as recovery_location_id,si.SiteName as origin_workstation_id,a.AssetScanID,a.SerialNumber,pt.parttype,a.DateCreated,
        'Pending Save' as pending_recovery_action
            from asset a 
            left join Recoverytype rt on a.Recoverytypeid = rt.Recoverytypeid 
            left join facility f on a.FacilityID = f.FacilityID 
            left join site si on a.SiteID = si.SiteID 
            left join parttype pt on a.parttypeid = pt.parttypeid  
            left join users u on a.CreatedBy = u.UserId where a.StatusID = 10
            ";
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1);
}
while($row = mysqli_fetch_assoc($query))
{
	if($row['DateCreated'] != '')
    {
        $row['DateCreated'] = date("Y-m-d H:i:s", strtotime($row['DateCreated']));
    }
    else
    {
        $row['DateCreated'] = '';
    }
	
	$row['entity_id'] = str_replace(","," ",$row['entity_id']);
	$row['unique_id'] = str_replace(","," ",$row['unique_id']);
	$row['recovery_type'] = str_replace(","," ",$row['recovery_type']);
	$row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
	$row['recovery_location_id'] = str_replace(","," ",$row['recovery_location_id']);
	$row['origin_workstation_id'] = str_replace(","," ",$row['origin_workstation_id']);
	$row['pending_recovery_action'] = str_replace(","," ",$row['pending_recovery_action']);
	if($row['entity_id'] == '')
	{
		$row['entity_id'] = 'n/a';
	}
	if($row['unique_id'] == '')
	{
		$row['unique_id'] = 'n/a';
	}
	if($row['recovery_type'] == '')
	{
		$row['recovery_type'] = 'n/a';
	}
	if($row['operator_login_id'] == '')
	{
		$row['operator_login_id'] = 'n/a';
	}
	if($row['recovery_location_id'] == '')
	{
		$row['recovery_location_id'] = 'n/a';
	}
	if($row['origin_workstation_id'] == '')
	{
		$row['origin_workstation_id'] = 'n/a';
	}
	if($row['pending_recovery_action'] == '')
	{
		$row['pending_recovery_action'] = 'n/a';
	}
    $row2  = array($row['entity_id'],$row['unique_id'],$row['recovery_type'],$row['operator_login_id'],$row['operator_login_id'],$row['recovery_location_id'],$row['origin_workstation_id'],$row['DateCreated'],$row['pending_recovery_action']);
    $rows[] = $row2;
}
foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8]."\n"; //Append data to csv
}
$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>