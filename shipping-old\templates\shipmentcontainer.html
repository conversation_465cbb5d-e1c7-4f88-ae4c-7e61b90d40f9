<div class="page" data-ng-controller="shipment_container">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">


                <script type="text/ng-template" id="password.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Closing Container (Container ID : {{CurrentContainer.ShippingContainerID}})
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">
                                <form name="tpvForm">
                                    <md-input-container class="md-block">
                                        <label>Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('password','0')">
                                        <div ng-messages="tpvForm.AuditController.$error" multiple ng-if='tpvForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="FocusNextField('SealID','0')">
                                        <div ng-messages="tpvForm.Password.$error" multiple ng-if='tpvForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Seal ID</label>
                                        <input required name="SealID" id="SealID" ng-model="confirmDetails.NewSealID" ng-maxlength="100" type="SealID" ng-enter="FocusNextField('ContainerWeight','0')" ng-enter="FocusNextField('ContainerWeight','0')">
                                        <div ng-messages="tpvForm.SealID.$error" multiple ng-if='tpvForm.SealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Weight</label>
                                        <input required name="ContainerWeight" id="ContainerWeight" ng-model="confirmDetails.ContainerWeight" ng-max="999999" ng-min="0" type="number" ng-enter="hide()">
                                        <div ng-messages="tpvForm.SealID.$error" multiple ng-if='tpvForm.SealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button>
                        </div>
                    </div>
                </script>               

                <!--Add Container Start-->
                <md-card class="no-margin-h" >

                    <md-toolbar class="md-table-toolbar md-default" ng-init="AddContainer = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;">
                            <i ng-click="AddContainer = !AddContainer" class="material-icons md-primary" ng-show="AddContainer">keyboard_arrow_up</i>
                            <i ng-click="AddContainer = !AddContainer" class="material-icons md-primary" ng-show="! AddContainer">keyboard_arrow_down</i>
                            <span ng-click="AddContainer = !AddContainer">Add Container</span>
                            <div flex></div>

                            <span ng-show="canServerCountbeShown()"><strong class="mr-5">Server SN Count:</strong><span ng-class="{'badge bg-danger': ScannedServerCount() != newContainer.ExpectedServersCount,'badge bg-success': newContainer.ScannedServersCount == newContainer.ExpectedServersCount}">{{ScannedServerCount()}} / {{newContainer.ExpectedServersCount}}</span></span>
                            <!-- <button class="md-button md-raised btn-w-md md-primary mt-10" style="display: flex;">
                                <i class="material-icons">add</i> Add Serials
                            </button> -->
                        </div>
                    </md-toolbar>

                    <div ng-show="AddContainer">

                        <div style="margin-bottom: 10px;">
                            <form class="row" name="containerForm">
                                <div class="col-md-12">
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Container ID</label>
                                            <input required name="ShippingContainerID" id="ShippingConID" ng-model="newContainer.ShippingContainerID" ng-maxlength="50" ng-disabled="newContainer.CreatedBy > 0" autofocus ng-enter="GetShippingDetails(newContainer.ShippingContainerID,'new')">
                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!newContainer.ShippingContainerID" ng-click="GetShippingDetails(newContainer.ShippingContainerID,'new')">
                                                <md-icon class="material-icons" style="margin-top: -6px; margin-left: 3px;">arrow_forward</md-icon>
                                            </md-button>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.ShippingContainerID.$error" multiple ng-if='containerForm.ShippingContainerID.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 50.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Facility</label>
                                            <md-select name="FacilityID" ng-model="newContainer.FacilityID" required ng-disabled="true" ng-change="GetFacilityContainers();GetFacilityByProducts()">
                                                <md-option value="{{fac.FacilityID}}" ng-repeat="fac in Facilities">{{fac.FacilityName}}</md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                            <div ng-messages="shipmentForm.FacilityID.$error" multiple ng-if='shipmentForm.FacilityID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Container Type</label>
                                            <md-select name="idPackage" ng-model="newContainer.idPackage" required>
                                                <md-option value="{{pkg.idPackage}}" ng-repeat="pkg in PackageTypes">{{pkg.packageName}}</md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                            <div ng-messages="containerForm.idPackage.$error" multiple ng-if='containerForm.idPackage.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Removal Type</label>
                                            <!-- <md-select name="disposition_id" ng-model="newContainer.disposition_id" required ng-disabled="newContainer.CreatedBy > 0" ng-change="GetFacilityByProducts()"> -->
                                            <md-select name="disposition_id" ng-model="newContainer.disposition_id" required ng-disabled="ContainerSerials.length > 0" ng-change="GetFacilityByProducts();GetReferenceTypeDetails()">
                                                <md-option value="{{disp.disposition_id}}" ng-repeat="disp in Dispositions">{{disp.disposition}} <span style="color:red;" ng-show="disp.sub_disposition > 0">(Sub Disposition)</span></md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                            <div ng-messages="containerForm.disposition_id.$error" multiple ng-if='containerForm.disposition_id.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Container Weight</label>
                                            <input name="ContainerWeight" ng-model="newContainer.ContainerWeight" ng-max="999999" ng-min="0" required type="number">
                                            <div class="error-sapce">
                                            <div ng-messages="containerForm.ContainerWeight.$error" multiple ng-if='containerForm.ContainerWeight.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="min">Min Value 0.</div>
                                                <div ng-message="max">Max Value 999999.</div>
                                            </div>
                                            </div>
                                        </md-input-container>
                                    </div> -->

                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Custom ID</label>
                                            <input name="CustomID" ng-model="newContainer.CustomID" ng-maxlength="50">
                                            <div ng-messages="containerForm.CustomID.$error" multiple ng-if='containerForm.CustomID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 50.</div>
                                            </div>
                                        </md-input-container>
                                    </div> -->

                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Container Notes</label>
                                            <input name="ContainerNotes" ng-model="newContainer.ContainerNotes" ng-maxlength="1000" required>
                                            <div class="error-sapce">
                                            <div ng-messages="containerForm.ContainerNotes.$error" multiple ng-if='containerForm.ContainerNotes.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 1000.</div>
                                            </div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Seal ID</label>
                                            <input name="SealID" ng-model="newContainer.SealID" ng-maxlength="50" required>
                                            <div class="error-sapce">
                                            <div ng-messages="containerForm.SealID.$error" multiple ng-if='containerForm.SealID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 50.</div>
                                            </div>
                                            </div>
                                        </md-input-container>
                                    </div> -->


                                    <div class="col-md-3" >                                        
                                        <div class="autocomplete">
                                            <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                md-input-name="group"
                                                md-input-maxlength="100"
                                                ng-disabled="newContainer.FacilityID == 0 || newContainer.FacilityID == NULL "
                                                md-no-cache="noCache"
                                                md-search-text-change="LocationChange1(newContainer.group)"
                                                md-search-text="newContainer.group"
                                                md-items="item in queryLocationSearch1(newContainer.group)"
                                                md-item-text="item.GroupName"
                                                md-selected-item-change="selectedLocationChange1(item)"
                                                md-min-length="0"
                                                ng-model-options='{ debounce: 1000 }'
                                                md-escape-options="clear"
                                                md-floating-label="Outbound Location Group"
                                                >
                                                <md-item-template>
                                                    <span md-highlight-text="newContainer.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                </md-item-template>
                                                <md-not-found>
                                                    No Records matching "{{newContainer.group}}" were found.
                                                </md-not-found>
                                                <div ng-messages="containerForm.group.$error" ng-if="containerForm.group.$touched">
                                                    <div ng-message="required">No Records matching.</div>
                                                    <div ng-message="minlength">Min length 2.</div>
                                                    <div ng-message="maxlength">Max length 100.</div>
                                                </div>
                                            </md-autocomplete>
                                        </div>
                                    </div>


                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Reference Type</label>                                            
                                            <md-select name="ReferenceTypeID" ng-model="newContainer.ReferenceTypeID" required ng-change="GetReferenceType()" >
                                                <md-option value="{{type.ReferenceTypeID}}" ng-repeat="type in ReferenceType">{{type.ReferenceType}} </md-option>
                                            </md-select>
                                            <div class="error-sapce">
                                            <div ng-messages="containerForm.ReferenceTypeID.$error" multiple ng-if='containerForm.ReferenceTypeID.$dirty'>
                                                <div ng-message="required">This is required.</div>
                                            </div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <div class="col-md-3" >
                                        <md-switch class="mt-10" ng-model="newContainer.ReferenceIDRequired" aria-label="Reference ID Required" ng-disabled="true" ng-true-value="'1'" ng-false-value="'0'"> Reference ID Required</md-switch>
                                    </div>
                                    <div class="col-md-12"></div>
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Reference ID</label>
                                            <input name="ReferenceID" id="ReferenceID" ng-model="newContainer.ReferenceID" ng-minlength="3" ng-maxlength="500" ng-required="newContainer.ReferenceIDRequired == '1'" ng-disabled="newContainer.ReferenceIDRequired == '0' || ! newContainer.ReferenceIDRequired">
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.ReferenceID.$error" multiple ng-if='containerForm.ReferenceID.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 500.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div>

                                    <!-- <div class="col-md-3">                                        
                                        <div class="autocomplete">
                                            <md-autocomplete flex style="margin-bottom:0px !important; padding-top: 0px !important; min-width: 100% !important;"
                                                    md-input-name="location"
                                                    md-input-maxlength="100"
                                                    ng-disabled="newContainer.FacilityID == 0 || newContainer.FacilityID == NULL "
                                                    md-no-cache="noCache"
                                                    md-search-text-change="LocationChange(newContainer.location)"
                                                    md-search-text="newContainer.location"
                                                    md-items="item in queryLocationSearch(newContainer.location)"
                                                    md-item-text="item.LocationName"
                                                    md-selected-item-change="selectedLocationChange(item)"
                                                    md-min-length="0"
                                                    md-escape-options="clear"
                                                    md-floating-label="Outbound Location"
                                                    >
                                                    <md-item-template>
                                                        <span md-highlight-text="newContainer.location" md-highlight-flags="^i">{{item.LocationName}}</span>
                                                    </md-item-template>
                                                    <md-not-found>
                                                        No Records matching "{{newContainer.location}}" were found.
                                                    </md-not-found>
                                                    <div ng-messages="containerForm.location.$error" ng-if="containerForm.location.$touched">
                                                        <div ng-message="required">No Records matching.</div>
                                                        <div ng-message="minlength">Min length 2.</div>
                                                        <div ng-message="maxlength">Max length 100.</div>
                                                    </div>
                                            </md-autocomplete>
                                        </div>
                                    </div> -->

                                    <!-- <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label style="padding-left:30px;">Controller Login ID </label>
                                            <i class="material-icons text-danger error" style="position: absolute; margin-top: 4px;" role="img" aria-label="error" ng-show="! newContainer.PasswordVerified">close</i>
                                            <i class="material-icons text-success yes" style="position: absolute; margin-top: 4px;" role="img" aria-label="yes" ng-show="newContainer.PasswordVerified">done</i>
                                            <input style="padding-left:30px;" name="ShippingControllerLoginID" ng-model="newContainer.ShippingControllerLoginID" ng-maxlength="100" required ng-change="newContainer.PasswordVerified = false" ng-enter="ValidateSanitizationControllerPopup($event)" />
                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="!newContainer.ShippingControllerLoginID" ng-click="ValidateSanitizationControllerPopup($event)">
                                                <md-icon class="material-icons" style="margin-top: -6px; margin-left: 3px;">arrow_forward</md-icon>
                                            </md-button>
                                            <div class="error-sapce">
                                                <div ng-messages="containerForm.ShippingControllerLoginID.$error" multiple ng-if='containerForm.ShippingControllerLoginID.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="minlength">Min length 3.</div>
                                                    <div ng-message="maxlength">Max length 100.</div>
                                                </div>
                                            </div>
                                        </md-input-container>
                                    </div> -->



                                    <!-- <div class="col-md-3" ng-show="newContainer.StatusID == '1'">
                                        <md-input-container class="md-block" >
                                            <md-checkbox ng-model="newContainer.CloseContainer" name="CloseContainer"> Close Container </md-checkbox>
                                        </md-input-container>
                                        <div class="error-sapce"></div>
                                    </div> -->

                                    <div class="col-md-3 pt-10" >
                                        <span class="text-danger" ng-click="CloseContainer(newContainer,$event)" ng-show="newContainer.StatusID == '1'">Close Container</span>
                                        <span class="text-success" ng-click="ReopenContainer(newContainer,$event)" ng-show="newContainer.StatusID == '6'">Reopen Container</span>
                                    </div>

                                    <!-- <div class="col-md-3 pt-10" ng-show="newContainer.StatusID == '6'">
                                        Container Closed
                                    </div> -->

                                    <div class="col-md-12 btns-row">
                                        <button class="md-button md-raised btn-w-md  md-default" ng-click="ClearContainer()">
                                            Cancel
                                        </button>

                                        <md-button class="md-raised btn-w-md md-primary btn-w-md" id="save_button"
                                            data-ng-disabled="containerForm.$invalid || newContainer.busy" ng-click="CreateContainer()">
                                            <span ng-show="! newContainer.busy">Save</span>
                                            <span ng-show="newContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                        </md-button>
                                    </div>
                                </div>
                            </form>
                            <div style="clear: both;"></div>
                        </div>

                        <!-- <div class="bg-grey-light pt-5" style="margin-bottom: 20px;" ng-show="newContainer.CreatedBy > 0 && newContainer.StatusID == '1' && newContainer.PasswordVerified"> -->
                        <div class="bg-grey-light pt-5" style="margin-bottom: 20px;" ng-show="newContainer.CreatedBy > 0 && newContainer.StatusID == '1'">
                        <!-- <div class="bg-grey-light pt-5" style="margin-bottom: 20px;" ng-show="newContainer.CreatedBy > 0 && newContainer.StatusID == '1'"> -->
                            <form class="row" name="SerialForm">
                               
                                <!--
                                <div class="col-md-12">
                                    <div class="alert alert-info" role="alert" ng-init="showAlert3=true;" ng-show="showAlert3">
                                        <i class="material-icons">info</i> <strong class="mr-5">Note!</strong> Please go through the any one of the form below.
                                        <i class="material-icons alert-close" ng-click="showAlert3 = ! showAlert3">close</i>
                                    </div>
                                </div>
                                -->

                                <!--Tabs Started-->
                                <div class="col-md-12">
                                    <md-tabs md-dynamic-height md-border-bottom class="md-litegrey">   

                                        <md-tab label="Inventory">
                                            <div class="mt-10">
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block includedsearch">
                                                        <label>SN</label>
                                                        <input required name="SerialNumber" ng-model="newContainer.SerialNumber" ng-maxlength="50" id="SerialNumber" ng-enter="GetCurrentTime(newContainer,'serial_scan_time');ValidateSerialNumber(newContainer.SerialNumber)"/>
                                                        <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newContainer.SerialNumber" ng-click="GetCurrentTime(newContainer,'serial_scan_time');ValidateSerialNumber(newContainer.SerialNumber)">
                                                            <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>MPN</label>                                                        
                                                        <input required name="UniversalModelNumber" ng-model="newContainer.UniversalModelNumber" ng-maxlength="100" ng-enter="GetCurrentTime(newContainer,'mpn_scan_time');GetExactMPN(newContainer.UniversalModelNumber)" />
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                            ng-disabled="!newContainer.UniversalModelNumber" ng-click="GetCurrentTime(newContainer,'mpn_scan_time');GetExactMPN(newContainer.UniversalModelNumber)">
                                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-4">
                                                    <md-input-container class="md-block includedsearch">
                                                        <label>Seal ID</label>
                                                        <!-- <input required name="UniversalModelNumber" ng-model="newContainer.UniversalModelNumber" ng-maxlength="100" ng-enter="AutoNavigateInventorySave()" /> -->
                                                        <input required id="sanitization_seal_id" name="sanitization_seal_id" ng-model="newContainer.sanitization_seal_id" ng-maxlength="100" ng-enter="ValidateSanitizationSealID(newContainer.sanitization_seal_id,newContainer.SerialNumber,newContainer.AssetScanID)" />
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                            ng-disabled="!newContainer.sanitization_seal_id" ng-click="ValidateSanitizationSealID(newContainer.sanitization_seal_id,newContainer.SerialNumber,newContainer.AssetScanID)">
                                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>Sanitization Verification ID</label>
                                                        <input required name="SanitizationVerificationID" ng-model="newContainer.SanitizationVerificationID" data-ng-disabled="true" ng-maxlength="100" >
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>Part Notes</label>
                                                        <input required name="Notes" ng-model="newContainer.Notes" ng-maxlength="500" >
                                                    </md-input-container>
                                                </div>  
                                                
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>COO</label>
                                                        <md-select name="COOID" ng-model="newContainer.COOID" id="COOID" required ng-change="GetCurrentTime(newContainer,'coo_scan_time');">
                                                            <md-option value="{{ir.COOID}}" ng-repeat="ir in COOList">{{ir.COO}}</md-option>
                                                        </md-select>                                                            
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-12">
                                                    <div class="row"> 
                                                        <div class="col-md-4">
                                                            <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                                <label>Scan for Save</label>
                                                                <input name="scan_for_save" ng-model="scan_for_save" ng-enter="AddSerialToContainer()" id="scan_for_save" style="width:2px;">
                                                            </md-input-container>
                                                        </div>
                                                        <div class="col-md-4 btns-row">                                              
                                                            <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                                data-ng-disabled="(!newContainer.SerialNumber || !newContainer.Notes || !newContainer.SanitizationVerificationID || !newContainer.UniversalModelNumber) || newContainer.busy" ng-click="AddSerialToContainer()">
                                                                <span ng-show="! newContainer.busy">Save</span>
                                                                <span ng-show="newContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                            </md-button>
                                                        </div>
                                                    </div> 
                                                </div>

                                            </div>
                                        </md-tab>
                                        <!-- <md-tab label="SubComponent Inventory">
                                            <div class="mt-10">
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block includedsearch">
                                                        <label>Inventory SN</label>
                                                        <input required name="InventorySerialNumber" ng-model="newContainer.InventorySerialNumber" ng-maxlength="50" id="InventorySerialNumber" ng-enter="GetCurrentTime(newContainer,'inventory_serial_scan_time');ValidateInventorySerialNumber(newContainer.InventorySerialNumber)" />
                                                        <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newContainer.InventorySerialNumber" ng-click="GetCurrentTime(newContainer,'inventory_serial_scan_time');ValidateInventorySerialNumber(newContainer.InventorySerialNumber)">
                                                            <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>MPN</label>                                                        
                                                        <input required name="InventoryUniversalModelNumber" ng-model="newContainer.InventoryUniversalModelNumber" ng-maxlength="100" ng-enter="GetCurrentTime(newContainer,'inventory_mpn_scan_time');GetExactMPNSN(newContainer.InventoryUniversalModelNumber)" />
                                                        <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right"
                                                            ng-disabled="!newContainer.InventoryUniversalModelNumber" ng-click="GetCurrentTime(newContainer,'inventory_mpn_scan_time');GetExactMPNSN(newContainer.InventoryUniversalModelNumber)">
                                                            <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>Part Notes</label>
                                                        <input required name="InventoryNotes" ng-model="newContainer.InventoryNotes" ng-maxlength="500" >
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-12">
                                                    <div class="row"> 
                                                        <div class="col-md-4">
                                                            <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                                <label>Scan for SubComponent Save</label>
                                                                <input name="scan_for_subcomponentsave" ng-model="scan_for_subcomponentsave" ng-enter="AddInventorySerialToContainer()" id="scan_for_subcomponentsave" style="width:2px;">
                                                            </md-input-container>
                                                        </div>
                                                        <div class="col-md-4 btns-row">                                              
                                                            <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                                data-ng-disabled="(!newContainer.InventorySerialNumber || !newContainer.InventoryNotes || !newContainer.InventoryUniversalModelNumber) || newContainer.busy" ng-click="AddInventorySerialToContainer()">
                                                                <span ng-show="! newContainer.busy">Save</span>
                                                                <span ng-show="newContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                            </md-button>
                                                        </div>
                                                    </div> 
                                                </div>

                                            </div> -->
                                        </md-tab>
                                        <md-tab label="Byproduct">
                                            <div class="mt-10">
                                                <div class="col-md-4 col-md-offset-4">
                                                    <md-input-container class="md-block">
                                                        <label>Part Type</label>
                                                        <md-select name="byproduct_id" ng-model="newContainer.byproduct_id" required ng-disabled="byproduct_id">
                                                            <!-- <md-option value="{{pro.byproduct_id}}" ng-repeat="pro in ByProducts">{{pro.part_type}}</md-option> -->
                                                            <md-option value="{{pro.byproduct_id}}" ng-repeat="pro in ByProducts">{{pro.parttype}}</md-option>
                                                        </md-select>
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                        <label>Scan for Save</label>
                                                        <input name="scan_for_save_byproduct" ng-model="scan_for_save_byproduct" ng-enter="AddByProductToContainer()" id="scan_for_save_byproduct" style="width:2px;">
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-12 btns-row">
                                                    <md-button class="md-raised btn-w-md md-primary btn-w-md" ng-disabled="(!newContainer.byproduct_id) || newContainer.busy" ng-click="AddByProductToContainer()">
                                                        <span ng-show="! newContainer.busy" >Save</span>
                                                        <span ng-show="newContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                    </md-button>
                                                </div>
                                            </div>
                                        </md-tab>
                                        <md-tab label="Recovered Gear">
                                            <div class="mt-10">
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block includedsearch">
                                                        <label>SN</label>
                                                        <input required name="SerialNumberServer" ng-model="newContainer.SerialNumberServer" ng-maxlength="50" id="SerialNumberServer" ng-enter="GetCurrentTime(newContainer,'server_serial_scan_time');ValidateServerSerialNumber(newContainer.SerialNumberServer)"/>
                                                        <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newContainer.SerialNumberServer" ng-click="GetCurrentTime(newContainer,'server_serial_scan_time');ValidateServerSerialNumber(newContainer.SerialNumberServer)">
                                                            <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>Type</label>
                                                        <input name="Type" ng-model="newContainer.Type" data-ng-disabled="true">
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block" ng-if="newContainer.Type == 'Server'">
                                                        <label>Sanitization Verification ID</label>
                                                        <input required name="ServerSanitizationVerificationID" id="ServerSanitizationVerificationID" ng-model="newContainer.ServerSanitizationVerificationID" ng-maxlength="100" data-ng-disabled="true" ng-enter="FocusNextField('scan_for_save_server','0')">
                                                        <!-- <input required name="ServerSanitizationVerificationID" id="ServerSanitizationVerificationID" ng-model="newContainer.ServerSanitizationVerificationID" ng-maxlength="100" ng-enter="FocusNextField('scan_for_save_server','0')"> -->
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block" ng-if="newContainer.Type == 'Switch'">
                                                        <label>MPN</label>
                                                        <input required name="MPN" ng-model="newContainer.MPN" ng-maxlength="100" ng-enter="GetCurrentTime(newContainer,'server_mpn_scan_time');FocusNextField('scan_for_save_server','0')" />
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>Part Notes</label>
                                                        <input required name="NotesServer" ng-model="newContainer.NotesServer" ng-maxlength="500" ng-enter="FocusNextField('add_switch','0')">
                                                    </md-input-container>
                                                </div>

                                                <div class="col-md-3" ng-show="canServerCountbeShown()">
                                                    <md-switch class="mt-10" ng-model="newContainer.ByPassContainerMatching" aria-label="Notes Required" ng-true-value="'1'" ng-false-value="'0'"> Bypass Container and Rack Matching</md-switch>
                                                </div>
                                                
                                                <div class="col-md-12">
                                                    <div class="row"> 
                                                        <div class="col-md-4">
                                                            <md-input-container class="md-block includedsearch" style="opacity:0;">
                                                                <label>Scan for Save</label>
                                                                <input name="scan_for_save_server" ng-model="scan_for_save_server" ng-enter="AddServerSerialToContainer()" id="scan_for_save_server" style="width:2px;">
                                                            </md-input-container>
                                                        </div>
                                                        <div class="col-md-4 btns-row">                                              
                                                            <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                                                data-ng-disabled="(!newContainer.SerialNumberServer || !newContainer.NotesServer ) || newContainer.busy" ng-click="AddServerSerialToContainer()" id="add_switch">
                                                                <span ng-show="! newContainer.busy">Save</span>
                                                                <span ng-show="newContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                            </md-button>
                                                        </div>
                                                    </div> 
                                                </div>
                                            </div>
                                        </md-tab>

                                        <md-tab label="Reuse Media">
                                            <div class="mt-10">
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block includedsearch">
                                                        <label>Media SN</label>
                                                        <input required name="MediaSerialNumber" ng-model="newContainer.MediaSerialNumber" ng-maxlength="50" id="MediaSerialNumber" ng-enter="GetCurrentTime(newContainer,'media_serial_scan_time');ValidateMediaSerialNumber(newContainer.MediaSerialNumber)"/>
                                                        <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SN" ng-disabled="!newContainer.MediaSerialNumber" ng-click="GetCurrentTime(newContainer,'media_serial_scan_time');ValidateMediaSerialNumber(newContainer.MediaSerialNumber)">
                                                            <md-icon class="scanicon" md-svg-src="../assets/images/search.svg"></md-icon>
                                                        </md-button>
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block">
                                                        <label>Part Type</label>                                                            
                                                        <md-select name="ReuseMedia_part_type" ng-model="newContainer.ReuseMedia_part_type" required ng-disabled="true">
                                                            <md-option value="HDD">HDD</md-option>
                                                            <md-option value="SSD">SSD</md-option>
                                                        </md-select>
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-4">
                                                    <md-input-container class="md-block includedsearch">
                                                        <label>MPN</label>
                                                        <input required name="ReuseMediaUniversalModelNumber" id="ReuseMediaUniversalModelNumber" ng-model="newContainer.ReuseMediaUniversalModelNumber" ng-maxlength="100" ng-enter="GetCurrentTime(newContainer,'media_mpn_scan_time');AddMediaToContainer()" />
                                                    </md-input-container>
                                                </div>
                                                <div class="col-md-12 btns-row">
                                                    <md-button class="md-raised btn-w-md md-primary btn-w-md" ng-disabled="(!newContainer.MediaSerialNumber || !newContainer.ReuseMedia_part_type || !newContainer.ReuseMediaUniversalModelNumber) || newContainer.busy" ng-click="AddMediaToContainer()">
                                                        <span ng-show="! newContainer.busy" >Save</span>
                                                        <span ng-show="newContainer.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                    </md-button>
                                                </div>
                                            </div>
                                        </md-tab>

                                    </md-tabs>
                                </div>
                                <!--Tabs Closed-->

                            </form>

                            <div class="row"  ng-show="ContainerSerials.length > 0">

                                <div class="col-md-12">
                                    <div class="col-md-12">

                                        <div ng-show="ContainerSerials" class="pull-right">
                                            <small>
                                            Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                            to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                                <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                            of <span style="font-weight:bold;">{{total}}</span>
                                            </small>
                                        </div>
                                        <div style="clear:both;"></div>

                                        <div class="table-responsive" style="overflow: auto;">
                                            <table class="table table-striped" md-table md-row-select>

                                                <thead md-head>

                                                    <tr class="th_sorting" md-row>
                                                        <th style="min-width: 80px;">Actions</th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('SerialNumber')" ng-class="{'orderby' : OrderBy == 'SerialNumber'}">
                                                            <div>
                                                                Serial Number<i class="fa fa-sort pull-right" ng-show="OrderBy != 'SerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'SerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('InventorySerialNumber')" ng-class="{'orderby' : OrderBy == 'InventorySerialNumber'}">
                                                            <div style="min-width: 220px;">
                                                                Inventory Serial Number<i class="fa fa-sort pull-right" ng-show="OrderBy != 'InventorySerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'InventorySerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ServerSerialNumber')" ng-class="{'orderby' : OrderBy == 'ServerSerialNumber'}">
                                                            <div style="min-width: 220px;">
                                                                Switch / Server SN<i class="fa fa-sort pull-right" ng-show="OrderBy != 'ServerSerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'ServerSerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('MediaSerialNumber')" ng-class="{'orderby' : OrderBy == 'MediaSerialNumber'}">
                                                            <div style="min-width: 220px;">
                                                                Media SN<i class="fa fa-sort pull-right" ng-show="OrderBy != 'MediaSerialNumber'"></i>
                                                                <span ng-show="OrderBy == 'MediaSerialNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('Notes')" ng-class="{'orderby' : OrderBy == 'Notes'}">
                                                            <div>
                                                                Part Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Notes'"></i>
                                                                <span ng-show="OrderBy == 'Notes'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('UniversalModelNumber')" ng-class="{'orderby' : OrderBy == 'UniversalModelNumber'}">
                                                            <div>
                                                                MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'UniversalModelNumber'"></i>
                                                                <span ng-show="OrderBy == 'UniversalModelNumber'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('part_type')" ng-class="{'orderby' : OrderBy == 'part_type'}">
                                                            <div>
                                                                Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'part_type'"></i>
                                                                <span ng-show="OrderBy == 'part_type'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('COO')" ng-class="{'orderby' : OrderBy == 'COO'}">
                                                            <div>
                                                                COO <i class="fa fa-sort pull-right" ng-show="OrderBy != 'COO'"></i>
                                                                <span ng-show="OrderBy == 'COO'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>

                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ShippingContainerID')" ng-class="{'orderby' : OrderBy == 'ShippingContainerID'}">
                                                            <div>
                                                                Container ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ShippingContainerID'"></i>
                                                                <span ng-show="OrderBy == 'ShippingContainerID'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">
                                                            <div>
                                                                Date Added <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>
                                                                <span ng-show="OrderBy == 'CreatedDate'">
                                                                    <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                    <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                </span>
                                                            </div>
                                                        </th>
                                                        <th>
                                                            <div>Move To BIN</div>
                                                        </th>
                                                    </tr>

                                                    <tr md-row class="errornone">
                                                        <td></td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="SerialNumber" ng-model="filter_text[0].SerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="InventorySerialNumber" ng-model="filter_text[0].InventorySerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="ServerSerialNumber" ng-model="filter_text[0].ServerSerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="MediaSerialNumber" ng-model="filter_text[0].MediaSerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="Notes" ng-model="filter_text[0].Notes" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="UniversalModelNumber" ng-model="filter_text[0].UniversalModelNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="part_type" ng-model="filter_text[0].part_type" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>

                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="COO" ng-model="filter_text[0].COO" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="ShippingContainerID" ng-model="filter_text[0].ShippingContainerID" ng-change="MakeFilter()"  aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td></td>
                                                    </tr>
                                                </thead>

                                                <tbody md-body ng-show="ContainerSerials.length > 0">
                                                    <tr md-row ng-repeat="serial in ContainerSerials">
                                                        <td class="actionicons" style="min-width: 60px;">
                                                            <a href="{{host}}label/master/examples/shipmentseriallabel.php?id={{serial.SerialID}}" target="_blank">
                                                                <i class="material-icons print" role="img" aria-label="print">print</i>
                                                            </a>
                                                        </td>

                                                        <td md-cell>
                                                            {{serial.SerialNumber}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.InventorySerialNumber}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.ServerSerialNumber}}
                                                        </td>

                                                        <td md-cell>
                                                            {{serial.MediaSerialNumber}}
                                                        </td>

                                                        <td md-cell>
                                                            {{serial.Notes}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.UniversalModelNumber}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.part_type}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.COO}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.ShippingContainerID}}
                                                        </td>
                                                        <td md-cell>
                                                            {{serial.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}
                                                        </td>
                                                        <td md-cell>
                                                            <md-input-container class="md-block md-no-float includedsearch tdinput" ng-show="serial.byproduct_id == '' || serial.byproduct_id == NULL">
                                                                <input required name="BinName" ng-model="serial.BinName" required>
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="DeleteSerialFromContainer(serial,$event)" ng-disabled="!serial.BinName">
                                                                    Go
                                                                </md-button>
                                                            </md-input-container>

                                                            <i class="material-icons text-danger" ng-click="DeleteByProductFromContainer(serial,$event)" ng-show="serial.byproduct_id > 0">close</i>

                                                        </td>
                                                    </tr>
                                                </tbody>
                                                <tfoot>
                                                    <tr>
                                                        <td colspan="11">
                                                            <div>
                                                                <ul class="pagination">
                                                                    <li ng-class="prevPageDisabled()">
                                                                        <a href ng-click="firstPage()"><< First</a>
                                                                    </li>
                                                                    <li ng-class="prevPageDisabled()">
                                                                        <a href ng-click="prevPage()"><< Prev</a>
                                                                    </li>
                                                                    <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                        <a style="cursor:pointer;">{{n+1}}</a>
                                                                    </li>
                                                                    <li ng-class="nextPageDisabled()">
                                                                        <a href ng-click="nextPage()">Next >></a>
                                                                    </li>
                                                                    <li ng-class="nextPageDisabled()">
                                                                        <a href ng-click="lastPage()">Last >></a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </div>

                </md-card>
                <!--Add Container Close-->

            </article>
        </div>
    </div>
</div>
