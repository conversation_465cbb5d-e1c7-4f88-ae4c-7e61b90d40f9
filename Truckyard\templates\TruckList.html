<div ng-controller = "TruckList" class="page">
    <div ng-show="loading" class="loading" style="text-align:center;"><img src="../images/loading2.gif" /> LOADING...</div>

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="TruckList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="TruckList = !TruckList" class="material-icons md-primary" ng-show="TruckList">keyboard_arrow_up</i>
                                <i ng-click="TruckList = !TruckList" class="material-icons md-primary" ng-show="! TruckList">keyboard_arrow_down</i>
                                <span ng-click="TruckList = !TruckList">Truck Booking List</span>
                                <div flex></div> 
                                 <a href="#!/TruckList" ng-click="ExportTruckListxls()" class="md-button md-raised btn-w-md md-default" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a> 
                                <a href="#!/Truck" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Truck
                                </a>
                            </div>
                        </md-toolbar>
                        <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">                            
                            <p>No Trucks available </p>
                        </div>
                        <div class="row"  ng-show="TruckList">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div class="tablemovebtns">
                                        <a class="md-button md-raised md-default" id="left-button"><i class="material-icons">keyboard_arrow_left</i></a>
                                    </div>
                                    <div class="tablemovebtns">
                                        <a class="md-button md-raised md-default" id="right-button"><i class="material-icons">keyboard_arrow_right</i></a>
                                    </div>

                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 80px;">Edit</th>
                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                           
                                                        <div>                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                    
                                                            <span ng-show="OrderBy == 'FacilityName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LoadNumber')" ng-class="{'orderby' : OrderBy == 'LoadNumber'}">                           
                                                        <div>                               
                                                            Load # <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LoadNumber'"></i>                                    
                                                            <span ng-show="OrderBy == 'LoadNumber'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ArrivalType')" ng-class="{'orderby' : OrderBy == 'ArrivalType'}">
                                                        <div>                               
                                                            Shipment Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ArrivalType'"></i>                                 
                                                            <span ng-show="OrderBy == 'ArrivalType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ParkingLocationName')" ng-class="{'orderby' : OrderBy == 'ParkingLocationName'}">
                                                        <div style="min-width: 180px;">                               
                                                            Parking Location <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ParkingLocationName'"></i>                                 
                                                            <span ng-show="OrderBy == 'ParkingLocationName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CarrierName')" ng-class="{'orderby' : OrderBy == 'CarrierName'}">
                                                        <div>                               
                                                            Carrier <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CarrierName'"></i>                                 
                                                            <span ng-show="OrderBy == 'CarrierName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ArrivalDate')" ng-class="{'orderby' : OrderBy == 'ArrivalDate'}">
                                                        <div>                               
                                                            Arrival Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ArrivalDate'"></i>                                 
                                                            <span ng-show="OrderBy == 'ArrivalDate'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('ArrivalTime')" ng-class="{'orderby' : OrderBy == 'ArrivalTime'}">
                                                        <div style="min-width: 230px;">                               
                                                            Expected Arrival Time <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ArrivalTime'"></i>                                 
                                                            <span ng-show="OrderBy == 'ArrivalTime'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DepartureTime')" ng-class="{'orderby' : OrderBy == 'DepartureTime'}">
                                                        <div style="min-width: 230px;">                               
                                                            Expected Departure Time  <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DepartureTime'"></i>                                 
                                                            <span ng-show="OrderBy == 'DepartureTime'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('LoadType')" ng-class="{'orderby' : OrderBy == 'LoadType'}">
                                                        <div>                               
                                                            Load Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LoadType'"></i>                                 
                                                            <span ng-show="OrderBy == 'LoadType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>                                                                       
                                                        </div>
                                                    </th>
                                                   
                                                    
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('TruckReg')" ng-class="{'orderby' : OrderBy == 'TruckReg'}">                         
                                                        <div>                               
                                                            Truck Reg <i class="fa fa-sort pull-right" ng-show="OrderBy != 'TruckReg'"></i>                                  
                                                            <span ng-show="OrderBy == 'TruckReg'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('TrailerNumber')" ng-class="{'orderby' : OrderBy == 'TrailerNumber'}">                         
                                                        <div>                               
                                                            Trailer No <i class="fa fa-sort pull-right" ng-show="OrderBy != 'TrailerNumber'"></i>                                  
                                                            <span ng-show="OrderBy == 'TrailerNumber'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DriverName')" ng-class="{'orderby' : OrderBy == 'DriverName'}">                         
                                                        <div>                               
                                                            Driver <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DriverName'"></i>                                  
                                                            <span ng-show="OrderBy == 'DriverName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DriverID')" ng-class="{'orderby' : OrderBy == 'DriverID'}">                         
                                                        <div>                               
                                                            Driver ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DriverID'"></i>                                  
                                                            <span ng-show="OrderBy == 'DriverID'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Notes')" ng-class="{'orderby' : OrderBy == 'Notes'}">                         
                                                        <div>                               
                                                            Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Notes'"></i>                                  
                                                            <span ng-show="OrderBy == 'Notes'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">                         
                                                        <div>                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>                                  
                                                            <span ng-show="OrderBy == 'Status'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                  
                                                        </div>          
                                                    </th>
                                                     <th style="min-width: 80px;">Process</th>
                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LoadNumber" ng-model="filter_text[0].LoadNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ArrivalType" ng-model="filter_text[0].ArrivalType" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ParkingLocationName" ng-model="filter_text[0].ParkingLocationName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CarrierName" ng-model="filter_text[0].CarrierName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ArrivalDate" ng-model="filter_text[0].ArrivalDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ArrivalTime" ng-model="filter_text[0].ArrivalTime" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DepartureTime" ng-model="filter_text[0].DepartureTime" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                   <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LoadType" ng-model="filter_text[0].LoadType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="TruckReg" ng-model="filter_text[0].TruckReg" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                     <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="TrailerNumber" ng-model="filter_text[0].TrailerNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                   
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DriverName" ng-model="filter_text[0].DriverName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="DriverID" ng-model="filter_text[0].DriverID" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Notes" ng-model="filter_text[0].Notes" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td></td>                 
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/Truck/{{product.TruckID}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a>
                                                        <a href="#!/TruckList" ng-click="DeleteTruck(product.TruckID)"><md-icon class="material-icons text-danger">delete</md-icon></a>
                                                 </td>
                                                    <td>
                                                        {{product.FacilityName}}
                                                    </td>
                                                      <td>
                                                        {{product.LoadNumber}}
                                                    </td>
                                                    <td>
                                                        {{product.ArrivalType}}                            
                                                    </td>
                                                    <td>
                                                        {{product.ParkingLocationName}}                            
                                                    </td>   
                                                    <td>
                                                        {{product.CarrierName}}                            
                                                    </td>   
                                                    <td>
                                                        {{product.ArrivalDate}}                            
                                                    </td>   
                                                    <td>
                                                        {{product.ArrivalTime}}                            
                                                    </td>   
                                                    <td>
                                                        {{product.DepartureTime}}                            
                                                    </td>  
                                                    <td>
                                                        {{product.LoadType}}                            
                                                    </td>
                                                    <td>
                                                        {{product.TruckReg}}                            
                                                    </td>
                                                    <td>
                                                        {{product.TrailerNumber}}                            
                                                    </td>                                                   
                                                    <td>
                                                       {{product.DriverName}}
                                                    </td> 
                                                     <td>
                                                       {{product.DriverID}}
                                                    </td> 
                                                     <td>
                                                       {{product.Notes}}
                                                    </td> 
                                                     <td>
                                                       {{product.Status}}
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block tdinput">
                                                            <md-select name="Status" ng-model="product.Status" aria-label="select" ng-change="ChangeStatus(product)">
                                                                <md-option value="Arrived"> Arrived </md-option>
                                                                <md-option value="In Progress"> In Progress </md-option>
                                                                <md-option value="Complete"> Complete </md-option>
                                                                <md-option value="No Show"> No Show </md-option>
                                                            </md-select>                                           
                                                        </md-input-container>
                                                    </td> 

                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="17">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>