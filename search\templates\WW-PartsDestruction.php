<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
//$ProcessDatefrom = "2025-01-08 00:00:00";
//$ProcessDateto = "2025-04-26 23:59:59";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'PartsDestruction.'.$today.'.csv';
//$filname = 'PD.'.$today.'.csv';
$csv = "entity_id,batch_event_flag,event_id,operator_login_id,controller_login_id,controller_scan_time,destruction_location_id,origin_bin_id,origin_bin_scan_time,origin_next_step_action,serial_id,serial_scan_time,mpn_id,mpn_scan_time,part_type,manufacturer_id,source_type,destruction_type,destruction_datetime,container_id,container_scan_time,next_step_action,next_step_rule_id,workstation_id,workstation_scan_time,destruction_rig_id,destruction_rig_scan_time,storage_location_group_id,coo_id,classification_type,classification_code_id,bin_id,customer_id,post_inventory_type,event_s_duration_value\n";//Column headers

$sql = "Select distinct(A.SerialNumber) as serial_id,'eV-Disposition-1' as entity_id,DH.bulk_transaction_flag as batch_event_flag,DH.bulk_transaction_id as event_id,AU.UserName as operator_login_id,
DH.AuditController as controller_login_id,DH.controller_scan_time as controller_scan_time,DHF.FacilityName as destruction_location_id,
DH.FromBinName as origin_bin_id,DH.origin_bin_scan_time as origin_bin_scan_time,DHD.disposition as origin_next_step_action,
DH.serial_scan_time as serial_scan_time,A.UniversalModelNumber as mpn_id,A.mpn_scan_time as mpn_scan_time,A.part_type as part_type,AM.ManufacturerName as manufacturer_id,
ST.Cumstomertype as source_type,DH.ModuleName as destruction_type,DH.CreatedDate as destruction_datetime,A.ShippingContainerID as container_id,A.origin_container_id_scan_time as container_scan_time,
DHTD.disposition as next_step_action,ABR.rule_id_text as next_step_rule_id,NULL as workstation_id,NULL as workstation_scan_time,
'' as destruction_rig_id,'' as destruction_rig_scan_time,ACPLG.GroupName as storage_location_group_id,ACOO.COO as coo_id,AD.WasteClassificationType as classification_type,
A.WasteCode as classification_code_id,ACP.BinName as bin_id,AWSSC.Customer as customer_id,'' as post_inventory_type,A.recovery_type_scan_time as recovery_type_scan_time
        FROM destruction_history as DH
        LEFT JOIN asset A on A.AssetScanID = DH.AssetScanID
        LEFT JOIN users AU on AU.UserId = DH.CreatedBy
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN facility DHF on DHF.FacilityID = DH.FacilityID
        LEFT JOIN disposition DHD ON DHD.`disposition_id` = DH.`FromDispositionID`
        LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
        LEFT JOIN disposition DHTD ON DHTD.`disposition_id` = DH.`ToDispositionID`
        LEFT JOIN business_rule ABR on ABR.rule_id = A.rule_id
        LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.CustomPalletID
        LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
        WHERE DH.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        AND DH.AssetScanID != ''
        ";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
	/*if($row['FacilityName'] == 'CVG110')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 16 hour'));
	}*/
    $row['post_inventory_type'] = 'Serialized';
    /*if($row['event_id'] != $eventid)
    {
        $i = 0;
    }
    if($i ==0)
    {
        $eventid = $row['event_id'];
        $timeFirst  = strtotime($row['origin_bin_scan_time']);
        $timeSecond = strtotime($row['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    else
    {
        $eventid = $row['event_id'];
        $timeFirst  = $timeSecond;
        $timeSecond = strtotime($row['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    $i = $i+1;
    if($row['batch_event_flag'] == '1')
    {
        $timeFirst  = strtotime($row['origin_bin_scan_time']);
        $timeSecond = strtotime($row['destruction_datetime']);
        $row['mpn_scan_time'] = '';
        $row['serial_scan_time'] = '';
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    else
    {
        $timeFirst  = strtotime($row['controller_scan_time']);
        $timeSecond = strtotime($row['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }*/
    $timeFirst  = strtotime($row['serial_scan_time']);
    $timeSecond = strtotime($row['destruction_datetime']);
    $differenceInSeconds = $timeSecond - $timeFirst;
    if($row['DateUpdated'] == '')
    {
        $row['DateUpdated'] = $row['DateCreated'];
    }
    
    $row['operator_login_id'] = strtolower($row['operator_login_id']);
    $row['entity_id'] = str_replace(","," ",$row['entity_id']);
	$row['serial_id'] = str_replace(","," ",$row['serial_id']);
	$row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
	$row['part_type'] = str_replace(","," ",$row['part_type']);
    $row['manufacturer_id'] = str_replace(","," ",$row['manufacturer_id']);
    $row['source_type'] = str_replace(","," ",$row['source_type']);
    $row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
    $row['bin_id'] = str_replace(","," ",$row['bin_id']);
    $row['container_id'] = str_replace(","," ",$row['container_id']);
    $row['storage_location_group_id'] = str_replace(","," ",$row['storage_location_group_id']);
    $row['coo_id'] = str_replace(","," ",$row['coo_id']);
    $row['classification_type'] = str_replace(","," ",$row['classification_type']);
    $row['classification_code_id'] = str_replace(","," ",$row['classification_code_id']);
    $row['controller_login_id'] = str_replace(","," ",$row['controller_login_id']);
    $row['event_id'] = str_replace(","," ",$row['event_id']);
    $row['customer_id'] = str_replace(","," ",$row['customer_id']);
    
    if($row['entity_id'] == '')
    {
        $row['entity_id'] = 'n/a';
    }
    if($row['batch_event_flag'] == '1')
    {
        $row['batch_event_flag'] = 'Yes';
    }
    else
    {
        $row['batch_event_flag'] = 'No';
    }
    if($row['event_id'] == '')
    {
        $row['event_id'] = 'n/a';
    }
    if($row['operator_login_id'] == '')
    {
        $row['operator_login_id'] = '';
    }
    if($row['controller_login_id'] == '')
    {
        $row['controller_login_id'] = 'n/a';
    }
    if($row['destruction_location_id'] == '')
    {
        $row['destruction_location_id'] = 'n/a';
    }
    if($row['origin_bin_id'] == '')
    {
        $row['origin_bin_id'] = 'n/a';
    }
    if($row['origin_bin_scan_time'] == '')
    {
        $row['origin_bin_scan_time'] = '';
    }
    if($row['origin_next_step_action'] == '')
    {
        $row['origin_next_step_action'] = 'n/a';
    }
    if($row['serial_id'] == '')
    {
        $row['serial_id'] = 'n/a';
    }
    if($row['mpn_id'] == '')
    {
        $row['mpn_id'] = 'n/a';
    }
    if($row['part_type'] == '')
    {
        $row['part_type'] = '';
    }
    if($row['manufacturer_id'] == '')
    {
        $row['manufacturer_id'] = 'n/a';
    }
    if($row['source_type'] == '')
    {
        $row['source_type'] = 'n/a';
    }
    if($row['container_id'] == '')
    {
        $row['container_id'] = 'n/a';
    }
    if($row['next_step_action'] == '')
    {
        $row['next_step_action'] = 'n/a';
    }
    if($row['next_step_rule_id'] == '')
    {
        $row['next_step_rule_id'] = 'n/a';
    }
    if($row['workstation_id'] == '')
    {
        $row['workstation_id'] = 'n/a';
    }
    if($row['destruction_rig_id'] == '')
    {
        $row['destruction_rig_id'] = 'n/a';
    }
    if($row['storage_location_group_id'] == '')
    {
        $row['storage_location_group_id'] = '';
    }
    if($row['coo_id'] == '')
    {
        $row['coo_id'] = 'n/a';
    }
    if($row['classification_type'] == '')
    {
        $row['classification_type'] = '';
    }
    if($row['classification_code_id'] == '')
    {
        $row['classification_code_id'] = 'n/a';
    }
    if($row['bin_id'] == '')
    {
        $row['bin_id'] = 'n/a';
    }
    if($row['customer_id'] == '')
    {
        $row['customer_id'] = 'n/a';
    }
    if($row['post_inventory_type'] == '')
    {
        $row['post_inventory_type'] = 'n/a';
    }
    if($row['controller_scan_time'] != '')
    {
        if($row['controller_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['controller_scan_time'] = date("Y-m-d H:i:s", strtotime($row['controller_scan_time']));
        }
        else
        {
            $row['controller_scan_time'] = '';
        }
    }
    else
    {
        $row['controller_scan_time'] = '';
    }
    if($row['origin_bin_scan_time'] != '')
    {
        if($row['origin_bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['origin_bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row['origin_bin_scan_time']));
        }
        else
        {
            $row['origin_bin_scan_time'] = '';
        }
    }
    else
    {
        $row['origin_bin_scan_time'] = '';
    }
    if($row['serial_scan_time'] != '')
    {
        if($row['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row['serial_scan_time']));
        }
        else
        {
            $row['serial_scan_time'] = '';
        }
    }
    else
    {
        $row['serial_scan_time'] = '';
    }
    if($row['mpn_scan_time'] != '')
    {
        if($row['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row['mpn_scan_time']));
        }
        else
        {
            $row['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['destruction_datetime'] != '')
    {
        if($row['destruction_datetime'] != '0000-00-00 00:00:00')
        {
            $row['destruction_datetime'] = date("Y-m-d H:i:s", strtotime($row['destruction_datetime']));
        }
        else
        {
            $row['destruction_datetime'] = '';
        }
    }
    else
    {
        $row['destruction_datetime'] = '';
    }
    if($row['container_scan_time'] != '')
    {
        if($row['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row['container_scan_time']));
        }
        else
        {
            $row['container_scan_time'] = '';
        }
    }
    else
    {
        $row['container_scan_time'] = '';
    }
    if($row['workstation_scan_time'] != '')
    {
        if($row['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row['workstation_scan_time']));
        }
        else
        {
            $row['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row['workstation_scan_time'] = '';
    }
    if($row['destruction_rig_scan_time'] != '')
    {
        if($row['destruction_rig_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['destruction_rig_scan_time'] = date("Y-m-d H:i:s", strtotime($row['destruction_rig_scan_time']));
        }
        else
        {
            $row['destruction_rig_scan_time'] = '';
        }
    }
    else
    {
        $row['destruction_rig_scan_time'] = '';
    }
    if($row['container_id'] == 'n/a')
    {
        $row['container_scan_time'] = '';
    }
    if($differenceInSeconds < 0)
    {
        $differenceInSeconds = '';
    }
    $row2  = array($row['entity_id'],$row['batch_event_flag'],$row['event_id'],$row['operator_login_id'],$row['controller_login_id'],$row['controller_scan_time'],$row['destruction_location_id'],$row['origin_bin_id'],$row['origin_bin_scan_time'],$row['origin_next_step_action'],$row['serial_id'],$row['serial_scan_time'],$row['mpn_id'],$row['mpn_scan_time'],$row['part_type'],$row['manufacturer_id'],$row['source_type'],$row['destruction_type'],$row['destruction_datetime'],$row['container_id'],$row['container_scan_time'],$row['next_step_action'],$row['next_step_rule_id'],$row['workstation_id'],$row['workstation_scan_time'],$row['destruction_rig_id'],$row['destruction_rig_scan_time'],$row['storage_location_group_id'],$row['coo_id'],$row['classification_type'],$row['classification_code_id'],$row['bin_id'],$row['customer_id'],$row['post_inventory_type'],$differenceInSeconds);
    $rows[] = $row2;
}
foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34]."\n"; //Append data to csv
}

$sql1 = "Select distinct(A.MediaSerialNumber) as serial_id,'eV-Disposition-1' as entity_id,DH.bulk_transaction_flag as batch_event_flag,DH.bulk_transaction_id as event_id,AU.UserName as operator_login_id,
DH.AuditController as controller_login_id,DH.controller_scan_time as controller_scan_time,DHF.FacilityName as destruction_location_id,
DH.FromBinName as origin_bin_id,DH.origin_bin_scan_time as origin_bin_scan_time,DHD.disposition as origin_next_step_action,
DH.serial_scan_time as serial_scan_time,A.MediaMPN as mpn_id,A.mpn_scan_time as mpn_scan_time,APT.parttype as part_type,AM.ManufacturerName as manufacturer_id,
ST.Cumstomertype as source_type,DH.ModuleName as destruction_type,DH.CreatedDate as destruction_datetime,A.ShippingContainerID as container_id,A.origin_container_id_scan_time as container_scan_time,
DHTD.disposition as next_step_action,ABR.rule_id_text as next_step_rule_id,NULL as workstation_id,NULL as workstation_scan_time,
'' as destruction_rig_id,'' as destruction_rig_scan_time,ACPLG.GroupName as storage_location_group_id,ACOO.COO as coo_id,AD.WasteClassificationType as classification_type,
A.WasteCode as classification_code_id,ACP.BinName as bin_id,AWSSC.Customer as customer_id,'' as post_inventory_type,A.recovery_type_scan_time as recovery_type_scan_time
        FROM destruction_history as DH
        LEFT JOIN speed_media_recovery A on A.MediaID = DH.MediaID
        LEFT JOIN parttype APT ON APT.parttypeid = A.parttypeid
        LEFT JOIN users AU on AU.UserId = DH.CreatedBy
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN facility DHF on DHF.FacilityID = DH.FacilityID
        LEFT JOIN disposition DHD ON DHD.`disposition_id` = DH.`FromDispositionID`
        LEFT JOIN catlog_creation ACC ON ACC.mpn_id = A.MediaMPN
        LEFT JOIN manufacturer AM on AM.idManufacturer = ACC.idManufacturer
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
        LEFT JOIN disposition DHTD ON DHTD.`disposition_id` = DH.`ToDispositionID`
        LEFT JOIN business_rule ABR on ABR.rule_id = A.rule_id
        LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.CustomPalletID
        LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
        WHERE DH.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        AND DH.MediaID != ''
        
        ";
$query1 = mysqli_query($connectionlink1,$sql1);
while($row1 = mysqli_fetch_assoc($query1))
{
	/*if($row['FacilityName'] == 'CVG110')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 16 hour'));
	}*/
    $row1['post_inventory_type'] = 'Serialized';
    if($row1['batch_event_flag'] == '1')
    {
        $timeFirst  = strtotime($row1['origin_bin_scan_time']);
        $timeSecond = strtotime($row1['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
        $row1['mpn_scan_time'] = '';
        $row1['serial_scan_time'] = '';
    }
    else
    {
        $timeFirst  = strtotime($row1['controller_scan_time']);
        $timeSecond = strtotime($row1['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    if($row1['DateUpdated'] == '')
    {
        $row1['DateUpdated'] = $row1['DateCreated'];
    }
    
    $row1['operator_login_id'] = strtolower($row1['operator_login_id']);
    $row1['entity_id'] = str_replace(","," ",$row1['entity_id']);
	$row1['serial_id'] = str_replace(","," ",$row1['serial_id']);
	$row1['mpn_id'] = str_replace(","," ",$row1['mpn_id']);
	$row1['part_type'] = str_replace(","," ",$row1['part_type']);
    $row1['manufacturer_id'] = str_replace(","," ",$row1['manufacturer_id']);
    $row1['source_type'] = str_replace(","," ",$row1['source_type']);
    $row1['operator_login_id'] = str_replace(","," ",$row1['operator_login_id']);
    $row1['bin_id'] = str_replace(","," ",$row1['bin_id']);
    $row1['container_id'] = str_replace(","," ",$row1['container_id']);
    $row1['storage_location_group_id'] = str_replace(","," ",$row1['storage_location_group_id']);
    $row1['coo_id'] = str_replace(","," ",$row1['coo_id']);
    $row1['classification_type'] = str_replace(","," ",$row1['classification_type']);
    $row1['classification_code_id'] = str_replace(","," ",$row1['classification_code_id']);
    $row1['controller_login_id'] = str_replace(","," ",$row1['controller_login_id']);
    $row1['event_id'] = str_replace(","," ",$row1['event_id']);
    $row1['customer_id'] = str_replace(","," ",$row1['customer_id']);
    
    if($row1['entity_id'] == '')
    {
        $row1['entity_id'] = 'n/a';
    }
    if($row1['batch_event_flag'] == '1')
    {
        $row1['batch_event_flag'] = 'Yes';
    }
    else
    {
        $row1['batch_event_flag'] = 'No';
    }
    if($row1['event_id'] == '')
    {
        $row1['event_id'] = 'n/a';
    }
    if($row1['operator_login_id'] == '')
    {
        $row1['operator_login_id'] = '';
    }
    if($row1['controller_login_id'] == '')
    {
        $row1['controller_login_id'] = 'n/a';
    }
    if($row1['destruction_location_id'] == '')
    {
        $row1['destruction_location_id'] = 'n/a';
    }
    if($row1['origin_bin_id'] == '')
    {
        $row1['origin_bin_id'] = 'n/a';
    }
    if($row1['origin_bin_scan_time'] == '')
    {
        $row1['origin_bin_scan_time'] = '';
    }
    if($row1['origin_next_step_action'] == '')
    {
        $row1['origin_next_step_action'] = 'n/a';
    }
    if($row1['serial_id'] == '')
    {
        $row1['serial_id'] = 'n/a';
    }
    if($row1['mpn_id'] == '')
    {
        $row1['mpn_id'] = 'n/a';
    }
    if($row1['part_type'] == '')
    {
        $row1['part_type'] = '';
    }
    if($row1['manufacturer_id'] == '')
    {
        $row1['manufacturer_id'] = 'n/a';
    }
    if($row1['source_type'] == '')
    {
        $row1['source_type'] = 'n/a';
    }
    if($row1['container_id'] == '')
    {
        $row1['container_id'] = 'n/a';
    }
    if($row1['next_step_action'] == '')
    {
        $row1['next_step_action'] = 'n/a';
    }
    if($row1['next_step_rule_id'] == '')
    {
        $row1['next_step_rule_id'] = 'n/a';
    }
    if($row1['workstation_id'] == '')
    {
        $row1['workstation_id'] = 'n/a';
    }
    if($row1['destruction_rig_id'] == '')
    {
        $row1['destruction_rig_id'] = 'n/a';
    }
    if($row1['storage_location_group_id'] == '')
    {
        $row1['storage_location_group_id'] = '';
    }
    if($row1['coo_id'] == '')
    {
        $row1['coo_id'] = 'n/a';
    }
    if($row1['classification_type'] == '')
    {
        $row1['classification_type'] = '';
    }
    if($row1['classification_code_id'] == '')
    {
        $row1['classification_code_id'] = 'n/a';
    }
    if($row1['bin_id'] == '')
    {
        $row1['bin_id'] = 'n/a';
    }
    if($row1['customer_id'] == '')
    {
        $row1['customer_id'] = 'n/a';
    }
    if($row1['post_inventory_type'] == '')
    {
        $row1['post_inventory_type'] = 'n/a';
    }
    if($row1['controller_scan_time'] != '')
    {
        if($row1['controller_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['controller_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['controller_scan_time']));
        }
        else
        {
            $row1['controller_scan_time'] = '';
        }
    }
    else
    {
        $row1['controller_scan_time'] = '';
    }
    if($row1['origin_bin_scan_time'] != '')
    {
        if($row1['origin_bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['origin_bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['origin_bin_scan_time']));
        }
        else
        {
            $row1['origin_bin_scan_time'] = '';
        }
    }
    else
    {
        $row1['origin_bin_scan_time'] = '';
    }
    if($row1['serial_scan_time'] != '')
    {
        if($row1['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['serial_scan_time']));
        }
        else
        {
            $row1['serial_scan_time'] = '';
        }
    }
    else
    {
        $row1['serial_scan_time'] = '';
    }
    if($row1['mpn_scan_time'] != '')
    {
        if($row1['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['mpn_scan_time']));
        }
        else
        {
            $row1['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row1['mpn_scan_time'] = '';
    }
    if($row1['destruction_datetime'] != '')
    {
        if($row1['destruction_datetime'] != '0000-00-00 00:00:00')
        {
            $row1['destruction_datetime'] = date("Y-m-d H:i:s", strtotime($row1['destruction_datetime']));
        }
        else
        {
            $row1['destruction_datetime'] = '';
        }
    }
    else
    {
        $row1['destruction_datetime'] = '';
    }
    if($row1['container_scan_time'] != '')
    {
        if($row1['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['container_scan_time']));
        }
        else
        {
            $row1['container_scan_time'] = '';
        }
    }
    else
    {
        $row1['container_scan_time'] = '';
    }
    if($row1['workstation_scan_time'] != '')
    {
        if($row1['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['workstation_scan_time']));
        }
        else
        {
            $row1['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row1['workstation_scan_time'] = '';
    }
    if($row1['destruction_rig_scan_time'] != '')
    {
        if($row1['destruction_rig_scan_time'] != '0000-00-00 00:00:00')
        {
            $row1['destruction_rig_scan_time'] = date("Y-m-d H:i:s", strtotime($row1['destruction_rig_scan_time']));
        }
        else
        {
            $row1['destruction_rig_scan_time'] = '';
        }
    }
    else
    {
        $row1['destruction_rig_scan_time'] = '';
    }
    if($row1['container_id'] == 'n/a')
    {
        $row1['container_scan_time'] = '';
    }
    if($differenceInSeconds < 0)
    {
        $differenceInSeconds = '';
    }
    $row12  = array($row1['entity_id'],$row1['batch_event_flag'],$row1['event_id'],$row1['operator_login_id'],$row1['controller_login_id'],$row1['controller_scan_time'],$row1['destruction_location_id'],$row1['origin_bin_id'],$row1['origin_bin_scan_time'],$row1['origin_next_step_action'],$row1['serial_id'],$row1['serial_scan_time'],$row1['mpn_id'],$row1['mpn_scan_time'],$row1['part_type'],$row1['manufacturer_id'],$row1['source_type'],$row1['destruction_type'],$row1['destruction_datetime'],$row1['container_id'],$row1['container_scan_time'],$row1['next_step_action'],$row1['next_step_rule_id'],$row1['workstation_id'],$row1['workstation_scan_time'],$row1['destruction_rig_id'],$row1['destruction_rig_scan_time'],$row1['storage_location_group_id'],$row1['coo_id'],$row1['classification_type'],$row1['classification_code_id'],$row1['bin_id'],$row1['customer_id'],$row1['post_inventory_type'],$differenceInSeconds);
    $row1s[] = $row12;
}
foreach ($row1s as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34]."\n"; //Append data to csv
}

$sql2 = "Select distinct(A.ServerSerialNumber) as serial_id,'eV-Disposition-1' as entity_id,DH.bulk_transaction_flag as batch_event_flag,DH.bulk_transaction_id as event_id,AU.UserName as operator_login_id,
DH.AuditController as controller_login_id,DH.controller_scan_time as controller_scan_time,DHF.FacilityName as destruction_location_id,
DH.FromBinName as origin_bin_id,DH.origin_bin_scan_time as origin_bin_scan_time,DHD.disposition as origin_next_step_action,
DH.serial_scan_time as serial_scan_time,A.MPN as mpn_id,A.mpn_scan_time as mpn_scan_time,APT.parttype as part_type,AM.ManufacturerName as manufacturer_id,
ST.Cumstomertype as source_type,DH.ModuleName as destruction_type,DH.CreatedDate as destruction_datetime,A.ShippingContainerID as container_id,A.origin_container_id_scan_time as container_scan_time,
DHTD.disposition as next_step_action,ABR.rule_id_text as next_step_rule_id,NULL as workstation_id,NULL as workstation_scan_time,
'' as destruction_rig_id,'' as destruction_rig_scan_time,ACPLG.GroupName as storage_location_group_id,ACOO.COO as coo_id,AD.WasteClassificationType as classification_type,
A.WasteCode as classification_code_id,ACP.BinName as bin_id,AWSSC.Customer as customer_id,'' as post_inventory_type,A.recovery_type_scan_time as recovery_type_scan_time
        FROM destruction_history as DH
        LEFT JOIN speed_server_recovery A on A.ServerID = DH.ServerID
        LEFT JOIN parttype APT ON APT.parttypeid = A.parttypeid
        LEFT JOIN users AU on AU.UserId = DH.CreatedBy
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN facility DHF on DHF.FacilityID = DH.FacilityID
        LEFT JOIN disposition DHD ON DHD.`disposition_id` = DH.`FromDispositionID`
        LEFT JOIN catlog_creation ACC ON ACC.mpn_id = A.MPN
        LEFT JOIN manufacturer AM on AM.idManufacturer = ACC.idManufacturer
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
        LEFT JOIN disposition DHTD ON DHTD.`disposition_id` = DH.`ToDispositionID`
        LEFT JOIN business_rule ABR on ABR.rule_id = A.rule_id
        LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.CustomPalletID
        LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
        WHERE DH.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        AND DH.ServerID != ''
        
        ";
$query2 = mysqli_query($connectionlink1,$sql2);
while($row2 = mysqli_fetch_assoc($query2))
{
	/*if($row['FacilityName'] == 'CVG110')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 16 hour'));
	}*/
    $row2['post_inventory_type'] = 'Serialized';
    if($row2['batch_event_flag'] == '1')
    {
        $timeFirst  = strtotime($row2['origin_bin_scan_time']);
        $timeSecond = strtotime($row2['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
        $row2['mpn_scan_time'] = '';
        $row2['serial_scan_time'] = '';
    }
    else
    {
        $timeFirst  = strtotime($row2['controller_scan_time']);
        $timeSecond = strtotime($row2['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    if($row2['DateUpdated'] == '')
    {
        $row2['DateUpdated'] = $row2['DateCreated'];
    }
    
    $row2['operator_login_id'] = strtolower($row2['operator_login_id']);
    $row2['entity_id'] = str_replace(","," ",$row2['entity_id']);
	$row2['serial_id'] = str_replace(","," ",$row2['serial_id']);
	$row2['mpn_id'] = str_replace(","," ",$row2['mpn_id']);
	$row2['part_type'] = str_replace(","," ",$row2['part_type']);
    $row2['manufacturer_id'] = str_replace(","," ",$row2['manufacturer_id']);
    $row2['source_type'] = str_replace(","," ",$row2['source_type']);
    $row2['operator_login_id'] = str_replace(","," ",$row2['operator_login_id']);
    $row2['bin_id'] = str_replace(","," ",$row2['bin_id']);
    $row2['container_id'] = str_replace(","," ",$row2['container_id']);
    $row2['storage_location_group_id'] = str_replace(","," ",$row2['storage_location_group_id']);
    $row2['coo_id'] = str_replace(","," ",$row2['coo_id']);
    $row2['classification_type'] = str_replace(","," ",$row2['classification_type']);
    $row2['classification_code_id'] = str_replace(","," ",$row2['classification_code_id']);
    $row2['controller_login_id'] = str_replace(","," ",$row2['controller_login_id']);
    $row2['event_id'] = str_replace(","," ",$row2['event_id']);
    $row2['customer_id'] = str_replace(","," ",$row2['customer_id']);
    
    if($row2['entity_id'] == '')
    {
        $row2['entity_id'] = 'n/a';
    }
    if($row2['batch_event_flag'] == '1')
    {
        $row2['batch_event_flag'] = 'Yes';
    }
    else
    {
        $row2['batch_event_flag'] = 'No';
    }
    if($row2['event_id'] == '')
    {
        $row2['event_id'] = 'n/a';
    }
    if($row2['operator_login_id'] == '')
    {
        $row2['operator_login_id'] = '';
    }
    if($row2['controller_login_id'] == '')
    {
        $row2['controller_login_id'] = 'n/a';
    }
    if($row2['destruction_location_id'] == '')
    {
        $row2['destruction_location_id'] = 'n/a';
    }
    if($row2['origin_bin_id'] == '')
    {
        $row2['origin_bin_id'] = 'n/a';
    }
    if($row2['origin_bin_scan_time'] == '')
    {
        $row2['origin_bin_scan_time'] = '';
    }
    if($row2['origin_next_step_action'] == '')
    {
        $row2['origin_next_step_action'] = 'n/a';
    }
    if($row2['serial_id'] == '')
    {
        $row2['serial_id'] = 'n/a';
    }
    if($row2['mpn_id'] == '')
    {
        $row2['mpn_id'] = 'n/a';
    }
    if($row2['part_type'] == '')
    {
        $row2['part_type'] = '';
    }
    if($row2['manufacturer_id'] == '')
    {
        $row2['manufacturer_id'] = 'n/a';
    }
    if($row2['source_type'] == '')
    {
        $row2['source_type'] = 'n/a';
    }
    if($row2['container_id'] == '')
    {
        $row2['container_id'] = 'n/a';
    }
    if($row2['next_step_action'] == '')
    {
        $row2['next_step_action'] = 'n/a';
    }
    if($row2['next_step_rule_id'] == '')
    {
        $row2['next_step_rule_id'] = 'n/a';
    }
    if($row2['workstation_id'] == '')
    {
        $row2['workstation_id'] = 'n/a';
    }
    if($row2['destruction_rig_id'] == '')
    {
        $row2['destruction_rig_id'] = 'n/a';
    }
    if($row2['storage_location_group_id'] == '')
    {
        $row2['storage_location_group_id'] = '';
    }
    if($row2['coo_id'] == '')
    {
        $row2['coo_id'] = 'n/a';
    }
    if($row2['classification_type'] == '')
    {
        $row2['classification_type'] = '';
    }
    if($row2['classification_code_id'] == '')
    {
        $row2['classification_code_id'] = 'n/a';
    }
    if($row2['bin_id'] == '')
    {
        $row2['bin_id'] = 'n/a';
    }
    if($row2['customer_id'] == '')
    {
        $row2['customer_id'] = 'n/a';
    }
    if($row2['post_inventory_type'] == '')
    {
        $row2['post_inventory_type'] = 'n/a';
    }
    if($row2['controller_scan_time'] != '')
    {
        if($row2['controller_scan_time'] != '0000-00-00 00:00:00')
        {
            $row2['controller_scan_time'] = date("Y-m-d H:i:s", strtotime($row2['controller_scan_time']));
        }
        else
        {
            $row2['controller_scan_time'] = '';
        }
    }
    else
    {
        $row2['controller_scan_time'] = '';
    }
    if($row2['origin_bin_scan_time'] != '')
    {
        if($row2['origin_bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row2['origin_bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row2['origin_bin_scan_time']));
        }
        else
        {
            $row2['origin_bin_scan_time'] = '';
        }
    }
    else
    {
        $row2['origin_bin_scan_time'] = '';
    }
    if($row2['serial_scan_time'] != '')
    {
        if($row2['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row2['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row2['serial_scan_time']));
        }
        else
        {
            $row2['serial_scan_time'] = '';
        }
    }
    else
    {
        $row2['serial_scan_time'] = '';
    }
    if($row2['mpn_scan_time'] != '')
    {
        if($row2['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row2['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row2['mpn_scan_time']));
        }
        else
        {
            $row2['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row2['mpn_scan_time'] = '';
    }
    if($row2['destruction_datetime'] != '')
    {
        if($row2['destruction_datetime'] != '0000-00-00 00:00:00')
        {
            $row2['destruction_datetime'] = date("Y-m-d H:i:s", strtotime($row2['destruction_datetime']));
        }
        else
        {
            $row2['destruction_datetime'] = '';
        }
    }
    else
    {
        $row2['destruction_datetime'] = '';
    }
    if($row2['container_scan_time'] != '')
    {
        if($row2['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row2['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row2['container_scan_time']));
        }
        else
        {
            $row2['container_scan_time'] = '';
        }
    }
    else
    {
        $row2['container_scan_time'] = '';
    }
    if($row2['workstation_scan_time'] != '')
    {
        if($row2['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row2['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row2['workstation_scan_time']));
        }
        else
        {
            $row2['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row2['workstation_scan_time'] = '';
    }
    if($row2['destruction_rig_scan_time'] != '')
    {
        if($row2['destruction_rig_scan_time'] != '0000-00-00 00:00:00')
        {
            $row2['destruction_rig_scan_time'] = date("Y-m-d H:i:s", strtotime($row2['destruction_rig_scan_time']));
        }
        else
        {
            $row2['destruction_rig_scan_time'] = '';
        }
    }
    else
    {
        $row2['destruction_rig_scan_time'] = '';
    }
    if($row2['container_id'] == 'n/a')
    {
        $row2['container_scan_time'] = '';
    }
    if($differenceInSeconds < 0)
    {
        $differenceInSeconds = '';
    }
    $row22  = array($row2['entity_id'],$row2['batch_event_flag'],$row2['event_id'],$row2['operator_login_id'],$row2['controller_login_id'],$row2['controller_scan_time'],$row2['destruction_location_id'],$row2['origin_bin_id'],$row2['origin_bin_scan_time'],$row2['origin_next_step_action'],$row2['serial_id'],$row2['serial_scan_time'],$row2['mpn_id'],$row2['mpn_scan_time'],$row2['part_type'],$row2['manufacturer_id'],$row2['source_type'],$row2['destruction_type'],$row2['destruction_datetime'],$row2['container_id'],$row2['container_scan_time'],$row2['next_step_action'],$row2['next_step_rule_id'],$row2['workstation_id'],$row2['workstation_scan_time'],$row2['destruction_rig_id'],$row2['destruction_rig_scan_time'],$row2['storage_location_group_id'],$row2['coo_id'],$row2['classification_type'],$row2['classification_code_id'],$row2['bin_id'],$row2['customer_id'],$row2['post_inventory_type'],$differenceInSeconds);
    $row2s[] = $row22;
}
foreach ($row2s as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34]."\n"; //Append data to csv
}

$sql3 = "Select distinct(A.MediaSerialNumber) as serial_id,'eV-Disposition-1' as entity_id,0 as batch_event_flag,DH.ProcessID as event_id,AU.UserName as operator_login_id,
DH.AuditControllerID as controller_login_id,'' as controller_scan_time,DHF.FacilityName as destruction_location_id,
FACP.BinName as origin_bin_id,'' as origin_bin_scan_time,DHD.disposition as origin_next_step_action,
'' as serial_scan_time,A.MediaMPN as mpn_id,A.mpn_scan_time as mpn_scan_time,APT.parttype as part_type,AM.ManufacturerName as manufacturer_id,
ST.Cumstomertype as source_type,DHD.disposition as destruction_type,DH.CreatedDate as destruction_datetime,A.ShippingContainerID as container_id,A.origin_container_id_scan_time as container_scan_time,
DHTD.disposition as next_step_action,ABR.rule_id_text as next_step_rule_id,NULL as workstation_id,NULL as workstation_scan_time,
'' as destruction_rig_id,'' as destruction_rig_scan_time,ACPLG.GroupName as storage_location_group_id,ACOO.COO as coo_id,AD.WasteClassificationType as classification_type,
A.WasteCode as classification_code_id,ACP.BinName as bin_id,AWSSC.Customer as customer_id,'' as post_inventory_type,A.recovery_type_scan_time as recovery_type_scan_time
        FROM speed_media_process as DH
        LEFT JOIN speed_media_recovery A ON A.MediaSerialNumber = DH.MediaSerialNumber
        LEFT JOIN parttype APT ON APT.parttypeid = A.parttypeid
        LEFT JOIN users AU on AU.UserId = DH.CreatedBy
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN facility DHF on DHF.FacilityID = A.FacilityID
        LEFT JOIN disposition DHD ON DHD.`disposition_id` = DH.`from_disposition_id`
        LEFT JOIN catlog_creation ACC ON ACC.mpn_id = A.MediaMPN
        LEFT JOIN manufacturer AM on AM.idManufacturer = ACC.idManufacturer
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
        LEFT JOIN disposition DHTD ON DHTD.`disposition_id` = DH.`to_disposition_id`
        LEFT JOIN business_rule ABR on ABR.rule_id = A.rule_id
        LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.CustomPalletID
        LEFT JOIN custompallet FACP on FACP.CustomPalletID = DH.from_CustomPalletID
        LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
        WHERE DH.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        AND DH.to_status = 3
        
        ";
$query3 = mysqli_query($connectionlink1,$sql3);
while($row3 = mysqli_fetch_assoc($query3))
{
	/*if($row['FacilityName'] == 'CVG110')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 16 hour'));
	}*/
    $row3['post_inventory_type'] = 'Serialized';
    if($row3['batch_event_flag'] == '1')
    {
        $timeFirst  = strtotime($row3['origin_bin_scan_time']);
        $timeSecond = strtotime($row3['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
        $row3['mpn_scan_time'] = '';
        $row3['serial_scan_time'] = '';
    }
    else
    {
        $timeFirst  = strtotime($row3['controller_scan_time']);
        $timeSecond = strtotime($row3['destruction_datetime']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    if($row3['DateUpdated'] == '')
    {
        $row3['DateUpdated'] = $row3['DateCreated'];
    }
    
    $row3['operator_login_id'] = strtolower($row3['operator_login_id']);
    $row3['entity_id'] = str_replace(","," ",$row3['entity_id']);
	$row3['serial_id'] = str_replace(","," ",$row3['serial_id']);
	$row3['mpn_id'] = str_replace(","," ",$row3['mpn_id']);
	$row3['part_type'] = str_replace(","," ",$row3['part_type']);
    $row3['manufacturer_id'] = str_replace(","," ",$row3['manufacturer_id']);
    $row3['source_type'] = str_replace(","," ",$row3['source_type']);
    $row3['operator_login_id'] = str_replace(","," ",$row3['operator_login_id']);
    $row3['bin_id'] = str_replace(","," ",$row3['bin_id']);
    $row3['container_id'] = str_replace(","," ",$row3['container_id']);
    $row3['storage_location_group_id'] = str_replace(","," ",$row3['storage_location_group_id']);
    $row3['coo_id'] = str_replace(","," ",$row3['coo_id']);
    $row3['classification_type'] = str_replace(","," ",$row3['classification_type']);
    $row3['classification_code_id'] = str_replace(","," ",$row3['classification_code_id']);
    $row3['controller_login_id'] = str_replace(","," ",$row3['controller_login_id']);
    $row3['event_id'] = str_replace(","," ",$row3['event_id']);
    $row3['customer_id'] = str_replace(","," ",$row3['customer_id']);
    
    if($row3['entity_id'] == '')
    {
        $row3['entity_id'] = 'n/a';
    }
    if($row3['batch_event_flag'] == '1')
    {
        $row3['batch_event_flag'] = 'Yes';
    }
    else
    {
        $row3['batch_event_flag'] = 'No';
    }
    if($row3['event_id'] == '')
    {
        $row3['event_id'] = 'n/a';
    }
    if($row3['operator_login_id'] == '')
    {
        $row3['operator_login_id'] = '';
    }
    if($row3['controller_login_id'] == '')
    {
        $row3['controller_login_id'] = 'n/a';
    }
    if($row3['destruction_location_id'] == '')
    {
        $row3['destruction_location_id'] = 'n/a';
    }
    if($row3['origin_bin_id'] == '')
    {
        $row3['origin_bin_id'] = 'n/a';
    }
    if($row3['origin_bin_scan_time'] == '')
    {
        $row3['origin_bin_scan_time'] = '';
    }
    if($row3['origin_next_step_action'] == '')
    {
        $row3['origin_next_step_action'] = 'n/a';
    }
    if($row3['serial_id'] == '')
    {
        $row3['serial_id'] = 'n/a';
    }
    if($row3['mpn_id'] == '')
    {
        $row3['mpn_id'] = 'n/a';
    }
    if($row3['part_type'] == '')
    {
        $row3['part_type'] = '';
    }
    if($row3['manufacturer_id'] == '')
    {
        $row3['manufacturer_id'] = 'n/a';
    }
    if($row3['source_type'] == '')
    {
        $row3['source_type'] = 'n/a';
    }
    if($row3['container_id'] == '')
    {
        $row3['container_id'] = 'n/a';
    }
    if($row3['next_step_action'] == '')
    {
        $row3['next_step_action'] = 'n/a';
    }
    if($row3['next_step_rule_id'] == '')
    {
        $row3['next_step_rule_id'] = 'n/a';
    }
    if($row3['workstation_id'] == '')
    {
        $row3['workstation_id'] = 'n/a';
    }
    if($row3['destruction_rig_id'] == '')
    {
        $row3['destruction_rig_id'] = 'n/a';
    }
    if($row3['storage_location_group_id'] == '')
    {
        $row3['storage_location_group_id'] = '';
    }
    if($row3['coo_id'] == '')
    {
        $row3['coo_id'] = 'n/a';
    }
    if($row3['classification_type'] == '')
    {
        $row3['classification_type'] = '';
    }
    if($row3['classification_code_id'] == '')
    {
        $row3['classification_code_id'] = 'n/a';
    }
    if($row3['bin_id'] == '')
    {
        $row3['bin_id'] = 'n/a';
    }
    if($row3['customer_id'] == '')
    {
        $row3['customer_id'] = 'n/a';
    }
    if($row3['post_inventory_type'] == '')
    {
        $row3['post_inventory_type'] = 'n/a';
    }
    if($row3['controller_scan_time'] != '')
    {
        if($row3['controller_scan_time'] != '0000-00-00 00:00:00')
        {
            $row3['controller_scan_time'] = date("Y-m-d H:i:s", strtotime($row3['controller_scan_time']));
        }
        else
        {
            $row3['controller_scan_time'] = '';
        }
    }
    else
    {
        $row3['controller_scan_time'] = '';
    }
    if($row3['origin_bin_scan_time'] != '')
    {
        if($row3['origin_bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row3['origin_bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row3['origin_bin_scan_time']));
        }
        else
        {
            $row3['origin_bin_scan_time'] = '';
        }
    }
    else
    {
        $row3['origin_bin_scan_time'] = '';
    }
    if($row3['serial_scan_time'] != '')
    {
        if($row3['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row3['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row3['serial_scan_time']));
        }
        else
        {
            $row3['serial_scan_time'] = '';
        }
    }
    else
    {
        $row3['serial_scan_time'] = '';
    }
    if($row3['mpn_scan_time'] != '')
    {
        if($row3['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row3['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row3['mpn_scan_time']));
        }
        else
        {
            $row3['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row3['mpn_scan_time'] = '';
    }
    if($row3['destruction_datetime'] != '')
    {
        if($row3['destruction_datetime'] != '0000-00-00 00:00:00')
        {
            $row3['destruction_datetime'] = date("Y-m-d H:i:s", strtotime($row3['destruction_datetime']));
        }
        else
        {
            $row3['destruction_datetime'] = '';
        }
    }
    else
    {
        $row3['destruction_datetime'] = '';
    }
    if($row3['container_scan_time'] != '')
    {
        if($row3['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row3['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row3['container_scan_time']));
        }
        else
        {
            $row3['container_scan_time'] = '';
        }
    }
    else
    {
        $row3['container_scan_time'] = '';
    }
    if($row3['workstation_scan_time'] != '')
    {
        if($row3['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row3['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row3['workstation_scan_time']));
        }
        else
        {
            $row3['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row3['workstation_scan_time'] = '';
    }
    if($row3['destruction_rig_scan_time'] != '')
    {
        if($row3['destruction_rig_scan_time'] != '0000-00-00 00:00:00')
        {
            $row3['destruction_rig_scan_time'] = date("Y-m-d H:i:s", strtotime($row3['destruction_rig_scan_time']));
        }
        else
        {
            $row3['destruction_rig_scan_time'] = '';
        }
    }
    else
    {
        $row3['destruction_rig_scan_time'] = '';
    }
    if($row3['container_id'] == 'n/a')
    {
        $row3['container_scan_time'] = '';
    }
    if($differenceInSeconds < 0)
    {
        $differenceInSeconds = '';
    }
    $row32  = array($row3['entity_id'],$row3['batch_event_flag'],$row3['event_id'],$row3['operator_login_id'],$row3['controller_login_id'],$row3['controller_scan_time'],$row3['destruction_location_id'],$row3['origin_bin_id'],$row3['origin_bin_scan_time'],$row3['origin_next_step_action'],$row3['serial_id'],$row3['serial_scan_time'],$row3['mpn_id'],$row3['mpn_scan_time'],$row3['part_type'],$row3['manufacturer_id'],$row3['source_type'],$row3['destruction_type'],$row3['destruction_datetime'],$row3['container_id'],$row3['container_scan_time'],$row3['next_step_action'],$row3['next_step_rule_id'],$row3['workstation_id'],$row3['workstation_scan_time'],$row3['destruction_rig_id'],$row3['destruction_rig_scan_time'],$row3['storage_location_group_id'],$row3['coo_id'],$row3['classification_type'],$row3['classification_code_id'],$row3['bin_id'],$row3['customer_id'],$row3['post_inventory_type'],$differenceInSeconds);
    $row3s[] = $row32;
}
foreach ($row3s as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink1,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>