<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'AdvanceShippingNotice.'.$today.'.csv';

$csv = "entity_id,location_id,serial_id,mpn_id,ipn_id,part_type,manufacturer_id,source_type,rack_asset_id,rack_ipn_id,origin_location_id,origin_ticket_id,origin_container_id,origin_seal_id,container_type,container_weight_value,container_weight_unit,material_type,pof_flag,classification_type,upload_datetime,upload_type\n";//Column headers
$sql = "Select AA.SerialNumber as serial_id,F.FacilityName as location_id,AA.UniversalModelNumber as mpn_id,AA.apn_id as ipn_id,AA.part_type,
		M.ManufacturerName as manufacturer_id,ST.Cumstomertype as source_id,P.idPallet as rack_asset_id,P.rackIpn as rack_ipn_id,SC.CustomerName as origin_location_id,
		P.LoadId as origin_ticket_id,P.idPallet as origin_container_id,P.SealNo1,P.SealNo2,P.SealNo3,P.SealNo4,PPK.packageName as container_type,
		P.pallet_netweight as container_weight_value,F.WeightUnit as container_weight_unit,P.MaterialType as material_type,AA.CreatedDate,P.POF as pof_flag,
		P.WasteClassificationType as classification_type,AA.Type as upload_type
		FROM asn_assets AA
		LEFT JOIN pallets P on P.idPallet = AA.idPallet
		LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
		LEFT JOIN manufacturer M ON M.idManufacturer = AA.idManufacturer
		LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
		LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
		LEFT JOIN package PPK on PPK.idPackage = P.idPackage
		WHERE AA.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
		group by AA.SerialNumber";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
	if($row['location_id'] != '')
	{
		$date1 = explode(" ",$row['CreatedDate']);
		$date2 = explode("-",$date1[0]);
		$date = $date2[1]."/".$date2[2]."/".$date2[0];
		$time = date("g:i:s A", strtotime($row['CreatedDate']));
		if($row['CreatedDate'] != '')
		{
			$row['CreatedDate'] = date("Y-m-d H:i:s", strtotime($row['CreatedDate']));
		}
		else
		{
			$row['CreatedDate'] = '';
		}
		$row['SealNo1'] = str_replace(","," ",$row['SealNo1']);
		$row['SealNo2'] = str_replace(","," ",$row['SealNo2']);
		$row['SealNo3'] = str_replace(","," ",$row['SealNo3']);
		$row['SealNo4'] = str_replace(","," ",$row['SealNo4']);
		$orginseal = $row['SealNo1']."/".$row['SealNo2']."/".$row['SealNo3']."/".$row['SealNo4'];
		$row['location_id'] = str_replace(","," ",$row['location_id']);
		$row['serial_id'] = str_replace(","," ",$row['serial_id']);
		$row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
		$row['ipn_id'] = str_replace(","," ",$row['ipn_id']);
		$row['part_type'] = str_replace(","," ",$row['part_type']);
		$row['manufacturer_id'] = str_replace(","," ",$row['manufacturer_id']);
		$row['source_id'] = str_replace(","," ",$row['source_id']);
		$row['rack_asset_id'] = str_replace(","," ",$row['rack_asset_id']);
		$row['rack_ipn_id'] = str_replace(","," ",$row['rack_ipn_id']);
		$row['origin_location_id'] = str_replace(","," ",$row['origin_location_id']);
		$row['origin_ticket_id'] = str_replace(","," ",$row['origin_ticket_id']);
		$row['origin_container_id'] = str_replace(","," ",$row['origin_container_id']);
		$row['origin_seal_id'] = str_replace(","," ",$row['origin_seal_id']);
		$row['container_type'] = str_replace(","," ",$row['container_type']);
		$row['container_weight_value'] = str_replace(","," ",$row['container_weight_value']);
		$row['container_weight_unit'] = str_replace(","," ",$row['container_weight_unit']);
		$row['material_type'] = str_replace(","," ",$row['material_type']);
		$row['classification_type'] = str_replace(","," ",$row['classification_type']);
		
		if($row['pof_flag'] == '1')
		{
			$row['pof_flag'] = 'Yes';
		}
		else
		{
			$row['pof_flag'] = 'No';
		}
		if($row['location_id'] == '')
		{
			$row['location_id'] = 'n/a';
		}
		if($row['serial_id'] == '')
		{
			$row['serial_id'] = 'n/a';
		}
		if($row['mpn_id'] == '')
		{
			$row['mpn_id'] = 'n/a';
		}
		if($row['ipn_id'] == '')
		{
			$row['ipn_id'] = 'n/a';
		}
		if($row['part_type'] == '')
		{
			$row['part_type'] = 'n/a';
		}
		if($row['manufacturer_id'] == '')
		{
			$row['manufacturer_id'] = 'n/a';
		}
		if($row['source_id'] == '')
		{
			$row['source_id'] = 'n/a';
		}
		if($row['rack_asset_id'] == '')
		{
			$row['rack_asset_id'] = 'n/a';
		}
		if($row['rack_ipn_id'] == '')
		{
			$row['rack_ipn_id'] = 'n/a';
		}
		if($row['origin_location_id'] == '')
		{
			$row['origin_location_id'] = 'n/a';
		}
		if($row['origin_ticket_id'] == '')
		{
			$row['origin_ticket_id'] = 'n/a';
		}
		if($row['origin_container_id'] == '')
		{
			$row['origin_container_id'] = 'n/a';
		}
		if($row['origin_seal_id'] == '')
		{
			$row['origin_seal_id'] = 'n/a';
		}
		if($row['container_type'] == '')
		{
			$row['container_type'] = 'n/a';
		}
		if($row['container_weight_value'] == '')
		{
			$row['container_weight_value'] = 'n/a';
		}
		if($row['container_weight_unit'] == '')
		{
			$row['container_weight_unit'] = 'n/a';
		}
		if($row['material_type'] == '')
		{
			$row['material_type'] = 'n/a';
		}
		if($row['classification_type'] == '')
		{
			$row['classification_type'] = 'n/a';
		}
		if($row['pof_flag'] == '')
		{
			$row['pof_flag'] = 'n/a';
		}
		if($row['upload_type'] == 'File Upload')
		{
			$row['upload_type'] = 'Manual File';
		}
		else
		{
			$row['upload_type'] = 'EDI';
		}
		$row2  = array('eV-Disposition-1',$row['location_id'],$row['serial_id'],$row['mpn_id'],$row['ipn_id'],$row['part_type'],$row['manufacturer_id'],$row['source_id'],$row['rack_asset_id'],$row['rack_ipn_id'],$row['origin_location_id'],$row['origin_ticket_id'],$row['origin_container_id'],$orginseal,$row['container_type'],$row['container_weight_value'],$row['container_weight_unit'],$row['material_type'],$row['pof_flag'],$row['classification_type'],$row['CreatedDate'],$row['upload_type']);
		$rows[] = $row2;
	}
    
}

foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink1,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>