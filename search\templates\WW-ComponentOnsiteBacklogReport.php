<?php
set_time_limit(24000);
ini_set('post_max_size', '2000M');
ini_set('memory_limit', '2000M');
ini_set('max_execution_time', 30000);
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'ComponentOnsiteBacklogReport.'.$today.'.csv';
$csv = "serial_id,mpn_id,part_type,manufacturer_id,source_id,origin_location_id,origin_ticket_id,origin_container_id,origin_seal_id,material_type,container_type,container_weight_value,container_receive_login_id,container_receive_date,receive_location_id,storage_location_id\n";//Column headers
$sql = "Select distinct(A.SerialNumber) as SerialNumber,A.UniversalModelNumber,SC.CustomerShotCode,
ST.Cumstomertype,F.FacilityName,L.LoadId,A.idPallet,ULD.SealNo1,ULD.SealNo2,ULD.SealNo3,ULD.SealNo4,US.UserName,A.CreatedDate,pkg.packageName,
P.pallet_netweight,P.MaterialType,LO.LocationName,AM.ManufacturerName,A.part_type
            FROM asn_assets A
            LEFT JOIN pallets P on P.idPallet = A.idPallet
            LEFT JOIN loads L on L.LoadId = P.LoadId
            LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
            LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
            LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
            LEFT JOIN uploaded_loads_details ULD on ULD.SerialNumber = A.SerialNumber
            LEFT JOIN users US ON US.UserId = A.CreatedBy
            LEFT JOIN package pkg on pkg.idPackage= P.idPackage
            LEFT JOIN location LO ON LO.LocationID = P.WarehouseLocationId
            LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
            where A.AssetScanID IS NULL AND P.MaterialType = 'Component' AND (P.status=1 or P.status = 7)";
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1);
}
while($row = mysqli_fetch_assoc($query))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 16 hour'));
	}*/
    $date1 = explode(" ",$row['CreatedDate']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $row['CreatedDate'] = $date;
    /*if($row['UniversalModelNumber'] != '')
    {
        $sqlmpn = "Select idManufacturer,part_type from catlog_creation where mpn_id = '".$row['UniversalModelNumber']."'";
        $querympn = mysqli_query($connectionlink1,$sqlmpn);
        $rowmpn = mysqli_fetch_assoc($querympn);
        $sqlmpnmaf = "Select ManufacturerName from manufacturer where idManufacturer = '".$rowmpn['idManufacturer']."'";
        $querympnmaf = mysqli_query($connectionlink1,$sqlmpnmaf);
        $rowmpnmaf = mysqli_fetch_assoc($querympnmaf);
    }
    else
    {
        $rowmpn['part_type'] = '';
        $rowmpnmaf['ManufacturerName'] = '';
    }*/
    $row['SealNo1'] = str_replace(","," ",$row['SealNo1']);
    $row['SealNo2'] = str_replace(","," ",$row['SealNo2']);
    $row['SealNo3'] = str_replace(","," ",$row['SealNo3']);
    $row['SealNo4'] = str_replace(","," ",$row['SealNo4']);
    $orginseal = $row['SealNo1']."/".$row['SealNo2']."/".$row['SealNo3']."/".$row['SealNo4'];
    $row['SerialNumber'] = str_replace(',', '', $row['SerialNumber']);
    $row['SerialNumber'] = str_replace('"', '', $row['SerialNumber']);
    $row['UniversalModelNumber'] = str_replace(',', '', $row['UniversalModelNumber']);
    $row['UniversalModelNumber'] = str_replace('"', '', $row['UniversalModelNumber']);
    if($row['ManufacturerName'] == '')
    {
        $row['ManufacturerName'] = 'n/a';
    }
    $row['SerialNumber'] = str_replace(","," ",$row['SerialNumber']);
    $row['UniversalModelNumber'] = str_replace(","," ",$row['UniversalModelNumber']);
    $row['part_type'] = str_replace(","," ",$row['part_type']);
    $row['ManufacturerName'] = str_replace(","," ",$row['ManufacturerName']);
    $row['Cumstomertype'] = str_replace(","," ",$row['Cumstomertype']);
    $row['CustomerShotCode'] = str_replace(","," ",$row['CustomerShotCode']);
    $row['LoadId'] = str_replace(","," ",$row['LoadId']);
    $row['idPallet'] = str_replace(","," ",$row['idPallet']);
    $row['MaterialType'] = str_replace(","," ",$row['MaterialType']);
    $row['packageName'] = str_replace(","," ",$row['packageName']);
    $row['pallet_netweight'] = str_replace(","," ",$row['pallet_netweight']);
    $row['UserName'] = str_replace(","," ",$row['UserName']);
    $row['FacilityName'] = str_replace(","," ",$row['FacilityName']);
    $row['LocationName'] = str_replace(","," ",$row['LocationName']);
    $row['UserName'] = strtolower($row['UserName']);
    if($row['SerialNumber'] == '')
	{
		$row['SerialNumber'] = 'n/a';
	}
    if($row['UniversalModelNumber'] == '')
	{
		$row['UniversalModelNumber'] = 'n/a';
	}
    if($row['part_type'] == '')
	{
		$row['part_type'] = 'n/a';
	}
    if($row['ManufacturerName'] == '')
	{
		$row['ManufacturerName'] = 'n/a';
	}
    if($row['Cumstomertype'] == '')
	{
		$row['Cumstomertype'] = 'n/a';
	}
    if($row['CustomerShotCode'] == '')
	{
		$row['CustomerShotCode'] = 'n/a';
	}
    if($row['LoadId'] == '')
	{
		$row['LoadId'] = 'n/a';
	}
    if($row['idPallet'] == '')
	{
		$row['idPallet'] = 'n/a';
	}
    if($orginseal == '')
	{
		$orginseal = 'n/a';
	}
    if($row['MaterialType'] == '')
	{
		$row['MaterialType'] = 'n/a';
	}
    if($row['packageName'] == '')
	{
		$row['packageName'] = 'n/a';
	}
    if($row['pallet_netweight'] == '')
	{
		$row['pallet_netweight'] = 'n/a';
	}
    if($row['UserName'] == '')
	{
		$row['UserName'] = 'n/a';
	}
    if($row['CreatedDate'] == '')
	{
		$row['CreatedDate'] = 'n/a';
	}
    if($row['FacilityName'] == '')
	{
		$row['FacilityName'] = 'n/a';
	}
    if($row['LocationName'] == '')
	{
		$row['LocationName'] = 'n/a';
	}
    $row2  = array($row['SerialNumber'],$row['UniversalModelNumber'],$row['part_type'],$row['ManufacturerName'],$row['Cumstomertype'],$row['CustomerShotCode'],$row['LoadId'],$row['idPallet'],$orginseal,$row['MaterialType'],$row['packageName'],$row['pallet_netweight'],$row['UserName'],$row['CreatedDate'],$row['FacilityName'],$row['LocationName']);
    $rows[] = $row2;
}
foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>