<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
//$ProcessDatefrom = "2024-12-17 00:00:00";
//$ProcessDateto = "2025-04-24 23:59:59";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'PartsSort.'.$today.'.csv';
#$filname = 'PS.'.$today.'.csv';
$csv = "entity_id,operator_login_id,sort_location_id,serial_id,serial_scan_time,mpn_id,mpn_scan_time,part_type,origin_bin_id,origin_next_step_action,evaluation_result,result_scan_time,sort_datetime,bin_id,bin_scan_time,next_step_action,next_step_rule_id,workstation_id,workstation_scan_time,workstation_group_id,storage_location_group_id,coo_id,coo_scan_time,classification_type,classification_code_id,source_type,customer_id,event_s_duration_value,event_id\n";//Column headers
$sql = "select distinct(PSH.SerialNumber) as serial_id,'eV-Disposition-1' as entity_id,PSHU.UserName as operator_login_id,PSHAF.FacilityName as sort_location_id,
PSH.serial_scan_time,PSH.MPN as mpn_id,PSH.mpn_scan_time,PSHCC.part_type,PSHCP.BinName as origin_bin_id,PSHD.disposition as origin_next_step_action,
PSHWI.input as evaluation_result,PSH.evaluation_result_scan_time as result_scan_time,PSH.CreatedDate as sort_datetime,PSHCPT.BinName as bin_id,
PSH.destination_bin_scan_time as bin_scan_time,PSHDT.disposition as next_step_action,PSHBR.rule_id_text as next_step_rule_id,PSHS.SiteName as workstation_id,
PSH.workstation_scan_time as workstation_scan_time,PSHWC.GroupName as workstation_group_id,PSHLG.GroupName as storage_location_group_id,
PSHC.COO as coo_id,PSH.workstation_scan_time as coo_scan_time,PSH.WasteClassificationType as classification_type,PSH.WasteCode as classification_code_id,
PSHPCT.Cumstomertype as source_type,AWSSC.Customer as customer_id,PSH.PartsSortScanTimeID as event_id
            from parts_sort_history PSH 
            LEFT JOIN asset PSHA on PSHA.AssetScanID = PSH.AssetScanID
            LEFT JOIN facility PSHAF on PSHAF.FacilityID = PSHA.FacilityID
            LEFT JOIN users PSHU on PSHU.UserId = PSH.CreatedBy
            LEFT JOIN catlog_creation PSHCC on PSHCC.mpn_id = PSH.MPN
			LEFT JOIN custompallet PSHCP on PSHCP.CustomPalletID = PSH.FromCustomPalletID
			LEFT JOIN disposition PSHD on PSHD.disposition_id = PSH.FromDisposition
			LEFT JOIN workflow_input PSHWI ON PSHWI.input_id = PSH.input_id
			LEFT JOIN custompallet PSHCPT on PSHCPT.CustomPalletID = PSH.ToCustomPalletID
			LEFT JOIN disposition PSHDT on PSHDT.disposition_id = PSH.ToDisposition
			LEFT JOIN business_rule PSHBR ON PSHBR.rule_id = PSH.rule_id
			LEFT JOIN `site` PSHS on PSHS.SiteID = PSH.SiteID
			LEFT JOIN WorkstationConfiguration PSHWC on PSHWC.GroupID = PSH.workstation_group_id
			LEFT JOIN location PSHL on PSHL.LocationName = PSH.LocationName
			LEFT JOIN location_group PSHLG on PSHLG.GroupID = PSHL.GroupID
			LEFT JOIN COO PSHC on PSHC.COOID = PSH.COOID
			LEFT JOIN pallets PSHP on PSHP.idPallet = PSH.idPallet
			LEFT JOIN customer PSHPC on PSHPC.CustomerID = PSHP.idCustomer
            LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = PSHPC.AWSCustomerID
			LEFT JOIN customertype PSHPCT on PSHPCT.idCustomertype = PSHP.idCustomertype
            where PSH.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
            group by PSH.SerialNumber";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
	/*if($row['FacilityName'] == 'CVG110')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 16 hour'));
	}*/
    $date1 = explode(" ",$row['CreatedDate']);
	$date2 = explode("-",$date1[0]);
	$date = $date2[1]."/".$date2[2]."/".$date2[0];
	$time = date("g:i a", strtotime($row['CreatedDate']));
	
    $timeFirst  = strtotime($row['serial_scan_time']);
    $timeSecond = strtotime($row['sort_datetime']);
    $differenceInSeconds = $timeSecond - $timeFirst;
    if($differenceInSeconds < 0)
    {
        $differenceInSeconds = 0;
    }
	$row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
	$row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
	$row['serial_id'] = str_replace(","," ",$row['serial_id']);
	$row['source_type'] = str_replace(","," ",$row['source_type']);
	$row['operator_login_id'] = strtolower($row['operator_login_id']);
	
    if($row['operator_login_id'] == '')
	{
		$row['operator_login_id'] = 'n/a';
	}
    if($row['sort_location_id'] == '')
	{
		$row['sort_location_id'] = 'n/a';
	}
    if($row['serial_id'] == '')
	{
		$row['serial_id'] = 'n/a';
	}
    if($row['mpn_id'] == '')
	{
		$row['mpn_id'] = 'n/a';
	}
    if($row['part_type'] == '')
	{
		$row['part_type'] = 'n/a';
	}
    if($row['origin_bin_id'] == '')
	{
		$row['origin_bin_id'] = 'n/a';
	}
    if($row['origin_next_step_action'] == '')
	{
		$row['origin_next_step_action'] = 'n/a';
	}
    if($row['evaluation_result'] == '')
	{
		$row['evaluation_result'] = 'n/a';
	}
    if($row['bin_id'] == '')
	{
		$row['bin_id'] = 'n/a';
	}
    if($row['next_step_action'] == '')
	{
		$row['next_step_action'] = 'n/a';
	}
    if($row['next_step_rule_id'] == '')
	{
		$row['next_step_rule_id'] = 'n/a';
	}
    if($row['workstation_id'] == '')
	{
		$row['workstation_id'] = 'n/a';
	}
    if($row['workstation_group_id'] == '')
	{
		$row['workstation_group_id'] = 'n/a';
	}
    if($row['storage_location_group_id'] == '')
	{
		$row['storage_location_group_id'] = 'n/a';
	}
    if($row['coo_id'] == '')
	{
		$row['coo_id'] = 'n/a';
	}
    if($row['classification_type'] == '')
	{
		$row['classification_type'] = 'n/a';
	}
    if($row['classification_code_id'] == '')
	{
		$row['classification_code_id'] = 'n/a';
	}
    if($row['source_type'] == '')
	{
		$row['source_type'] = 'n/a';
	}
    if($row['customer_id'] == '')
	{
		$row['customer_id'] = 'n/a';
	}
    if($row['serial_scan_time'] != '')
    {
        if($row['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row['serial_scan_time']));
        }
        else
        {
            $row['serial_scan_time'] = '';
        }
    }
    else
    {
        $row['serial_scan_time'] = '';
    }
    if($row['mpn_scan_time'] != '')
    {
        if($row['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row['mpn_scan_time']));
        }
        else
        {
            $row['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['result_scan_time'] != '')
    {
        if($row['result_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['result_scan_time'] = date("Y-m-d H:i:s", strtotime($row['result_scan_time']));
        }
        else
        {
            $row['result_scan_time'] = '';
        }
    }
    else
    {
        $row['result_scan_time'] = '';
    }
    if($row['sort_datetime'] != '')
    {
        if($row['sort_datetime'] != '0000-00-00 00:00:00')
        {
            $row['sort_datetime'] = date("Y-m-d H:i:s", strtotime($row['sort_datetime']));
        }
        else
        {
            $row['sort_datetime'] = '';
        }
    }
    else
    {
        $row['sort_datetime'] = '';
    }
    if($row['bin_scan_time'] != '')
    {
        if($row['bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row['bin_scan_time']));
        }
        else
        {
            $row['bin_scan_time'] = '';
        }
    }
    else
    {
        $row['bin_scan_time'] = '';
    }
    if($row['workstation_scan_time'] != '')
    {
        if($row['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row['workstation_scan_time']));
        }
        else
        {
            $row['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row['workstation_scan_time'] = '';
    }
    if($row['coo_scan_time'] != '')
    {
        if($row['coo_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['coo_scan_time'] = date("Y-m-d H:i:s", strtotime($row['coo_scan_time']));
        }
        else
        {
            $row['coo_scan_time'] = '';
        }
    }
    else
    {
        $row['coo_scan_time'] = '';
    }
    $row2  = array($row['entity_id'],$row['operator_login_id'],$row['sort_location_id'],$row['serial_id'],$row['serial_scan_time'],$row['mpn_id'],$row['mpn_scan_time'],$row['part_type'],$row['origin_bin_id'],$row['origin_next_step_action'],$row['evaluation_result'],$row['result_scan_time'],$row['sort_datetime'],$row['bin_id'],$row['bin_scan_time'],$row['next_step_action'],$row['next_step_rule_id'],$row['workstation_id'],$row['workstation_scan_time'],$row['workstation_group_id'],$row['storage_location_group_id'],$row['coo_id'],$row['coo_scan_time'],$row['classification_type'],$row['classification_code_id'],$row['source_type'],$row['customer_id'],$differenceInSeconds,$row['event_id']);
    $rows[] = $row2;
}
foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink1,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>