<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'ComponentRecovery.'.$today.'.csv';
$i = 0;
$csv = "entity_id,recovery_type_scan_time,recovered_serial_id,serial_scan_time,mpn_id,mpn_scan_time,part_type,part_type_scan_time,manufacturer_id,source_type,component_id,component_id_scan_time,component_mpn_id,component_part_type,component_bin_id,component_next_step_action,component_next_step_rule_id,operator_login_id,recovery_location_id,recovery_result,result_scan_time,recovery_datetime,bin_id,bin_scan_time,container_id,container_scan_time,next_step_action,next_step_rule_id,workstation_id,workstation_scan_time,storage_location_group_id,coo_id,component_classification_type,component_classification_code_id,classification_type,classification_code_id,controller_login_id,tpvr_reason,tpvr_request_scan_time,controller_scan_time,recovery_verification_id,coo_scan_time,batch_event_flag,event_id,customer_id,event_s_duration_value\n";//Column headers
$sql = "Select distinct(A.SerialNumber) as recovered_serial_id,A.recovery_type_scan_time as recovery_type_scan_time,A.serial_scan_time as serial_scan_time,
        A.UniversalModelNumber as mpn_id, A.mpn_scan_time as mpn_scan_time,APT.parttype as part_type,A.part_type_scan_time as part_type_scan_time,
        AM.ManufacturerName as manufacturer_id,ST.Cumstomertype as source_type,A.TopLevelSerial as component_id,A.origin_container_id_scan_time as component_id_scan_time,
        ARR.MPN as component_mpn_id,ARR.Type as component_part_type,ARRCP.BinName as component_bin_id,ARRD.disposition as component_next_step_action,
        NULL as component_next_step_rule_id,AU.UserName as operator_login_id,AF.FacilityName as recovery_location_id,ASW.input as recovery_result,
        A.result_scan_time as result_scan_time,A.DateCreated,ACP.BinName as bin_id,A.bin_scan_time as bin_scan_time,A.idPallet as container_id,
        A.origin_container_id_scan_time as container_scan_time,AD.disposition as next_step_action,ABR.rule_name as next_step_rule_id,
        ASS.SiteName as workstation_id,A.workstation_scan_time as workstation_scan_time,ACPLG.GroupName as storage_location_group_id,
        ACOO.COO as coo_id,ARRD.WasteClassificationType as component_classification_type,ARR.WasteCode as component_classification_code_id,
        AD.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,P.AuditControllerLoginID as controller_login_id,
        NULL as tpvr_reason,NULL as tpvr_request_scan_time,A.origin_container_id_scan_time as controller_scan_time,A.MediaRecovery_VerificationID as recovery_verification_id,
        A.coo_scan_time as coo_scan_time,A.batch_event_flag,A.event_id,AWSSC.Customer as customer_id,A.DateUpdated,ASCLG.GroupName
        FROM asset as A
        LEFT JOIN parttype APT ON APT.parttypeid = A.parttypeid
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype ST on ST.idCustomertype = P.idCustomertype
        LEFT JOIN users AU on AU.UserId = A.CreatedBy
        LEFT JOIN facility AF on AF.FacilityID = A.FacilityID
        LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
        LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        LEFT JOIN workflow_input ASW ON ASW.input_id = A.input_id
        LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
        LEFT JOIN speed_server_recovery ARR ON ARR.ServerSerialNumber = A.TopLevelSerial
        LEFT JOIN pallets ARRP on ARRP.idPallet = ARR.idPallet
        LEFT JOIN custompallet ARRCP on ARRCP.CustomPalletID = ARR.CustomPalletID
        LEFT JOIN disposition ARRD ON ARRD.`disposition_id` = ARR.`disposition_id`
        LEFT JOIN business_rule ABR on ABR.rule_id = A.rule_id
        LEFT JOIN shipping_containers ASCS ON ASCS.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCL ON ASCL.LocationID = ASCS.LocationID
        LEFT JOIN location_group ASCLG ON ASCLG.GroupID = ASCL.GroupID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.DateCreated Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        and A.Recoverytypeid IN (Select Recoverytypeid from Recoverytype where Recoverytype = 'Component')
        AND A.StatusID != 10
        group by A.SerialNumber
        order by A.event_id,A.serial_scan_time ASC";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    /*if($row['event_id'] != $eventid)
    {
        $i = 0;
    }
    if($i ==0)
    {
        $eventid = $row['event_id'];
        $timeFirst  = strtotime($row['recovery_type_scan_time']);
        $timeSecond = strtotime($row['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    else
    {
        $eventid = $row['event_id'];
        $timeFirst  = $timeSecond;
        $timeSecond = strtotime($row['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    $i = $i+1;*/
    $eventid = $row['event_id'];
    $timeFirst  = strtotime($row['component_id_scan_time']);
    $timeSecond = strtotime($row['DateCreated']);
    $differenceInSeconds = $timeSecond - $timeFirst;
    if($row['DateUpdated'] == '')
    {
        $row['DateUpdated'] = $row['DateCreated'];
    }
    
    if($row['bin_id'] != '')
    {
        $row['container_id'] = 'n/a';
        $row['container_scan_time'] = '';
    }
    else
    {
        $row['bin_id'] = 'n/a';
        $row['bin_scan_time'] = '';
        $row['storage_location_group_id'] = $row['GroupName'];
    }
    $date1 = explode(" ",$row['DateCreated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row['DateCreated']));
    if($row['DateCreated'] != '')
    {
        $row['DateCreated'] = date("Y-m-d H:i:s", strtotime($row['DateCreated']));
    }
    else
    {
        $row['DateCreated'] = '';
    }
    $row['recovered_serial_id'] = strtolower($row['recovered_serial_id']);
    $row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
	$row['part_type'] = str_replace(","," ",$row['part_type']);
	$row['manufacturer_id'] = str_replace(","," ",$row['manufacturer_id']);
	$row['source_type'] = str_replace(","," ",$row['source_type']);
    $row['component_id'] = str_replace(","," ",$row['component_id']);
    $row['component_mpn_id'] = str_replace(","," ",$row['component_mpn_id']);
    $row['component_part_type'] = str_replace(","," ",$row['component_part_type']);
    $row['component_bin_id'] = str_replace(","," ",$row['component_bin_id']);
    $row['component_next_step_action'] = str_replace(","," ",$row['component_next_step_action']);
    $row['component_next_step_rule_id'] = str_replace(","," ",$row['component_next_step_rule_id']);
    $row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
    $row['recovery_location_id'] = str_replace(","," ",$row['recovery_location_id']);
    $row['recovery_result'] = str_replace(","," ",$row['recovery_result']);
    $row['bin_id'] = str_replace(","," ",$row['bin_id']);
    $row['container_id'] = str_replace(","," ",$row['container_id']);
    $row['next_step_action'] = str_replace(","," ",$row['next_step_action']);
    $row['next_step_rule_id'] = str_replace(","," ",$row['next_step_rule_id']);
    $row['workstation_id'] = str_replace(","," ",$row['workstation_id']);
    $row['storage_location_group_id'] = str_replace(","," ",$row['storage_location_group_id']);
    $row['coo_id'] = str_replace(","," ",$row['coo_id']);
    $row['component_classification_type'] = str_replace(","," ",$row['component_classification_type']);
    $row['component_classification_code_id'] = str_replace(","," ",$row['component_classification_code_id']);
    $row['classification_type'] = str_replace(","," ",$row['classification_type']);
    $row['classification_code_id'] = str_replace(","," ",$row['classification_code_id']);
    $row['controller_login_id'] = str_replace(","," ",$row['controller_login_id']);
    $row['tpvr_reason'] = str_replace(","," ",$row['tpvr_reason']);
    $row['recovery_verification_id'] = str_replace(","," ",$row['recovery_verification_id']);
    $row['batch_event_flag'] = str_replace(","," ",$row['batch_event_flag']);
    $row['event_id'] = str_replace(","," ",$row['event_id']);
    $row['customer_id'] = str_replace(","," ",$row['customer_id']);
    
    if($row['entity_id'] == '')
    {
        $row['entity_id'] = 'n/a';
    }
    if($row['recovery_type_scan_time'] == '')
    {
        $row['recovery_type_scan_time'] = '';
    }
    if($row['recovered_serial_id'] == '')
    {
        $row['recovered_serial_id'] = 'n/a';
    }
    if($row['serial_scan_time'] == '')
    {
        $row['serial_scan_time'] = '';
    }
    if($row['mpn_id'] == '')
    {
        $row['mpn_id'] = 'n/a';
    }
    if($row['mpn_scan_time'] == '')
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['part_type'] == '')
    {
        $row['part_type'] = 'n/a';
    }
    if($row['part_type_scan_time'] == '')
    {
        $row['part_type_scan_time'] = '';
    }
    if($row['manufacturer_id'] == '')
    {
        $row['manufacturer_id'] = 'n/a';
    }
    if($row['source_type'] == '')
    {
        $row['source_type'] = 'n/a';
    }
    if($row['component_id'] == '')
    {
        $row['component_id'] = 'n/a';
    }
    if($row['component_id_scan_time'] == '')
    {
        $row['component_id_scan_time'] = '';
    }
    if($row['component_mpn_id'] == '')
    {
        $row['component_mpn_id'] = 'n/a';
    }
    if($row['component_part_type'] == '')
    {
        $row['component_part_type'] = 'n/a';
    }
    if($row['component_bin_id'] == '')
    {
        $row['component_bin_id'] = 'n/a';
    }
    if($row['component_next_step_action'] == '')
    {
        $row['component_next_step_action'] = 'n/a';
    }
    if($row['component_next_step_rule_id'] == '')
    {
        $row['component_next_step_rule_id'] = 'n/a';
    }
    if($row['operator_login_id'] == '')
    {
        $row['operator_login_id'] = 'n/a';
    }
    if($row['recovery_location_id'] == '')
    {
        $row['recovery_location_id'] = 'n/a';
    }
    if($row['recovery_result'] == '')
    {
        $row['recovery_result'] = 'n/a';
    }
    if($row['result_scan_time'] == '')
    {
        $row['result_scan_time'] = '';
    }
    if($row['bin_id'] == '')
    {
        $row['bin_id'] = 'n/a';
    }
    if($row['bin_scan_time'] == '')
    {
        $row['bin_scan_time'] = '';
    }
    if($row['container_id'] == '')
    {
        $row['container_id'] = 'n/a';
    }
    if($row['container_scan_time'] == '')
    {
        $row['container_scan_time'] = '';
    }
    if($row['next_step_action'] == '')
    {
        $row['next_step_action'] = 'n/a';
    }
    if($row['next_step_rule_id'] == '')
    {
        $row['next_step_rule_id'] = '';
    }
    if($row['workstation_id'] == '')
    {
        $row['workstation_id'] = 'n/a';
    }
    if($row['workstation_scan_time'] == '')
    {
        $row['workstation_scan_time'] = '';
    }
    if($row['storage_location_group_id'] == '')
    {
        $row['storage_location_group_id'] = 'n/a';
    }
    if($row['coo_id'] == '')
    {
        $row['coo_id'] = 'n/a';
    }
    if($row['component_classification_type'] == '')
    {
        $row['component_classification_type'] = 'n/a';
    }
    if($row['component_classification_code_id'] == '')
    {
        $row['component_classification_code_id'] = 'n/a';
    }
    if($row['classification_type'] == '')
    {
        $row['classification_type'] = 'n/a';
    }
    if($row['controller_login_id'] == '')
    {
        $row['controller_login_id'] = 'n/a';
    }
    if($row['tpvr_reason'] == '')
    {
        $row['tpvr_reason'] = 'n/a';
    }
    if($row['tpvr_request_scan_time'] == '')
    {
        $row['tpvr_request_scan_time'] = '';
    }
    if($row['controller_scan_time'] == '')
    {
        $row['controller_scan_time'] = '';
    }
    if($row['recovery_verification_id'] == '')
    {
        $row['recovery_verification_id'] = 'n/a';
    }
    if($row['coo_scan_time'] == '')
    {
        $row['coo_scan_time'] = '';
    }
    if($row['batch_event_flag'] == '')
    {
        $row['batch_event_flag'] = 'N';
    }
    if($row['event_id'] == '')
    {
        $row['event_id'] = 'n/a';
    }
    if($row['customer_id'] == '')
    {
        $row['customer_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    if($row['recovery_type_scan_time'] != '')
    {
        if($row['recovery_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['recovery_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row['recovery_type_scan_time']));
        }
        else
        {
            $row['recovery_type_scan_time'] = '';
        }
    }
    else
    {
        $row['recovery_type_scan_time'] = '';
    }
    if($row['serial_scan_time'] != '')
    {
        if($row['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row['serial_scan_time']));
        }
        else
        {
            $row['serial_scan_time'] = '';
        }
    }
    else
    {
        $row['serial_scan_time'] = '';
    }
    if($row['mpn_scan_time'] != '')
    {
        if($row['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row['mpn_scan_time']));
        }
        else
        {
            $row['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['part_type_scan_time'] != '')
    {
        if($row['part_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['part_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row['part_type_scan_time']));
        }
        else
        {
            $row['part_type_scan_time'] = '';
        }
    }
    else
    {
        $row['part_type_scan_time'] = '';
    }
    if($row['component_id_scan_time'] != '')
    {
        if($row['component_id_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['component_id_scan_time'] = date("Y-m-d H:i:s", strtotime($row['component_id_scan_time']));
        }
        else
        {
            $row['component_id_scan_time'] = '';
        }
    }
    else
    {
        $row['component_id_scan_time'] = '';
    }
    if($row['result_scan_time'] != '')
    {
        if($row['result_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['result_scan_time'] = date("Y-m-d H:i:s", strtotime($row['result_scan_time']));
        }
        else
        {
            $row['result_scan_time'] = '';
        }
    }
    else
    {
        $row['result_scan_time'] = '';
    }
    if($row['bin_scan_time'] != '')
    {
        if($row['bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row['bin_scan_time']));
        }
        else
        {
            $row['bin_scan_time'] = '';
        }
    }
    else
    {
        $row['bin_scan_time'] = '';
    }
    if($row['container_scan_time'] != '')
    {
        if($row['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row['container_scan_time']));
        }
        else
        {
            $row['container_scan_time'] = '';
        }
    }
    else
    {
        $row['container_scan_time'] = '';
    }
    if($row['workstation_scan_time'] != '')
    {
        if($row['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row['workstation_scan_time']));
        }
        else
        {
            $row['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row['workstation_scan_time'] = '';
    }
    if($row['tpvr_request_scan_time'] != '')
    {
        if($row['tpvr_request_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['tpvr_request_scan_time'] = date("Y-m-d H:i:s", strtotime($row['tpvr_request_scan_time']));
        }
        else
        {
            $row['tpvr_request_scan_time'] = '';
        }
    }
    else
    {
        $row['tpvr_request_scan_time'] = '';
    }
    if($row['controller_scan_time'] != '')
    {
        if($row['controller_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['controller_scan_time'] = date("Y-m-d H:i:s", strtotime($row['controller_scan_time']));
        }
        else
        {
            $row['controller_scan_time'] = '';
        }
    }
    else
    {
        $row['controller_scan_time'] = '';
    }
    if($row['coo_scan_time'] != '')
    {
        if($row['coo_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['coo_scan_time'] = date("Y-m-d H:i:s", strtotime($row['coo_scan_time']));
        }
        else
        {
            $row['coo_scan_time'] = '';
        }
    }
    else
    {
        $row['coo_scan_time'] = '';
    }
    $row2  = array('eV-Disposition-1',$row['recovery_type_scan_time'],$row['recovered_serial_id'],$row['serial_scan_time'],$row['mpn_id'],$row['mpn_scan_time'],$row['part_type'],$row['part_type_scan_time'],$row['manufacturer_id'],$row['source_type'],$row['component_id'],$row['component_id_scan_time'],$row['component_mpn_id'],$row['component_part_type'],$row['component_bin_id'],$row['component_next_step_action'],$row['component_next_step_rule_id'],$row['operator_login_id'],$row['recovery_location_id'],$row['recovery_result'],$row['result_scan_time'],$row['DateCreated'],$row['bin_id'],$row['bin_scan_time'],$row['container_id'],$row['container_scan_time'],$row['next_step_action'],$row['next_step_rule_id'],$row['workstation_id'],$row['workstation_scan_time'],$row['storage_location_group_id'],$row['coo_id'],$row['component_classification_type'],$row['component_classification_code_id'],$row['classification_type'],$row['classification_code_id'],$row['controller_login_id'],$row['tpvr_reason'],$row['tpvr_request_scan_time'],$row['controller_scan_time'],$row['recovery_verification_id'],$row['coo_scan_time'],$row['batch_event_flag'],$row['event_id'],$row['customer_id'],$differenceInSeconds);
    $rows[] = $row2;
}

foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34].','.$record[35].','.$record[36].','.$record[37].','.$record[38].','.$record[39].','.$record[40].','.$record[41].','.$record[42].','.$record[43].','.$record[44].','.$record[45]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>