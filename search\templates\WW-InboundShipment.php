<?php
set_time_limit(24000);
ini_set('post_max_size', '2000M');
ini_set('memory_limit', '2000M');
ini_set('max_execution_time', 30000);
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";

$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'InboundShipments.'.$today.'.csv';

$csv = "entity_id,receive_location_id,container_id,container_type,source_type,origin_location_id,origin_ticket_id,origin_seal_id,material_type,operator_login_id,container_action,container_action_datetime,container_weight_value,container_weight_unit,carrier_id,vehicle_id,trailer_id,shipment_seal_id,tracking_id,valid_ticket_flag,valid_seal_flag,pof_flag,classification_type,classification_code_id,customer_id,controller_login_id,quarantine_reason\n";//Column headers
$sql = "Select AF.FacilityName as receive_location_id,P.idPallet as container_id,PK.packageName as container_type,SCT.Cumstomertype as source_type,
        SC.CustomerShotCode as origin_location_id,P.LoadId as origin_ticket_id,P.SealNo1,P.SealNo2,P.SealNo3,P.SealNo4,
        P.MaterialType as material_type,PAU.UserName as operator_login_id,PPS.StatusValue as container_action,P.ReceivedDate,
        P.pallet_netweight as container_weight_value,AF.WeightUnit as container_weight_unit,NULL as carrier_id,NULL as vehicle_id,NULL as trailer_id,NULL as shipment_seal_id,
        NULL as tracking_id,P.Verified as valid_ticket_flag,P.Verified as valid_seal_flag,P.POF as pof_flag,P.WasteClassificationType as classification_type,
        P.WasteCode as classification_code_id,PAC.Customer as customer_id,P.AuditControllerLoginID as controller_login_id

        From pallets P 
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN facility AF ON AF.FacilityID = P.PalletFacilityID
        LEFT JOIN `location` PL on PL.LocationID = P.WarehouseLocationId
        LEFT JOIN package PK on PK.idPackage = P.idPackage
        LEFT JOIN users PAU on PAU.UserId = P.ReceivedBy
        LEFT JOIN pallet_status PPS on PPS.status = P.status
        LEFT JOIN aws_customers PAC on PAC.AWSCustomerID = P.AWSCustomerID
        where
		P.ReceivedDate  Between '".$ProcessDatefrom."' and '".$ProcessDateto."' 
		and P.Received = 1
		group by P.idPallet";
		//AND (P.status=1 or P.status = 7)

$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1).$sql;
}
while($row = mysqli_fetch_assoc($query))
{
	if($row['ReceivedDate'] != '')
    {
        $row['ReceivedDate'] = date("Y-m-d H:i:s", strtotime($row['ReceivedDate']));
    }
    else
    {
        $row['ReceivedDate'] = '';
    }
	$date1 = explode(" ",$row['ReceivedDate']);
	$date2 = explode("-",$date1[0]);
	$date = $date2[1]."/".$date2[2]."/".$date2[0];
	$time = date("g:i:s a", strtotime($row['ReceivedDate']));
	$date11 = explode(" ",$row['UpdatedDate']);
	$date21 = explode("-",$date11[0]);
	$date22 = $date21[1]."/".$date21[2]."/".$date21[0];
	$time22 = date("g:i:s a", strtotime($row['UpdatedDate']));
	
	$row['SealNo1'] = str_replace(","," ",$row['SealNo1']);
	$row['SealNo2'] = str_replace(","," ",$row['SealNo2']);
	$row['SealNo3'] = str_replace(","," ",$row['SealNo3']);
	$row['SealNo4'] = str_replace(","," ",$row['SealNo4']);
	$seal = $row['SealNo1']."/".$row['SealNo2']."/".$row['SealNo3']."/".$row['SealNo4'];

	$row['entity_id'] = str_replace(","," ",$row['entity_id']);
	$row['receive_location_id'] = str_replace(","," ",$row['receive_location_id']);
	$row['container_id'] = str_replace(","," ",$row['container_id']);
	$row['container_type'] = str_replace(","," ",$row['container_type']);
	$row['source_type'] = str_replace(","," ",$row['source_type']);
	$row['origin_location_id'] = str_replace(","," ",$row['origin_location_id']);
    $row['origin_ticket_id'] = str_replace(","," ",$row['origin_ticket_id']);
    $row['origin_seal_id'] = str_replace(","," ",$row['origin_seal_id']);
    $row['material_type'] = str_replace(","," ",$row['material_type']);
    $row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
    $row['container_action'] = str_replace(","," ",$row['container_action']);
    $row['container_weight_value'] = str_replace(","," ",$row['container_weight_value']);
    $row['container_weight_unit'] = str_replace(","," ",$row['container_weight_unit']);
    $row['carrier_id'] = str_replace(","," ",$row['carrier_id']);
    $row['vehicle_id'] = str_replace(","," ",$row['vehicle_id']);
    $row['trailer_id'] = str_replace(","," ",$row['trailer_id']);
    $row['shipment_seal_id'] = str_replace(","," ",$row['shipment_seal_id']);
    $row['tracking_id'] = str_replace(","," ",$row['tracking_id']);
    $row['valid_ticket_flag'] = str_replace(","," ",$row['valid_ticket_flag']);
    $row['valid_seal_flag'] = str_replace(","," ",$row['valid_seal_flag']);
    $row['pof_flag'] = str_replace(","," ",$row['pof_flag']);
    $row['classification_type'] = str_replace(","," ",$row['classification_type']);
    $row['classification_code_id'] = str_replace(","," ",$row['classification_code_id']);
    $row['customer_id'] = str_replace(","," ",$row['customer_id']);
    $row['controller_login_id'] = str_replace(","," ",$row['controller_login_id']);
    $row['quarantine_reason'] = str_replace(","," ",$row['quarantine_reason']);
	
	if($row['valid_ticket_flag'] == '1')
	{
		$row['valid_ticket_flag'] = 'Yes';
	}
	else
	{
		$row['valid_ticket_flag'] = 'No';
	}
	if($row['valid_seal_flag'] == '1')
	{
		$row['valid_seal_flag'] = 'Yes';
	}
	else
	{
		$row['valid_seal_flag'] = 'No';
	}
	if($row['pof_flag'] == '1')
	{
		$row['pof_flag'] = 'Yes';
	}
	else
	{
		$row['pof_flag'] = 'No';
	}
	if($row['entity_id'] == '')
	{
		$row['entity_id'] = 'n/a';
	}
	if($row['receive_location_id'] == '')
	{
		$row['receive_location_id'] = 'n/a';
	}
	if($row['container_id'] == '')
	{
		$row['container_id'] = 'n/a';
	}
	if($seal == '')
	{
		$seal = 'n/a';
	}
	if($row['container_type'] == '')
	{
		$row['container_type'] = 'n/a';
	}
	if($date == '')
	{
		$date = 'n/a';
	}
	if($time == '')
	{
		$time = 'n/a';
	}
	if($row['source_type'] == '')
	{
		$row['source_type'] = 'n/a';
	}
	if($row['origin_location_id'] == '')
	{
		$row['origin_location_id'] = 'n/a';
	}
	if($row['origin_ticket_id'] == '')
	{
		$row['origin_ticket_id'] = 'n/a';
	}
    if($row['material_type'] == '')
	{
		$row['material_type'] = 'n/a';
	}
    if($row['operator_login_id'] == '')
	{
		$row['container_action'] = 'n/a';
	}
    if($row['container_weight_value'] == '')
	{
		$row['container_weight_value'] = 'n/a';
	}
    if($row['container_weight_unit'] == '')
	{
		$row['container_weight_unit'] = 'n/a';
	}
    if($row['carrier_id'] == '')
	{
		$row['carrier_id'] = 'n/a';
	}
    if($row['vehicle_id'] == '')
	{
		$row['vehicle_id'] = 'n/a';
	}
    if($row['trailer_id'] == '')
	{
		$row['trailer_id'] = 'n/a';
	}
    if($row['shipment_seal_id'] == '')
	{
		$row['shipment_seal_id'] = 'n/a';
	}
    if($row['tracking_id'] == '')
	{
		$row['tracking_id'] = 'n/a';
	}
    if($row['valid_ticket_flag'] == '')
	{
		$row['valid_ticket_flag'] = 'n/a';
	}
    if($row['valid_seal_flag'] == '')
	{
		$row['valid_seal_flag'] = 'n/a';
	}
    if($row['pof_flag'] == '')
	{
		$row['pof_flag'] = 'n/a';
	}
    if($row['classification_type'] == '')
	{
		$row['classification_type'] = 'n/a';
	}
    if($row['classification_code_id'] == '')
	{
		$row['classification_code_id'] = 'n/a';
	}
    if($row['customer_id'] == '')
	{
		$row['customer_id'] = 'n/a';
	}
    if($row['controller_login_id'] == '')
	{
		$row['controller_login_id'] = 'n/a';
	}
    if($row['quarantine_reason'] == '')
	{
		$row['quarantine_reason'] = 'n/a';
	}
    
    $row2  = array('eV-Disposition-1',$row['receive_location_id'],$row['container_id'],$row['container_type'],$row['source_type'],$row['origin_location_id'],$row['origin_ticket_id'],$seal,$row['material_type'],$row['operator_login_id'],$row['container_action'],$row['ReceivedDate'],$row['container_weight_value'],$row['container_weight_unit'],$row['carrier_id'],$row['vehicle_id'],$row['trailer_id'],$row['shipment_seal_id'],$row['tracking_id'],$row['valid_ticket_flag'],$row['valid_seal_flag'],$row['pof_flag'],$row['classification_type'],$row['classification_code_id'],$row['customer_id'],$row['controller_login_id'],$row['quarantine_reason']);
    $rows[] = $row2;
}
$sql1 = "Select AF.FacilityName as receive_location_id,P.idPallet as container_id,PK.packageName as container_type,SCT.Cumstomertype as source_type,
        SC.CustomerShotCode as origin_location_id,P.LoadId as origin_ticket_id,P.SealNo1,P.SealNo2,P.SealNo3,P.SealNo4,
        P.MaterialType as material_type,PAU.UserName as operator_login_id,PPS.StatusValue as container_action,P.ReceivedDate,
        P.pallet_netweight as container_weight_value,AF.WeightUnit as container_weight_unit,NULL as carrier_id,NULL as vehicle_id,NULL as trailer_id,NULL as shipment_seal_id,
        NULL as tracking_id,P.Verified as valid_ticket_flag,P.Verified as valid_seal_flag,P.POF as pof_flag,P.WasteClassificationType as classification_type,
        NULL as classification_code_id,PAC.Customer as customer_id,NULL as controller_login_id,P.UpdatedDate

        From pallets P 
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN facility AF ON AF.FacilityID = P.PalletFacilityID
        LEFT JOIN `location` PL on PL.LocationID = P.WarehouseLocationId
        LEFT JOIN package PK on PK.idPackage = P.idPackage
        LEFT JOIN users PAU on PAU.UserId = P.ReceivedBy
        LEFT JOIN pallet_status PPS on PPS.status = P.status
        LEFT JOIN aws_customers PAC on PAC.AWSCustomerID = P.AWSCustomerID
        where
		P.UpdatedDate  Between '".$ProcessDatefrom."' and '".$ProcessDateto."' 
		and P.status = 3 and P.Received = 1
		group by P.idPallet";
		//AND (P.status=1 or P.status = 7)

$query1 = mysqli_query($connectionlink1,$sql1);
if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1).$sql;
}
while($row1 = mysqli_fetch_assoc($query1))
{
	$receiveddate = date("Y-m-d", strtotime($row1['ReceivedDate']));
	$UpdatedDate = date("Y-m-d", strtotime($row1['UpdatedDate']));
	if($UpdatedDate != $receiveddate)
	{
		if($row1['ReceivedDate'] != '')
		{
			$row1['ReceivedDate'] = date("Y-m-d H:i:s", strtotime($row1['UpdatedDate']));
		}
		else
		{
			$row1['ReceivedDate'] = '';
		}
		$date1 = explode(" ",$row1['ReceivedDate']);
		$date2 = explode("-",$date1[0]);
		$date = $date2[1]."/".$date2[2]."/".$date2[0];
		$time = date("g:i:s a", strtotime($row1['ReceivedDate']));
		$date11 = explode(" ",$row1['UpdatedDate']);
		$date21 = explode("-",$date11[0]);
		$date22 = $date21[1]."/".$date21[2]."/".$date21[0];
		$time22 = date("g:i:s a", strtotime($row1['UpdatedDate']));
		
		$row1['SealNo1'] = str_replace(","," ",$row1['SealNo1']);
		$row1['SealNo2'] = str_replace(","," ",$row1['SealNo2']);
		$row1['SealNo3'] = str_replace(","," ",$row1['SealNo3']);
		$row1['SealNo4'] = str_replace(","," ",$row1['SealNo4']);
		$seal = $row1['SealNo1']."/".$row1['SealNo2']."/".$row1['SealNo3']."/".$row1['SealNo4'];

		$row1['entity_id'] = str_replace(","," ",$row1['entity_id']);
		$row1['receive_location_id'] = str_replace(","," ",$row1['receive_location_id']);
		$row1['container_id'] = str_replace(","," ",$row1['container_id']);
		$row1['container_type'] = str_replace(","," ",$row1['container_type']);
		$row1['source_type'] = str_replace(","," ",$row1['source_type']);
		$row1['origin_location_id'] = str_replace(","," ",$row1['origin_location_id']);
		$row1['origin_ticket_id'] = str_replace(","," ",$row1['origin_ticket_id']);
		$row1['origin_seal_id'] = str_replace(","," ",$row1['origin_seal_id']);
		$row1['material_type'] = str_replace(","," ",$row1['material_type']);
		$row1['operator_login_id'] = str_replace(","," ",$row1['operator_login_id']);
		$row1['container_action'] = str_replace(","," ",$row1['container_action']);
		$row1['container_weight_value'] = str_replace(","," ",$row1['container_weight_value']);
		$row1['container_weight_unit'] = str_replace(","," ",$row1['container_weight_unit']);
		$row1['carrier_id'] = str_replace(","," ",$row1['carrier_id']);
		$row1['vehicle_id'] = str_replace(","," ",$row1['vehicle_id']);
		$row1['trailer_id'] = str_replace(","," ",$row1['trailer_id']);
		$row1['shipment_seal_id'] = str_replace(","," ",$row1['shipment_seal_id']);
		$row1['tracking_id'] = str_replace(","," ",$row1['tracking_id']);
		$row1['valid_ticket_flag'] = str_replace(","," ",$row1['valid_ticket_flag']);
		$row1['valid_seal_flag'] = str_replace(","," ",$row1['valid_seal_flag']);
		$row1['pof_flag'] = str_replace(","," ",$row1['pof_flag']);
		$row1['classification_type'] = str_replace(","," ",$row1['classification_type']);
		$row1['classification_code_id'] = str_replace(","," ",$row1['classification_code_id']);
		$row1['customer_id'] = str_replace(","," ",$row1['customer_id']);
		$row1['controller_login_id'] = str_replace(","," ",$row1['controller_login_id']);
		$row1['quarantine_reason'] = str_replace(","," ",$row1['quarantine_reason']);
		
		if($row1['valid_ticket_flag'] == '1')
		{
			$row1['valid_ticket_flag'] = 'Yes';
		}
		else
		{
			$row1['valid_ticket_flag'] = 'No';
		}
		if($row1['valid_seal_flag'] == '1')
		{
			$row1['valid_seal_flag'] = 'Yes';
		}
		else
		{
			$row1['valid_seal_flag'] = 'No';
		}
		if($row1['pof_flag'] == '1')
		{
			$row1['pof_flag'] = 'Yes';
		}
		else
		{
			$row1['pof_flag'] = 'No';
		}
		if($row1['entity_id'] == '')
		{
			$row1['entity_id'] = 'n/a';
		}
		if($row1['receive_location_id'] == '')
		{
			$row1['receive_location_id'] = 'n/a';
		}
		if($row1['container_id'] == '')
		{
			$row1['container_id'] = 'n/a';
		}
		if($seal == '')
		{
			$seal = 'n/a';
		}
		if($row1['container_type'] == '')
		{
			$row1['container_type'] = 'n/a';
		}
		if($date == '')
		{
			$date = 'n/a';
		}
		if($time == '')
		{
			$time = 'n/a';
		}
		if($row1['source_type'] == '')
		{
			$row1['source_type'] = 'n/a';
		}
		if($row1['origin_location_id'] == '')
		{
			$row1['origin_location_id'] = 'n/a';
		}
		if($row1['origin_ticket_id'] == '')
		{
			$row1['origin_ticket_id'] = 'n/a';
		}
		if($row1['material_type'] == '')
		{
			$row1['material_type'] = 'n/a';
		}
		if($row1['operator_login_id'] == '')
		{
			$row1['container_action'] = 'n/a';
		}
		if($row1['container_weight_value'] == '')
		{
			$row1['container_weight_value'] = 'n/a';
		}
		if($row1['container_weight_unit'] == '')
		{
			$row1['container_weight_unit'] = 'n/a';
		}
		if($row1['carrier_id'] == '')
		{
			$row1['carrier_id'] = 'n/a';
		}
		if($row1['vehicle_id'] == '')
		{
			$row1['vehicle_id'] = 'n/a';
		}
		if($row1['trailer_id'] == '')
		{
			$row1['trailer_id'] = 'n/a';
		}
		if($row1['shipment_seal_id'] == '')
		{
			$row1['shipment_seal_id'] = 'n/a';
		}
		if($row1['tracking_id'] == '')
		{
			$row1['tracking_id'] = 'n/a';
		}
		if($row1['valid_ticket_flag'] == '')
		{
			$row1['valid_ticket_flag'] = 'n/a';
		}
		if($row1['valid_seal_flag'] == '')
		{
			$row1['valid_seal_flag'] = 'n/a';
		}
		if($row1['pof_flag'] == '')
		{
			$row1['pof_flag'] = 'n/a';
		}
		if($row1['classification_type'] == '')
		{
			$row1['classification_type'] = 'n/a';
		}
		if($row1['classification_code_id'] == '')
		{
			$row1['classification_code_id'] = 'n/a';
		}
		if($row1['customer_id'] == '')
		{
			$row1['customer_id'] = 'n/a';
		}
		if($row1['controller_login_id'] == '')
		{
			$row1['controller_login_id'] = 'n/a';
		}
		if($row1['quarantine_reason'] == '')
		{
			$row1['quarantine_reason'] = 'n/a';
		}
		
		$row21  = array('eV-Disposition-1',$row1['receive_location_id'],$row1['container_id'],$row1['container_type'],$row1['source_type'],$row1['origin_location_id'],$row1['origin_ticket_id'],$seal,$row1['material_type'],$row1['operator_login_id'],$row1['container_action'],$row1['ReceivedDate'],$row1['container_weight_value'],$row1['container_weight_unit'],$row1['carrier_id'],$row1['vehicle_id'],$row1['trailer_id'],$row1['shipment_seal_id'],$row1['tracking_id'],$row1['valid_ticket_flag'],$row1['valid_seal_flag'],$row1['pof_flag'],$row1['classification_type'],$row1['classification_code_id'],$row1['customer_id'],$row1['controller_login_id'],$row1['quarantine_reason']);
		$rows[] = $row21;
	}
}
foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>