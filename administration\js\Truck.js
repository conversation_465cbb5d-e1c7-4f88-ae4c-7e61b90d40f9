(function () {
    'use strict';
angular.module('app').controller("parktypeConfiguration", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,facilityinformation, UserFacility) {
    $scope.parktype = {};
    $scope.Facility = {};

    facilityinformation.async().then(function (d) { //2. so you can use .then()
        $scope.Facility = d['data']['Result'];
    });

    UserFacility.async().then(function (d) { //2. so you can use .then()
        $scope.UserFacility = d.data;
        $scope.parktype.FacilityID = $scope.UserFacility;
    });

    $scope.SaveParkTypeConfiguration = function () {
        $scope.parktype.busy = true;
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=SaveParkTypeConfiguration&'+$.param($scope.parktype),
            success: function(data){
                /*console.log('sssssss');
                console.log( data.Result);
                console.log('sssssss');*/
                $rootScope.$broadcast('preloader:hide');
                $scope.parktype.busy = false;
                if(data.Success) {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-success md-block')
                    );
                   location.reload();
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.parktype.busy = false;
                alert('error');
                initSessionTime(); $scope.$apply();
            }
        });
    };

    $scope.EditParkTypeConfiguration = function (parktype) {
        $scope.parktype = parktype;
        jQuery.ajax({
            url: host+'administration/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetParkTypeConfigurationDetails&ParkTypeID=' + parktype.ParkTypeID,
            success: function (data) {
                if (data.Success == true) {
                    $scope.parktype = data.Result;
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );
                    $scope.parktype = [];
                }
                $scope.$apply();
            }, error: function (data) {
                $scope.$apply();
            }
        });
    }

    $scope.busy = false;
    $scope.ParkTypeConfigurationList = [];
    $scope.pagedItems = [];
    //Start Pagination Logic
    $scope.itemsPerPage = 10;
    $scope.currentPage = 0;
    $scope.OrderBy = '';
    $scope.OrderByType = '';
    $scope.filter_text = [{}];
    $scope.range = function() {
        var rangeSize = 10;
        var ret = [];
        var start;
        start = $scope.currentPage;
        if ( start > $scope.pageCount()-rangeSize ) {
            start = $scope.pageCount()-rangeSize;
        }
        for (var i=start; i<start+rangeSize; i++) {
            ret.push(i);
        }
        return ret;
    };
    $scope.prevPage = function() {
        if ($scope.currentPage > 0) {
            $scope.currentPage--;
        }
    };
    $scope.firstPage = function () {
        $scope.currentPage = 0;
    };
    $scope.prevPageDisabled = function() {
        return $scope.currentPage === 0 ? "disabled" : "";
    };
    $scope.nextPage = function() {
        if ($scope.currentPage < $scope.pageCount() - 1) {
            $scope.currentPage++;
        }
    };
    $scope.lastPage = function() {
        $scope.currentPage =  $scope.pageCount() - 1;
    };
    $scope.nextPageDisabled = function() {
        return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
    };
    $scope.pageCount = function() {
        return Math.ceil($scope.total/$scope.itemsPerPage);
    };
    $scope.setPage = function(n) {
        if (n >= 0 && n < $scope.pageCount()) {
            $scope.currentPage = n;
        }
    };
    $scope.CallServerFunction = function (newValue) {
        if($scope.CurrentStatus != '' )  {
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetParkTypeConfigurationList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        }
    };
    $scope.$watch("currentPage", function(newValue, oldValue) {
        $scope.CallServerFunction(newValue);
    });
    $scope.convertSingle = function (multiarray) {
        var result = {};
        for(var i=0;i<multiarray.length;i++) {
            result[i] = multiarray[i];
        }
        //alert(result);
        return result;
    };
    $scope.MakeOrderBy = function (orderby) {
        $scope.OrderBy = orderby;
        if($scope.OrderByType == 'asc') {
            $scope.OrderByType = 'desc';
        } else {
            $scope.OrderByType = 'asc';
        }
        $scope.busy = true;
        $rootScope.$broadcast('preloader:active');

        jQuery.ajax({
            url: host+'administration/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetParkTypeConfigurationList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
            success: function(data) {
                $scope.busy = false;
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                    $scope.pagedItems = data.Result;
                    if(data.total) {
                        $scope.total = data.total;
                    }
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .position('right')
                            .hideDelay(3000)
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.busy = false;
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
    };
    $scope.MakeFilter = function () {
        if($scope.currentPage == 0) {
            $scope.CallServerFunction($scope.currentPage);
        } else {
            $scope.currentPage = 0;
        }
    };
    //End Pagination Logic

    $scope.ParkTypeConfigurationxls = function () {
    //alert("1");
        jQuery.ajax({
            //url: host+'administration/includes/admin_extended_submit.php',
            url: host+'administration/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GenerateParkTypeConfigurationxls',
            success: function(data) {
                if(data.Success) {
                    //alert("2");
                    window.location="templates/ParkTypeConfigurationxls.php";
                } else {
                    //alert("4");
                    $mdToast.show (
                        $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                    );

                    var op = data.Result.split(' ');                            
                    if( op[0] == "No" && op[1] == 'Access') {
                        window.location = host;
                    }
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                //alert(data.Result);
                //alert("3");
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
    };
});

angular.module('app').controller("trucktypeConfiguration", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,facilityinformation, UserFacility,) {
        $scope.trucktype = {};
        $scope.Facility = {};

        facilityinformation.async().then(function (d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        UserFacility.async().then(function (d) { //2. so you can use .then()
            $scope.UserFacility = d.data;
            $scope.trucktype.FacilityID = $scope.UserFacility;
        });

        $scope.SaveTruckTypeConfiguration = function () {
            $scope.trucktype.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                //url: host+'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SaveTruckTypeConfiguration&'+$.param($scope.trucktype),
                success: function(data){
                    /*console.log('sssssss');
                    console.log( data.Result);
                    console.log('sssssss');*/
                    $rootScope.$broadcast('preloader:hide');
                    $scope.trucktype.busy = false;
                    if(data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                        );
                       location.reload();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.trucktype.busy = false;
                    alert('error');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.EditTruckTypeConfiguration = function (trucktype) {
            $scope.trucktype = trucktype;
            jQuery.ajax({
                //url: host + 'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTruckTypeConfigurationDetails&TruckTypeID=' + trucktype.TruckTypeID,
                success: function (data) {
                    if (data.Success == true) {
                        $scope.trucktype = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                        $scope.trucktype = [];
                    }
                    $scope.$apply();
                }, error: function (data) {
                    $scope.$apply();
                }
            });
        }

        $scope.busy = false;
        $scope.TruckTypeConfigurationList = [];
        $scope.pagedItems = [];
        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if($scope.CurrentStatus != '' )  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    //url: host+'administration/includes/admin_submit.php',
                    url: host+'administration/includes/Truck_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetTruckTypeConfigurationList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                //url: host+'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTruckTypeConfigurationList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic

         $scope.TruckTypeConfigurationxls = function () {
        //alert("1");
            jQuery.ajax({
                //url: host+'administration/includes/admin_extended_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateTruckTypeConfigurationxls',
                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        window.location="templates/TruckTypeConfigurationxls.php";
                    } else {
                        //alert("4");
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
    });

angular.module('app').controller("TDRSOPConfiguration", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,facilityinformation, UserFacility,) {
        $scope.TDRSOP = {};
       
        $scope.SaveTDRSOPConfiguration = function () {
            $scope.TDRSOP.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                //url: host+'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SaveTDRSOPConfiguration&'+$.param($scope.TDRSOP),
                success: function(data){
                    /*console.log('sssssss');
                    console.log( data.Result);
                    console.log('sssssss');*/
                    $rootScope.$broadcast('preloader:hide');
                    $scope.TDRSOP.busy = false;
                    if(data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                        );
                       location.reload();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.TDRSOP.busy = false;
                    alert('error');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.EditTDRSOPConfiguration = function (TDRSOP) {
            $scope.TDRSOP = TDRSOP;
            jQuery.ajax({
                //url: host + 'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTDRSOPConfigurationDetails&TDRSOPID=' + TDRSOP.TDRSOPID,
                success: function (data) {
                    if (data.Success == true) {
                        $scope.TDRSOP = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                        $scope.TDRSOP = [];
                    }
                    $scope.$apply();
                }, error: function (data) {
                    $scope.$apply();
                }
            });
        }

        $scope.busy = false;
        $scope.TDRSOPConfigurationList = [];
        $scope.pagedItems = [];
        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if($scope.CurrentStatus != '' )  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    //url: host+'administration/includes/admin_submit.php',
                    url: host+'administration/includes/Truck_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetTDRSOPConfigurationList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                //url: host+'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTDRSOPConfigurationList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic

        $scope.TDRSOPConfigurationxls = function () {
        //alert("1");
            jQuery.ajax({
                //url: host+'administration/includes/admin_extended_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateTDRSOPConfigurationxls',
                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        window.location="templates/TDRSOPConfigurationxls.php";
                    } else {
                        //alert("4");
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );

                        var op = data.Result.split(' ');                            
                        if( op[0] == "No" && op[1] == 'Access') {
                            window.location = host;
                        }
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
    });

angular.module('app').controller("parkinglocationConfiguration", function ($scope,$location,$http,$rootScope,$mdToast,$stateParams,facilityinformation, UserFacility,$upload) {
        $scope.parkinglocation = {};
        $scope.Facility = {};

        facilityinformation.async().then(function (d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        UserFacility.async().then(function (d) { //2. so you can use .then()
            $scope.UserFacility = d.data;
            $scope.parkinglocation.FacilityID = $scope.UserFacility;
        });

        jQuery.ajax({
            //url: host + 'administration/includes/admin_submit.php',
            url: host+'administration/includes/Truck_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetParkType',
            success: function (data) {
                if (data.Success) {
                    $scope.ParkTypes = data.Result;
                } else {
            $mdToast.show (
                    $mdToast.simple()
                        .content(data.Result)
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                }
                $scope.$apply();
            }, error: function (data) {
                $scope.data = data;
                $scope.$apply();
            }
        });

        $scope.SaveParkingLocationConfiguration = function () {
            $scope.parkinglocation.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                //url: host+'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SaveParkingLocationConfiguration&'+$.param($scope.parkinglocation),
                success: function(data){
                    /*console.log('sssssss');
                    console.log( data.Result);
                    console.log('sssssss');*/
                    $rootScope.$broadcast('preloader:hide');
                    $scope.parkinglocation.busy = false;
                    if(data.Success) {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                        );
                       location.reload();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.parkinglocation.busy = false;
                    alert('error');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.EditParkingLocationConfiguration = function (parkinglocation) {
            $scope.parkinglocation = parkinglocation;
            jQuery.ajax({
                //url: host + 'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetParkingLocationConfigurationDetails&ParkingLocationID=' + parkinglocation.ParkingLocationID,
                success: function (data) {
                    if (data.Success == true) {
                        $scope.parkinglocation = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                        $scope.parkinglocation = [];
                    }
                    $scope.$apply();
                }, error: function (data) {
                    $scope.$apply();
                }
            });
        }

        $scope.busy = false;
        $scope.ParkingLocationNameConfigurationList = [];
        $scope.pagedItems = [];
        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if($scope.CurrentStatus != '' )  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    //url: host+'administration/includes/admin_submit.php',
                    url: host+'administration/includes/Truck_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetParkingLocationConfigurationList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                //url: host+'administration/includes/admin_submit.php',
                url: host+'administration/includes/Truck_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetParkingLocationConfigurationList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic

        
        $scope.onFileSelect = function($files) {
            if($("#ParkingLocationFile").val() != '') {
                //$files: an array of files selected, each file has name, size, and type.
                $rootScope.$broadcast('preloader:active');
                $scope.busy = true;
                for (var i = 0; i < $files.length; i++) {
                    var file = $files[i];
                    $scope.upload = $upload.upload({
                    //url: host+'administration/includes/admin_extended_submit.php',
                    url: host+'administration/includes/Truck_submit.php',
                    data: {ajax: 'UploadParkingLocationFile'},
                    file: file, // or list of files ($files) for html5 only
                    }).success(function(data, status, headers, config) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.busy = false;
                    if(data.Success) {
                        $("#ParkingLocationFile").val('');
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $scope.CallServerFunction(0);
                           window.location = "templates/UploadDataExport.php";
                        //location.reload();
                    } else {
                        $("#ParkingLocationFile").val('');
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                    // file is uploaded successfully
                    }).error(function(data, status, headers, config) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data);
                    });
                }
            }
        };
    });


})();
