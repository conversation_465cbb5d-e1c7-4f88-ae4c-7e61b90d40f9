<?php
session_start();
include_once("admin.class.php");
include_once("../../common_functions.php");
include_once("../../excel_reader/SimpleXLSX.php");
include_once("../../Truckyard/templates/xlsxwriter.class.php");
include_once("../../Truckyard/templates/xlsxwriterplus.class.php");
use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

class TruckClass extends CommonClass
{
 
	public function GetParkingLocations($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
		/*	if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}*/
			$query = "select * from ParkingLocation where Status = '1' order by `ParkingLocationName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Parking Location Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetCarriers($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
		/*	if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}*/
			$query = "select * from Carrier where StatusID = '1' order by `CarrierName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Carrier Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetTruckTypes($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		try {
		/*	if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Workflow')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Workflow Page';
				return json_encode($json);
			}*/
			$query = "select * from TruckType where Status = '1' order by `TruckTypeName`";
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$result =  array();
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No TruckType Available";
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function TruckSave($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$chkdt = $data['ArrivalDate'];
		$chkdtarr=explode("GMT",$chkdt);
		$newdt= strtotime($chkdtarr[0]);
		$data['ArrivalDate'] = date("Y-m-d",$newdt);

		// Arrival Time
		$chkdt = $data['ArrivalTime'];
		$chkdtarr = explode("GMT", $chkdt);
		$newdt = strtotime($chkdtarr[0]);
		$data['ArrivalTime'] = date("H:i:s", $newdt); // <-- Just time

		// Departure Time
		/*$chkdt = $data['DepartureTime'];
		$chkdtarr = explode("GMT", $chkdt);
		$newdt = strtotime($chkdtarr[0]);
		$data['DepartureTime'] = date("H:i:s", $newdt); // <-- Just time*/


		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recovery type')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/
		//return json_encode($json);
		if ($data['TruckID'] == '') { //If New Class
			$query = "insert into Truck (FacilityID,ArrivalType,ParkingLocationID,CarrierID,ArrivalDate,ArrivalTime,LoadType,LoadNumber,TruckTypeID,TruckReg,TrailerNumber,DriverName,DriverID,ShipmentTicketID,ClassificationType,WasteCollectionPermit,Notes,Status,CreatedDate,CreatedBy) values ('" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalDate']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalTime']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['LoadType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TruckReg']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['DriverName']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['DriverID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Notes']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',NOW(),'" . $_SESSION['user']['UserId'] . "')";
		} else {
			$query = "update Truck set FacilityID='" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',ArrivalType='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalType']) . "',ParkingLocationID='" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "',CarrierID='" . mysqli_real_escape_string($this->connectionlink, $data['CarrierID']) . "',ArrivalDate='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalDate']) . "',ArrivalTime='" . mysqli_real_escape_string($this->connectionlink, $data['ArrivalTime']) . "',LoadType='" . mysqli_real_escape_string($this->connectionlink, $data['LoadType']) . "',LoadNumber='" . mysqli_real_escape_string($this->connectionlink, $data['LoadNumber']) . "',TruckTypeID='" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "',TruckReg='" . mysqli_real_escape_string($this->connectionlink, $data['TruckReg']) . "',TrailerNumber='" . mysqli_real_escape_string($this->connectionlink, $data['TrailerNumber']) . "',DriverName='" . mysqli_real_escape_string($this->connectionlink, $data['DriverName']) . "',DriverID='" . mysqli_real_escape_string($this->connectionlink, $data['DriverID']) . "',ShipmentTicketID='" . mysqli_real_escape_string($this->connectionlink, $data['ShipmentTicketID']) . "',ClassificationType='" . mysqli_real_escape_string($this->connectionlink, $data['ClassificationType']) . "',WasteCollectionPermit='" . mysqli_real_escape_string($this->connectionlink, $data['WasteCollectionPermit']) . "',Notes='" . mysqli_real_escape_string($this->connectionlink, $data['Notes']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',UpdatedDate =NOW(),UpdatedBy='" . $_SESSION['user']['UserId'] . "' where TruckID='" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}

		if ($data['TruckID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New Truck created";
			$json['TruckID'] = $insert_id;

		} else {
			$json['Success'] = true;
			$json['Result'] = "Truck modified";
		}
		return json_encode($json);
	}

	public function GetTruckDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Recoverytype Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Recoverytype Page';
			return json_encode($json);
		}*/

		//return json_encode($json);
		$query = "select * from Truck where TruckID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckID']) . "' ORDER BY TruckID";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Truck ID";
		}
		return json_encode($json);
	}

	public function GetTruckList($data)
	{
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['TruckID']
			);

			$query = "select T.*,PL.ParkingLocationName,F.FacilityName,C.CarrierName from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID left join facility F on T.FacilityID = F.FacilityID left join Carrier C on T.CarrierID = C.CarrierID where 1";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'FacilityName') {
							$query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'CarrierName') {
							$query = $query . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ParkingLocationName') {
							$query = $query . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalType') {
							$query = $query . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalDate') {
							$query = $query . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ArrivalTime') {
							$query = $query . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadType') {
							$query = $query . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TruckReg') {
							$query = $query . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverName') {
							$query = $query . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'DriverID') {
							$query = $query . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Notes') {
							$query = $query . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'LoadNumber') {
							$query = $query . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'TrailerNumber') {
							$query = $query . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Status') {
							$query = $query . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}

					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'CarrierName') {
					$query = $query . " order by C.CarrierName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ParkingLocationName') {
					$query = $query . " order by PL.ParkingLocationName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalType') {
					$query = $query . " order by T.ArrivalType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalDate') {
					$query = $query . " order by T.ArrivalDate " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ArrivalTime') {
					$query = $query . " order by T.ArrivalTime " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadType') {
					$query = $query . " order by T.LoadType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TruckReg') {
					$query = $query . " order by T.TruckReg " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverName') {
					$query = $query . " order by T.DriverName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'DriverID') {
					$query = $query . " order by T.DriverID " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Notes') {
					$query = $query . " order by T.Notes " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'LoadNumber') {
					$query = $query . " order by T.LoadNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'TrailerNumber') {
					$query = $query . " order by T.TrailerNumber " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Status') {
					$query = $query . " order by T.Status " . $order_by_type . " ";
				} 
 			} else {
				$query = $query . " order by T.TruckID desc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					 // Calculate DepartureTime: 30 mins after ArrivalTime
            if (!empty($row['ArrivalTime'])) {
                $arrival = new DateTime($row['ArrivalTime']);
                $arrival->modify('+30 minutes');
                $row['DepartureTime'] = $arrival->format('H:i');
            } else {
                $row['DepartureTime'] = null;
            }
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Data Available";
			}

			if ($data['skip'] == 0) {

				$query1 = "select count(*) from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID 
								left join facility F on T.FacilityID = F.FacilityID	left join Carrier C on T.CarrierID = C.CarrierID where 1";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'CarrierName') {
								$query1 = $query1 . " AND C.CarrierName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ParkingLocationName') {
								$query1 = $query1 . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalType') {
								$query1 = $query1 . " AND T.ArrivalType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalDate') {
								$query1 = $query1 . " AND T.ArrivalDate like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ArrivalTime') {
								$query1 = $query1 . " AND T.ArrivalTime like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadType') {
								$query1 = $query1 . " AND T.LoadType like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TruckReg') {
								$query1 = $query1 . " AND T.TruckReg like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverName') {
								$query1 = $query1 . " AND T.DriverName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'DriverID') {
								$query1 = $query1 . " AND T.DriverID like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Notes') {
								$query1 = $query1 . " AND T.Notes like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'LoadNumber') {
								$query1 = $query1 . " AND T.LoadNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'TrailerNumber') {
								$query1 = $query1 . " AND T.TrailerNumber like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Status') {
								$query1 = $query1 . " AND T.Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}

						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteTruck($data)
		{
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);

			$transaction = 'Administration ---> eViridis Administration --->Truck Booking';
			$description = 'Truck Booking Delete';
			$this->RecordUserTransaction($transaction, $description);
			$sql = "DELETE FROM Truck WHERE TruckID = '".$data['id']."'";
			$query = mysqli_query($this->connectionlink,$sql);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			else
			{
				$json['Success'] = true;
				$json['Result'] = "Record Deleted Successfully.";
				return json_encode($json);
			}
		}
	public function ChangeStatus($data) {
		$json = array(
			'Success' => false,
			'Result' => 'No Data Available'
		);
		$query = "update Truck set Status='".$data['Status']."',UpdatedDate=NOW(),UpdatedBy='".$_SESSION['user']['UserId']."' where TruckID='".$data['TruckID']."'";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		} else {
			$json['Success'] = true;
			$json['Result'] = 'Status Changed';
			return json_encode($json);
		}
	}

	public function GenerateTruckListxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['TruckListxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

}

?>