<?php
	session_start();
	include_once("../database/shipping.class.php");
	$obj = new ShippingClass();
	
	if($_POST['ajax'] == "GetDispositions") {
		$result = $obj->GetDispositions($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllVendors") {
		$result = $obj->GetAllVendors($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateShipment") {
		$result = $obj->CreateShipment($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetShipmentDetails") {
		$result = $obj->GetShipmentDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPackageTypes") {
		$result = $obj->GetPackageTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateContainer") {
		$result = $obj->CreateContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddSerialToContainer") {
		$result = $obj->AddSerialToContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateSerialNumber") {
		$result = $obj->ValidateSerialNumber($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetContainerSerials") {
		$result = $obj->GetContainerSerials($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteSerialFromContainer") {
		$result = $obj->DeleteSerialFromContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetActiveShipments") {
		$result = $obj->GetActiveShipments($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetShipmentContainers") {
		$result = $obj->GetShipmentContainers($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ShipShipment") {
		$result = $obj->ShipShipment($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRemovedShipments") {
		$result = $obj->GetRemovedShipments($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRemovedShipmentContainers") {
		$result = $obj->GetRemovedShipmentContainers($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetFacilities") {
		$result = $obj->GetFacilities($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "DeleteContainerFromShipment") {
		$result = $obj->DeleteContainerFromShipment($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetDispositioNextStep") {
		$result = $obj->GetDispositioNextStep($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddInventorySerialToContainer") {
		$result = $obj->AddInventorySerialToContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "Getcountries"){
		$result = $obj->Getcountries($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Getstates"){
		$result = $obj->Getstates($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetVendorCategory"){
		$result = $obj->GetVendorCategory($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetPaymentTerms"){
		$result = $obj->GetPaymentTerms($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "Vendor"){
		$result = $obj->Vendor($_POST);
		echo $result;
	}
	
	if($_POST['ajax'] == "GetVendorDetails"){
  		$result = $obj->GetVendorDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetShippingRemovalTypes") {
		$result = $obj->GetShippingRemovalTypes($_POST);
	  echo $result;
  	}

	if($_POST['ajax'] == "AddRemovalTypeToDestination") {
		$result = $obj->AddRemovalTypeToDestination($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetMappedRemovalTypes") {
		$result = $obj->GetMappedRemovalTypes($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "DeleteMappedRemovalType") {
		$result = $obj->DeleteMappedRemovalType($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetDestinationRemovalTypes") {
		$result = $obj->GetDestinationRemovalTypes($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "DeleteShipment") {
		$result = $obj->DeleteShipment($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ValidateInventorySerialNumber") {
		$result = $obj->ValidateInventorySerialNumber($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ValidateRemovalController") {
		$result = $obj->ValidateRemovalController($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GenerateShipmentPalletID") {
		$result = $obj->GenerateShipmentPalletID($_POST);
		echo $result;
  	}

	if($_GET['ajax'] == "GetMatchingLocations"){
		$result = $obj->GetMatchingLocations($_GET);
		echo $result;
	}

	if($_POST['ajax'] == "RemovePalletFromContainer") {
		$result = $obj->RemovePalletFromContainer($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetDispositionVendors") {
		$result = $obj->GetDispositionVendors($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "CreateContainerWithoutShippingID") {
		$result = $obj->CreateContainerWithoutShippingID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetShippingContainerDetails") {
		$result = $obj->GetShippingContainerDetails($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ValidateSerialNumberForShipmentContainer") {
		$result = $obj->ValidateSerialNumberForShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddSerialToShipmentContainer") {
		$result = $obj->AddSerialToShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateInventorySerialNumberForShipmentContainer") {
		$result = $obj->ValidateInventorySerialNumberForShipmentContainer($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "AddInventorySerialToShipmentContainer") {
		$result = $obj->AddInventorySerialToShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateServerSerialNumberForShipmentContainer") {
		$result = $obj->ValidateServerSerialNumberForShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddServerSerialToShipmentContainer") {
		$result = $obj->AddServerSerialToShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetByProducts") {
		$result = $obj->GetByProducts($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddByProductToContainer") {
		$result = $obj->AddByProductToContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddContainerToShipment") {
		$result = $obj->AddContainerToShipment($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "AddByProductToShipmentContainer") {
		$result = $obj->AddByProductToShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddServerSerialToContainer") {
		$result = $obj->AddServerSerialToContainer($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "ValidateServerSerialNumberForContainer") {
		$result = $obj->ValidateServerSerialNumberForContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetShipmentCarriers") {
		$result = $obj->GetShipmentCarriers($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetFacilityPackageTypes") {
		$result = $obj->GetFacilityPackageTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateRemovalController1") {
		$result = $obj->ValidateRemovalController1($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "GetSesstionFacility") {
		$result = $obj->GetSesstionFacility($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "DeleteByProductContainerFromShipment") {
		$result = $obj->DeleteByProductContainerFromShipment($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "DeleteByProductFromContainer") {
		$result = $obj->DeleteByProductFromContainer($_POST);
		echo $result;
	}


	if($_POST['ajax'] == "DeleteByProductFromShipmentContainer") {
		$result = $obj->DeleteByProductFromShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteSerialFromShipmentContainer") {
		$result = $obj->DeleteSerialFromShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetTrimmedMPN") {
		$result = $obj->GetTrimmedMPN($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddMediaToContainer") {
		$result = $obj->AddMediaToContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateMediaSerialNumber") {
		$result = $obj->ValidateMediaSerialNumber($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateMediaSerialNumberForShipmentContainer") {
		$result = $obj->ValidateMediaSerialNumberForShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "AddMediaToShipmentContainer") {
		$result = $obj->AddMediaToShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CloseContainer") {
		$result = $obj->CloseContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ReopenContainer") {
		$result = $obj->ReopenContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CheckClosedContainers") {
		$result = $obj->CheckClosedContainers($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CloseShipmentContainer") {
		$result = $obj->CloseShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetShippingContainerDetails1") {
		$result = $obj->GetShippingContainerDetails1($_POST);
		echo $result;
  	}

	if($_POST['ajax'] == "ReopenContainer1") {
		$result = $obj->ReopenContainer1($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UnlinkContainerFromShipment") {
		$result = $obj->UnlinkContainerFromShipment($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateSanitizationSealID") {
		$result = $obj->ValidateSanitizationSealID($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ValidateSanitizationSealIDforShipmentContainer") {
		$result = $obj->ValidateSanitizationSealIDforShipmentContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GenerateDemanASN") {
		$result = $obj->GenerateDemanASN($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetCOOList") {
		$result = $obj->GetCOOList($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ManageReferenceType") {
		$result = $obj->ManageReferenceType($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllReferenceTypes") {
		$result = $obj->GetAllReferenceTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetReferenceTypeDetails") {
		$result = $obj->GetReferenceTypeDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetReferenceTypeList") {
		$result = $obj->GetReferenceTypeList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetEditReferenceTypeDetails"){
			$result = $obj->GetEditReferenceTypeDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateReferenceTypeListxls") {
		$result = $obj->GenerateReferenceTypeListxls($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetReferenceType") {
		$result = $obj->GetReferenceType($_POST);
		echo $result;
	}
?>