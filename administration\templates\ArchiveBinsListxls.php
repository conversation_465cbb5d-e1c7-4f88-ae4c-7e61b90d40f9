<?php
session_start();
include_once("../database/archive_bins.class.php");

if (!isset($_SESSION['user'])) {
    header("Location: ../index.php");
    exit();
}

$obj = new ArchiveBinsClass();

// Set headers for Excel download
header("Content-Type: application/vnd.ms-excel");
header("Content-Disposition: attachment; filename=ArchiveBinsList_" . date('Y-m-d_H-i-s') . ".xls");
header("Pragma: no-cache");
header("Expires: 0");

// Get ALL archive bins data for export (no filters, no pagination)
$data = array(); // Empty array since we don't want any filters
$result = $obj->GetAllArchiveBins($data);
$resultData = json_decode($result, true);

echo "<table border='1'>";
echo "<tr>";
echo "<th>Bin Name</th>";
echo "<th>Container Type</th>";
echo "<th>Disposition</th>";
echo "<th>Notes</th>";
echo "<th>Status</th>";
echo "<th>Mobility Name</th>";
echo "<th>Reference Type</th>";
echo "<th>Reference ID</th>";
echo "<th>Created Date</th>";
echo "<th>Deleted Date</th>";
echo "<th>Deleted By</th>";
echo "</tr>";

if ($resultData['Success'] && !empty($resultData['Result'])) {
    foreach ($resultData['Result'] as $item) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($item['BinName']) . "</td>";
        echo "<td>" . htmlspecialchars($item['ContainerType']) . "</td>";
        echo "<td>" . htmlspecialchars($item['disposition']) . "</td>";
        echo "<td>" . htmlspecialchars($item['Description']) . "</td>";
        echo "<td>" . htmlspecialchars($item['Status']) . "</td>";
        echo "<td>" . htmlspecialchars($item['MobilityName']) . "</td>";
        echo "<td>" . htmlspecialchars($item['ReferenceType']) . "</td>";
        echo "<td>" . htmlspecialchars($item['ReferenceID']) . "</td>";
        echo "<td>" . htmlspecialchars($item['CreatedDate']) . "</td>";
        echo "<td>" . htmlspecialchars($item['DeletedDate']) . "</td>";
        echo "<td>" . htmlspecialchars($item['FirstName'] . ' ' . $item['LastName']) . "</td>";
        echo "</tr>";
    }
} else {
    echo "<tr><td colspan='11'>No archive bins found</td></tr>";
}

echo "</table>";
?>
