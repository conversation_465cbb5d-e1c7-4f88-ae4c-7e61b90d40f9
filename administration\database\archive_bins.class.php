<?php
include_once("admin.class.php");

class ArchiveBinsClass extends AdminClass {
    
    public function GetArchiveBins($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }

        $json = array(
            'Success' => false,
            'Result' => 'No data'
        );

        if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Bin')) {
            $json['Success'] = false;
            $json['Result'] = 'No Access to Bin Page';
            return json_encode($json);
        }

        try {
            $limit = isset($data['limit']) ? mysqli_real_escape_string($this->connectionlink, $data['limit']) : 10;
            $skip = isset($data['skip']) ? mysqli_real_escape_string($this->connectionlink, $data['skip']) : 0;
            $orderBy = isset($data['OrderBy']) && !empty($data['OrderBy']) ? mysqli_real_escape_string($this->connectionlink, $data['OrderBy']) : 'BinName';
            $orderByType = isset($data['OrderByType']) && !empty($data['OrderByType']) ? mysqli_real_escape_string($this->connectionlink, $data['OrderByType']) : 'asc';

            // Build WHERE clause for filters
            $whereClause = "WHERE c.StatusID = '5' AND c.FacilityID = '" . $_SESSION['user']['FacilityID'] . "'";
            
            // Add filter conditions
            if (isset($data[0]) && is_array($data[0])) {
                $filters = $data[0];
                
                if (!empty($filters['BinName'])) {
                    $whereClause .= " AND c.BinName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['BinName']) . "%'";
                }
                if (!empty($filters['ContainerType'])) {
                    $whereClause .= " AND p.packageName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['ContainerType']) . "%'";
                }
                if (!empty($filters['ParentBinName'])) {
                    $whereClause .= " AND c.ParentBinName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['ParentBinName']) . "%'";
                }
                if (!empty($filters['LocationType'])) {
                    $whereClause .= " AND c.LocationType LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['LocationType']) . "%'";
                }
                if (!empty($filters['GroupName'])) {
                    $whereClause .= " AND lg.GroupName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['GroupName']) . "%'";
                }
                if (!empty($filters['LocationName'])) {
                    $whereClause .= " AND l.LocationName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['LocationName']) . "%'";
                }
                if (!empty($filters['disposition'])) {
                    $whereClause .= " AND d.disposition LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['disposition']) . "%'";
                }
                if (!empty($filters['Description'])) {
                    $whereClause .= " AND c.Description LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['Description']) . "%'";
                }
                if (!empty($filters['MobilityName'])) {
                    $whereClause .= " AND c.MobilityName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['MobilityName']) . "%'";
                }
                if (!empty($filters['ReferenceType'])) {
                    $whereClause .= " AND c.ReferenceType LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['ReferenceType']) . "%'";
                }
                if (!empty($filters['ReferenceID'])) {
                    $whereClause .= " AND c.ReferenceID LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['ReferenceID']) . "%'";
                }
                if (!empty($filters['CreatedDate'])) {
                    $whereClause .= " AND DATE(c.CreatedDate) = '" . mysqli_real_escape_string($this->connectionlink, $filters['CreatedDate']) . "'";
                }
                if (!empty($filters['DeletedDate'])) {
                    $whereClause .= " AND DATE(c.DeletedDate) = '" . mysqli_real_escape_string($this->connectionlink, $filters['DeletedDate']) . "'";
                }
                if (!empty($filters['DeletedBy'])) {
                    $whereClause .= " AND (u.FirstName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['DeletedBy']) . "%' OR u.LastName LIKE '%" . mysqli_real_escape_string($this->connectionlink, $filters['DeletedBy']) . "%')";
                }
            }

            // Count total records
            $countQuery = "SELECT COUNT(*) as total
                          FROM custompallet c
                          LEFT JOIN package p ON c.idPackage = p.idPackage
                          LEFT JOIN location l ON c.LocationID = l.LocationID
                          LEFT JOIN location_group lg ON c.GroupID = lg.GroupID
                          LEFT JOIN disposition d ON c.disposition_id = d.disposition_id
                          LEFT JOIN custompallet_status cs ON c.StatusID = cs.StatusID
                          LEFT JOIN users u ON c.DeletedBy = u.UserId
                          $whereClause";

            $countResult = mysqli_query($this->connectionlink, $countQuery);
            if (mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $totalRow = mysqli_fetch_assoc($countResult);
            $total = $totalRow['total'];

            // Main query to get archive bins
            $query = "SELECT c.CustomPalletID, c.BinName, c.ParentBinName, c.LocationType, c.Description,
                            c.MobilityName, c.ReferenceType, c.ReferenceID, c.CreatedDate, c.AssetsCount,
                            c.DeletedDate, c.DeletedBy,
                            p.packageName as ContainerType,
                            l.LocationName,
                            lg.GroupName,
                            d.disposition,
                            cs.Status,
                            u.FirstName, u.LastName,
                            CASE
                                WHEN c.AcceptAllDisposition = 1 THEN 'All'
                                ELSE d.disposition
                            END as disposition
                     FROM custompallet c
                     LEFT JOIN package p ON c.idPackage = p.idPackage
                     LEFT JOIN location l ON c.LocationID = l.LocationID
                     LEFT JOIN location_group lg ON c.GroupID = lg.GroupID
                     LEFT JOIN disposition d ON c.disposition_id = d.disposition_id
                     LEFT JOIN custompallet_status cs ON c.StatusID = cs.StatusID
                     LEFT JOIN users u ON c.DeletedBy = u.UserId
                     $whereClause
                     ORDER BY $orderBy $orderByType
                     LIMIT $limit OFFSET $skip";

            $result = mysqli_query($this->connectionlink, $query);
            if (mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $bins = array();
            if (mysqli_num_rows($result) > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $bins[] = $row;
                }
                $json['Success'] = true;
                $json['Result'] = $bins;
                $json['total'] = $total;
            } else {
                $json['Success'] = true;
                $json['Result'] = array();
                $json['total'] = 0;
            }

        } catch (Exception $e) {
            $json['Success'] = false;
            $json['Result'] = 'Error: ' . $e->getMessage();
        }

        return json_encode($json);
    }

    public function GetAllArchiveBins($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }

        $json = array(
            'Success' => false,
            'Result' => 'No data'
        );

        if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Bin')) {
            $json['Success'] = false;
            $json['Result'] = 'No Access to Bin Page';
            return json_encode($json);
        }

        try {
            // For export, we want ALL archived bins regardless of filters
            // Only apply basic WHERE clause for archived bins in current facility
            $whereClause = "WHERE c.StatusID = '5' AND c.FacilityID = '" . $_SESSION['user']['FacilityID'] . "'";

            // Main query to get ALL archive bins (no pagination, no filters)
            $query = "SELECT c.CustomPalletID, c.BinName, c.ParentBinName, c.LocationType, c.Description,
                            c.MobilityName, c.ReferenceType, c.ReferenceID, c.CreatedDate, c.AssetsCount,
                            c.DeletedDate, c.DeletedBy,
                            p.packageName as ContainerType,
                            l.LocationName,
                            lg.GroupName,
                            d.disposition,
                            cs.Status,
                            u.FirstName, u.LastName,
                            CASE
                                WHEN c.AcceptAllDisposition = 1 THEN 'All'
                                ELSE d.disposition
                            END as disposition
                     FROM custompallet c
                     LEFT JOIN package p ON c.idPackage = p.idPackage
                     LEFT JOIN location l ON c.LocationID = l.LocationID
                     LEFT JOIN location_group lg ON c.GroupID = lg.GroupID
                     LEFT JOIN disposition d ON c.disposition_id = d.disposition_id
                     LEFT JOIN custompallet_status cs ON c.StatusID = cs.StatusID
                     LEFT JOIN users u ON c.DeletedBy = u.UserId
                     $whereClause
                     ORDER BY c.BinName ASC";

            $result = mysqli_query($this->connectionlink, $query);
            if (mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $bins = array();
            if (mysqli_num_rows($result) > 0) {
                while ($row = mysqli_fetch_assoc($result)) {
                    $bins[] = $row;
                }
                $json['Success'] = true;
                $json['Result'] = $bins;
                $json['total'] = count($bins);
            } else {
                $json['Success'] = true;
                $json['Result'] = array();
                $json['total'] = 0;
            }

        } catch (Exception $e) {
            $json['Success'] = false;
            $json['Result'] = 'Error: ' . $e->getMessage();
        }

        return json_encode($json);
    }



    public function GenerateArchiveBinsListxls($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }

        $json = array(
            'Success' => false,
            'Result' => 'No data'
        );

        if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Bin')) {
            $json['Success'] = false;
            $json['Result'] = 'No Access to Bin Page';
            return json_encode($json);
        }

        try {
            // This function would generate Excel export for archive bins
            // For now, just return success to indicate the functionality is available
            $json['Success'] = true;
            $json['Result'] = 'Excel export functionality ready';
        } catch (Exception $e) {
            $json['Success'] = false;
            $json['Result'] = 'Error: ' . $e->getMessage();
        }

        return json_encode($json);
    }

    public function ReActivateBin($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }

        $json = array(
            'Success' => false,
            'Result' => 'Failed to reactivate bin'
        );

        if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Bin')) {
            $json['Success'] = false;
            $json['Result'] = 'No Access to Bin Page';
            return json_encode($json);
        }

        try {
            $customPalletID = mysqli_real_escape_string($this->connectionlink, $data['CustomPalletID']);
            $groupName = mysqli_real_escape_string($this->connectionlink, $data['GroupName']);
            $userId = $_SESSION['user']['UserId'];

            // Validate inputs
            if (empty($customPalletID) || empty($groupName)) {
                $json['Success'] = false;
                $json['Result'] = 'CustomPalletID and GroupName are required';
                return json_encode($json);
            }

            // Get bin details
            $binQuery = "SELECT BinName, StatusID,FacilityID FROM custompallet WHERE CustomPalletID = '$customPalletID'";
            $binResult = mysqli_query($this->connectionlink, $binQuery);

            if (!$binResult || mysqli_num_rows($binResult) == 0) {
                $json['Success'] = false;
                $json['Result'] = 'Bin not found';
                return json_encode($json);
            }

            $binData = mysqli_fetch_assoc($binResult);

            // Check if bin is actually deleted/archived
            if ($binData['StatusID'] != '5') {
                $json['Success'] = false;
                $json['Result'] = 'Bin is not in archived status';
                return json_encode($json);
            }

            // Get location group details by GroupName and validate it's WIP
            $locationGroupQuery = "SELECT GroupID, GroupName, LocationType,FacilityID FROM location_group WHERE GroupName = '$groupName' AND LocationStatus = '1'";
            $locationGroupResult = mysqli_query($this->connectionlink, $locationGroupQuery);

            if (!$locationGroupResult || mysqli_num_rows($locationGroupResult) == 0) {
                $json['Success'] = false;
                $json['Result'] = 'Location Group not found or inactive';
                return json_encode($json);
            }

            if($locationGroupResult['FacilityID'] != $binData['FacilityID']) {
                $json['Success'] = false;
                $json['Result'] = 'Location Group does not belong to the same facility as the bin';
                return json_encode($json);
            }

            $locationGroupData = mysqli_fetch_assoc($locationGroupResult);
            $locationGroupID = $locationGroupData['GroupID'];

            if ($locationGroupData['LocationType'] != 'WIP') {
                $json['Success'] = false;
                $json['Result'] = 'Selected Location Group is not a WIP type';
                return json_encode($json);
            }

            // Find an available (unlocked) location in the WIP location group
            $availableLocationQuery = "SELECT l.LocationID, l.LocationName
                                       FROM location l
                                       WHERE l.GroupID = '$locationGroupID'
                                       AND l.LocationStatus = '1'
                                       AND l.Locked = '2'                                        
                                       ORDER BY l.LocationName
                                       LIMIT 1";
            $availableLocationResult = mysqli_query($this->connectionlink, $availableLocationQuery);

            if (!$availableLocationResult || mysqli_num_rows($availableLocationResult) == 0) {
                $json['Success'] = false;
                $json['Result'] = 'No available unlocked locations found in the selected WIP Location Group';
                return json_encode($json);
            }

            $locationData = mysqli_fetch_assoc($availableLocationResult);

            // Lock the location first
            $lockLocationQuery = "UPDATE location SET Locked = '1', DateUpdated = NOW(), UpdatedBy = '$userId',currentItemType = 'Bin',currentItemID = '{$binData['BinName']}' WHERE LocationID = '{$locationData['LocationID']}'";
            $lockLocationResult = mysqli_query($this->connectionlink, $lockLocationQuery);

            if (!$lockLocationResult) {
                $json['Success'] = false;
                $json['Result'] = 'Failed to lock location: ' . mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            // Reactivate the bin by updating status and assigning to WIP location
            $updateQuery = "UPDATE custompallet SET
                StatusID = '1',
                LocationID = '{$locationData['LocationID']}',
                GroupID = '$locationGroupID',
                DeletedDate = NULL,
                DeletedBy = NULL,
                LastModifiedDate = NOW(),
                LastModifiedBy = '$userId'
                WHERE CustomPalletID = '$customPalletID'";

            $updateResult = mysqli_query($this->connectionlink, $updateQuery);

            if (!$updateResult) {
                // If bin update fails, unlock the location
                $unlockLocationQuery = "UPDATE location SET Locked = '2', LastModifiedDate = NOW(), LastModifiedBy = '$userId',currentItemType = '',currentItemID = '' WHERE LocationID = '{$locationData['LocationID']}'";
                mysqli_query($this->connectionlink, $unlockLocationQuery);

                $json['Success'] = false;
                $json['Result'] = 'Failed to reactivate bin: ' . mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            // Add tracking record for bin reactivation
            $trackingAction = "Bin reactivated and moved to WIP Location Group '{$locationGroupData['GroupName']}', Location '{$locationData['LocationName']}' (Location Locked)";
            $trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
                              VALUES ('".mysqli_real_escape_string($this->connectionlink, $customPalletID)."',
                                      '".mysqli_real_escape_string($this->connectionlink, $binData['BinName'])."',
                                      '".mysqli_real_escape_string($this->connectionlink, $trackingAction)."',
                                      NOW(),
                                      '".mysqli_real_escape_string($this->connectionlink, $userId)."',
                                      'Archive Bins Page')";
            $trackingResult = mysqli_query($this->connectionlink, $trackingQuery);

            if (!$trackingResult) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);                
            }

            $json['Success'] = true;
            $json['Result'] = "Bin '{$binData['BinName']}' reactivated successfully and moved to {$locationGroupData['GroupName']} - {$locationData['LocationName']} (Location Locked)";

        } catch (Exception $e) {
            $json['Success'] = false;
            $json['Result'] = 'Error: ' . $e->getMessage();
        }

        return json_encode($json);
    }

    public function GetLocationGroups($data) {
        if (!isset($_SESSION['user'])) {
            $json['Success'] = false;
            $json['Result'] = 'Login to continue';
            return json_encode($json);
        }

        $json = array(
            'Success' => false,
            'Result' => 'No data',
            'Data' => array()
        );
        

        if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Bin')) {
            $json['Success'] = false;
            $json['Result'] = 'No Access to Bin Page';
            return json_encode($json);
        }

        try {
            $search = isset($data['search']) ? mysqli_real_escape_string($this->connectionlink, $data['search']) : '';
            $wipOnly = isset($data['wipOnly']) && $data['wipOnly'] === true;

            $whereClause = "WHERE lg.LocationStatus = '1' and lg.FacilityID = '".$_SESSION['user']['FacilityID']."'";

            if ($wipOnly) {
                $whereClause .= " AND lg.LocationType = 'WIP'";
            }

            if (!empty($search)) {
                $whereClause .= " AND lg.GroupName LIKE '%$search%'";
            }

            // Only show location groups that have available unlocked locations
            $query = "SELECT lg.GroupID, lg.GroupName, lg.LocationType,
                             COUNT(l.LocationID) as AvailableLocations
                      FROM location_group lg
                      LEFT JOIN location l ON lg.GroupID = l.GroupID
                          AND l.LocationStatus = '1'
                          AND l.Locked = '2'                           
                      $whereClause
                      GROUP BY lg.GroupID, lg.GroupName, lg.LocationType
                      HAVING AvailableLocations > 0
                      ORDER BY lg.GroupName
                      LIMIT 20";

            $result = mysqli_query($this->connectionlink, $query);

            if (!$result) {
                $json['Success'] = false;
                $json['Result'] = 'Database error: ' . mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $locationGroups = array();
            while ($row = mysqli_fetch_assoc($result)) {
                $locationGroups[] = array(
                    'GroupID' => $row['GroupID'],
                    'GroupName' => $row['GroupName'],
                    'GroupType' => $row['GroupType'],
                    'AvailableLocations' => $row['AvailableLocations']
                );
            }

            $json['Success'] = true;
            $json['Data'] = $locationGroups;
            $json['Result'] = count($locationGroups) . ' location groups with available locations found';

        } catch (Exception $e) {
            $json['Success'] = false;
            $json['Result'] = 'Error: ' . $e->getMessage();
        }

        return json_encode($json);
    }
}
?>
