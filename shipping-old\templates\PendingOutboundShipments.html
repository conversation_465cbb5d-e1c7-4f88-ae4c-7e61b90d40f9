<div class="page" data-ng-controller="pending_outbound_shipments">
    <div class="row ui-section">            
        <div class="col-md-12">
            <article class="article">
                <style>
                    .icon_btn{padding: 0 !important;  margin: 0 !important;  line-height: 24px !important;  min-height: 24px !important; height: 24px !important;}
                    .table > thead > tr > th, .table > thead > tr > td, .table > tbody > tr > th, .table > tbody > tr > td, .table > tfoot > tr > th, .table > tfoot > tr > td{padding: 4px 8px 4px 0px !important;}
                </style>

                <script type="text/ng-template" id="password.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Closing Container (Container ID : {{CurrentContainer.ShippingContainerID}})
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">
                                <form name="tpvForm">
                                    <md-input-container class="md-block">
                                        <label>Controller</label>
                                        <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="100" type="text" ng-enter="FocusNextField('password','0')">
                                        <div ng-messages="tpvForm.AuditController.$error" multiple ng-if='tpvForm.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>
                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="FocusNextField('SealID','0')">
                                        <div ng-messages="tpvForm.Password.$error" multiple ng-if='tpvForm.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Seal ID</label>
                                        <input required name="SealID" id="SealID" ng-model="confirmDetails.NewSealID" ng-maxlength="100" type="SealID" ng-enter="FocusNextField('ContainerWeight','0')">
                                        <div ng-messages="tpvForm.SealID.$error" multiple ng-if='tpvForm.SealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Weight</label>
                                        <input required name="ContainerWeight" id="ContainerWeight" ng-model="confirmDetails.ContainerWeight" ng-max="999999" ng-min="0" type="number" ng-enter="hide()">
                                        <div ng-messages="tpvForm.SealID.$error" multiple ng-if='tpvForm.SealID.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button>
                        </div>
                    </div>
                </script>


                <script type="text/ng-template" id="password1.html">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            TPVR for Moving Container out of Quarantine (Container ID : {{CurrentPallet.ShippingContainerID}})
                        </div>
                        <div class="panel-body">
                            <div class="form-horizontal verification-form">                                
                                <form name="tpvForm1">
                                    <md-input-container class="md-block">
                                        <label>Audit Controller</label>
                                        <input required name="AuditController" id="AuditController1" ng-model="confirmDetails1.AuditController" ng-enter="FocusPasswordField()" ng-maxlength="50" type="text" autocomplete="off">
                                        <div ng-messages="tpvForm1.AuditController.$error" multiple ng-if='tpvForm1.AuditController.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>

                                    <md-input-container class="md-block">
                                        <label>Password</label>
                                        <input required name="Password" id="Password1" ng-model="confirmDetails1.Password" ng-maxlength="50" type="password" ng-enter="hide1()" autocomplete="off">
                                        <div ng-messages="tpvForm1.Password.$error" multiple ng-if='tpvForm1.Password.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="maxlength">Max length 50.</div>
                                        </div>
                                    </md-input-container>
                                </form>
                            </div>

                        </div>

                        <div class="panel-footer text-center">
                            <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel1()">Close</button>
                            <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide1()" ng-disabled="!confirmDetails1.Password">Continue</button>
                        </div>
                    </div>
                </script>


                <md-card class="no-margin-h pt-0">
                    
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ShipmentremovallistPanel = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" >
                            <i class="material-icons md-primary" ng-show="ShipmentremovallistPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ShipmentremovallistPanel">keyboard_arrow_down</i>
                            <span ng-click="ShipmentremovallistPanel = !ShipmentremovallistPanel">Pending Outbound Containers</span>
                            <div flex></div>
                            <a ng-click="RemoveshipXLS()" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                <md-icon class="excel_icon mr-5" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                            </a>
                        </div>
                    </md-toolbar>
                    
                    <md-card-content style="padding: 0px 16px;" ng-show="ShipmentremovallistPanel">
                        <!-- <md-table-container> -->
                            <div class="row"  ng-show="Shipments.length > 0">
                                <div class="col-md-12">         
                                    <div ng-show="Shipments.length > 0" class="pull-right mt-10">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>                            
                                    <div class="table-responsive" style="overflow: auto;">                                        
                                        
                                                    
                                        <table class="table mb-0 multi-tables" md-table>
        
                                            <thead md-head>
        
                                                <tr class="th_sorting" md-row>
                                                    <th style="min-width: 110px;">
                                                        Action
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ShippingContainerID')" ng-class="{'orderby' : OrderBy == 'ShippingContainerID'}">
                                                        <div style="min-width: max-content;">                               
                                                            Container ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ShippingContainerID'"></i>                                 
                                                            <span ng-show="OrderBy == 'ShippingContainerID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ShippingID')" ng-class="{'orderby' : OrderBy == 'ShippingID'}">
                                                        <div style="min-width: 110px;">                               
                                                            Ticket ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ShippingID'"></i>                                 
                                                            <span ng-show="OrderBy == 'ShippingID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ShippingNotes')" ng-class="{'orderby' : OrderBy == 'ShippingNotes'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Expected Shipment <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ShippingNotes'"></i>                                    
                                                            <span ng-show="OrderBy == 'ShippingNotes'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <!-- <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('PalletID')" ng-class="{'orderby' : OrderBy == 'PalletID'}">
                                                        <div>                               
                                                            Pallet ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PalletID'"></i>                                 
                                                            <span ng-show="OrderBy == 'PalletID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th> -->


                                                    <th md-column>
                                                        <div> 
                                                        Move to Shipment
                                                        </div>
                                                    </th>
                                                    
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('packageName')" ng-class="{'orderby' : OrderBy == 'packageName'}">
                                                        <div style="min-width: max-content;">                               
                                                            Container Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'packageName'"></i>                                 
                                                            <span ng-show="OrderBy == 'packageName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('PartTypeSummary')" ng-class="{'orderby' : OrderBy == 'PartTypeSummary'}">
                                                        <div style="min-width: max-content;">                               
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'PartTypeSummary'"></i>                                 
                                                            <span ng-show="OrderBy == 'PartTypeSummary'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('SealID')" ng-class="{'orderby' : OrderBy == 'SealID'}">
                                                        <div style="min-width: max-content;">                               
                                                            Seal <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SealID'"></i>                                 
                                                            <span ng-show="OrderBy == 'SealID'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ContainerWeight')" ng-class="{'orderby' : OrderBy == 'ContainerWeight'}">
                                                        <div style="min-width: max-content;">                               
                                                            Weight <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ContainerWeight'"></i>                                 
                                                            <span ng-show="OrderBy == 'ContainerWeight'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">                           
                                                        <div style="min-width: max-content;">                               
                                                            Removal Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>                                    
                                                            <span ng-show="OrderBy == 'disposition'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <!-- <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                           
                                                        <div style="min-width: max-content;">                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                    
                                                            <span ng-show="OrderBy == 'FacilityName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('VendorName')" ng-class="{'orderby' : OrderBy == 'VendorName'}">                           
                                                        <div style="min-width: max-content;">                               
                                                            Destination <i class="fa fa-sort pull-right" ng-show="OrderBy != 'VendorName'"></i>                                    
                                                            <span ng-show="OrderBy == 'VendorName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <!-- <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ContactName')" ng-class="{'orderby' : OrderBy == 'ContactName'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Dest POC <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ContactName'"></i>                                    
                                                            <span ng-show="OrderBy == 'ContactName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th> -->

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('GroupName')" ng-class="{'orderby' : OrderBy == 'GroupName'}"> 
                                                        <div style="min-width: 160px;">                               
                                                            Location Group<i class="fa fa-sort pull-right" ng-show="OrderBy != 'GroupName'"></i>                                    
                                                            <span ng-show="OrderBy == 'GroupName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('LocationName')" ng-class="{'orderby' : OrderBy == 'LocationName'}"> 
                                                        <div style="min-width: 160px;">                               
                                                            Location <i class="fa fa-sort pull-right" ng-show="OrderBy != 'LocationName'"></i>                                    
                                                            <span ng-show="OrderBy == 'LocationName'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>                                    
                                                            <span ng-show="OrderBy == 'Status'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('RemovalCode')" ng-class="{'orderby' : OrderBy == 'RemovalCode'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Removal Code <i class="fa fa-sort pull-right" ng-show="OrderBy != 'RemovalCode'"></i>                                    
                                                            <span ng-show="OrderBy == 'RemovalCode'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ContainerNotes')" ng-class="{'orderby' : OrderBy == 'ContainerNotes'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Container Notes <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ContainerNotes'"></i>                                    
                                                            <span ng-show="OrderBy == 'ContainerNotes'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                     
                                                     <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ReferenceID')" ng-class="{'orderby' : OrderBy == 'ReferenceID'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Reference ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ReferenceID'"></i>                                    
                                                            <span ng-show="OrderBy == 'ReferenceID'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ReferenceType')" ng-class="{'orderby' : OrderBy == 'ReferenceType'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Reference Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'ReferenceType'"></i>                                    
                                                            <span ng-show="OrderBy == 'ReferenceType'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('BatchRecovery')" ng-class="{'orderby' : OrderBy == 'BatchRecovery'}"> 
                                                        <div style="min-width: max-content;">                               
                                                            Batch Recovery <i class="fa fa-sort pull-right" ng-show="OrderBy != 'BatchRecovery'"></i>                                    
                                                            <span ng-show="OrderBy == 'BatchRecovery'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="min-width: max-content;">Container</th>
                                                    <th style="min-width: max-content;">Remove</th>

                                                    <!-- <th>
                                                        <div style="width:80px; min-width: 80px;">Export</div>
                                                    </th>                                                 -->
                                                </tr>
                                                
                                                <tr md-row class="errornone">
                                                    <td></td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ShippingContainerID" id="ShippingContainerID" ng-model="filter_text[0].ShippingContainerID" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ShippingID" ng-model="filter_text[0].ShippingID" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ShippingNotes" ng-model="filter_text[0].ShippingNotes" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PalletID" ng-model="filter_text[0].PalletID" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->

                                                    <td></td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="packageName" ng-model="filter_text[0].packageName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="PartTypeSummary" ng-model="filter_text[0].PartTypeSummary" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SealID" ng-model="filter_text[0].SealID" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ContainerWeight" ng-model="filter_text[0].ContainerWeight" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <!-- <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td> -->
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="VendorName" ng-model="filter_text[0].VendorName" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <!-- <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ContactName" ng-model="filter_text[0].ContactName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>   -->

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="GroupName" ng-model="filter_text[0].GroupName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LocationName" ng-model="filter_text[0].LocationName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>

                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="RemovalCode" ng-model="filter_text[0].RemovalCode" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ContainerNotes" ng-model="filter_text[0].ContainerNotes" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ReferenceID" ng-model="filter_text[0].ReferenceID" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="ReferenceType" ng-model="filter_text[0].ReferenceType" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td md-cell>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="BatchRecovery" ng-model="filter_text[0].BatchRecovery" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td></td>
                                                    <td></td>
                                                    <!-- <td></td> -->
                                                </tr>
                                            </thead>
        
                                            <tbody md-body ng-show="Shipments.length > 0" ng-repeat="ship in Shipments">
                                                <tr md-row ng-class="{'info' : ship.Status == 'Closed' && GetHoursDifference(ship.RecentSealDate) >= 7 && GetHoursDifference(ship.RecentSealDate) < 14,'warning' : ship.Status == 'Closed' && GetHoursDifference(ship.RecentSealDate) >= 14 && GetHoursDifference(ship.RecentSealDate) < 21, 'danger' : ship.Status == 'Closed' && GetHoursDifference(ship.RecentSealDate) >= 21}">
                                                    <td class="actionicons" md-cell style="min-width: 60px;" ng-init="ship.showDetails = false;">                                                          
                                                        <a href="{{host}}label/master/examples/ShipmentContainerlabel.php?id={{ship.ShippingContainerID}}" target="_blank">
                                                        <i class="material-icons print">print</i></a>

                                                        <a href="{{host}}label/master/examples/ShipmentOutboundContainerlabel.php?id={{ship.ShippingContainerID}}" target="_blank">
                                                        <i class="material-icons print">print</i></a>

                                                        <!-- <a href="#!/ShipmentContainer/{{ship.ShippingContainerID}}" ng-show="!ship.ShippingID">
                                                            <i class="material-icons edit text-danger">edit</i>
                                                        </a>   
                                                        
                                                        <a href="#!/ShipmentPrep/{{ship.ShippingID}}" ng-show="ship.ShippingID">
                                                            <i class="material-icons edit text-danger">edit</i>
                                                        </a> -->


                                                        <a ng-show="ship.ASNContainer == '0'">
                                                            <i class="material-icons edit text-danger" ng-click="EditContainer(ship)">edit</i>
                                                        </a>                                                                                                                                                                
                                                    </td>

                                                    <td md-cell>
                                                        {{ship.ShippingContainerID}}
                                                    </td>

                                                    <td md-cell>
                                                        {{ship.ShippingID}}
                                                    </td>

                                                    <td md-cell>                                                        
                                                        <md-input-container class="md-block tdinput includedsearch">
                                                            <input name="ShippingNotes" ng-model="ship.ShippingNotes" maxlength="100" ng-enter="UpdateShippingNotes(ship,$event)">
                                                            <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="UpdateShippingNotes(ship,$event)">
                                                                <i ng-show="! ship.busy" class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                                <span ng-show="ship.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                            </md-button>
                                                        </md-input-container>
                                                    </td>

                                                    <!-- <td md-cell>                                                        
                                                        <span ng-show="ship.PalletID != '' && ship.PalletID != NULL" style="display: flex;">
                                                            <span class="mr-5">{{ship.PalletID}}</span>
                                                            <span><i class="material-icons add text-danger" ng-click="RemovePalletFromContainer(ship,$event)">close</i></span>
                                                        </span>
                                                        <span ng-show="ship.PalletID == '' || ship.PalletID == NULL ">                                                            
                                                            <button class="md-button md-raised md-accent"  style="min-height:30px; line-height:30px;" type="button" ng-click="MoveContainerToPallet(ship,$event)">                                                               
                                                                <span ng-show="! ship.busy">Generate</span>
                                                                <span ng-show="ship.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>    
                                                            </button>
                                                        </span>
                                                    </td> -->

                                                    <td md-cell>
                                                        <div ng-show = '! ship.ShippingID'>
                                                            <!-- <md-input-container class="md-block tdinput includedsearch">
                                                                <input name="MoveToShipment" ng-model="ship.MoveToShipment" maxlength="50" ng-enter="MoveContainer(ship,$event)">
                                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-disabled="! ship.MoveToShipment" ng-click="MoveContainer(ship,$event)">
                                                                    <i ng-show="! ship.busy" class="material-icons" style="margin-top: 3px; margin-left: 3px;">arrow_forward</i>
                                                                    <span ng-show="ship.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                                </md-button>
                                                            </md-input-container>-->
                                                            <md-button class="md-button md-raised md-primary" ng-click="MoveContainer(ship,$event)" ng-disabled="!ship.ShippingNotes" style="min-height:30px; line-height:30px; min-width: 70px;">
                                                                <span ng-show="!ship.busy">Move</span>
                                                                <span ng-show="ship.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                            </md-button>
                                                        </div>                                                        
                                                    </td>

                                                    <td md-cell>
                                                        {{ship.packageName}}
                                                    </td>

                                                    <td md-cell>
                                                        {{ship.PartTypeSummary}}
                                                    </td>

                                                    <td md-cell>
                                                        {{ship.SealID}}
                                                    </td>

                                                    <td md-cell>
                                                        {{ship.ContainerWeight}}
                                                    </td>
                                                    
                                                    <td md-cell>
                                                        {{ship.disposition}}
                                                    </td>
                                                    <!-- <td md-cell>
                                                        {{ship.FacilityName}}
                                                    </td> -->
                                                    <td md-cell>
                                                        {{ship.VendorName}}
                                                    </td>
                                                    <!-- <td md-cell>
                                                        {{ship.ContactName}}
                                                    </td> -->

                                                    <td md-cell>
                                                        <div class="autocomplete insideuse">
                                                            <md-autocomplete required style="width: 120px;" ng-enter="UpdateContainerLocationGroup(ship,$event)" 
                                                                ng-disabled=""
                                                                md-no-cache="noCache"    
                                                                md-search-text-change="ContainerLocationChange1(ship.group,ship)"      
                                                                md-search-text="ship.group"                                  
                                                                md-items="item in queryContainerLocationSearch1(ship.group,ship)"
                                                                md-item-text="item.GroupName"
                                                                md-selected-item-change="selectedContainerLocationChange1(item,ship)"
                                                                md-min-length="0"    
                                                                ng-model-options='{ debounce: 1000 }'                                
                                                                placeholder="Search Location Group">
                                                                <md-item-template>
                                                                    <span md-highlight-text="ship.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                                                </md-item-template>
                                                                <md-not-found>
                                                                    No Records matching "{{ship.group}}" were found.                                    
                                                                </md-not-found>
                                                            </md-autocomplete>                                                    
                                                            <button class="md-button md-raised md-accent icon_btn"  type="button" ng-disabled="! ship.group" ng-click="UpdateContainerLocationGroup(ship,$event)">                                                                
                                                                <i ng-show="! ship.busy" class="material-icons">done</i>
                                                                <span ng-show="ship.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                            </button>                                                            
                                                        </div>
                                                    </td>
                                                    
                                                    <td md-cell>
                                                        {{ship.LocationName}}


                                                        <div class="autocomplete insideuse" style="display: none;">
                                                            <md-autocomplete required style="width: 120px;" ng-enter="UpdateContainerLocation(ship,$event)" 
                                                                ng-disabled=""
                                                                md-no-cache="noCache"    
                                                                md-search-text-change="ContainerLocationChange(ship.LocationName,ship)"      
                                                                md-search-text="ship.LocationName"                                  
                                                                md-items="item in queryContainerLocationSearch(ship.LocationName,ship)"
                                                                md-item-text="item.LocationName"
                                                                md-selected-item-change="selectedContainerLocationChange(item,ship)"
                                                                md-min-length="0"     
                                                                ng-model-options='{ debounce: 1000 }'                               
                                                                placeholder="Search Location">
                                                                <md-item-template>
                                                                    <span md-highlight-text="ship.LocationName" md-highlight-flags="^i">{{item.LocationName}}</span>
                                                                </md-item-template>
                                                                <md-not-found>
                                                                    No Records matching "{{ship.LocationName}}" were found.                                    
                                                                </md-not-found>
                                                            </md-autocomplete>                                                    
                                                            <button class="md-button md-raised md-accent icon_btn"  type="button" ng-disabled="! ship.LocationName" ng-click="UpdateContainerLocation(ship,$event)">
                                                                <!-- Update -->
    
                                                                <i ng-show="! ship.busy" class="material-icons">done</i>
                                                                <span ng-show="ship.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
    
                                                            </button>
                                                            <!-- <button  style="min-width: 40px; min-height: 30px; margin-top: -6px;" class="md-button md-raised md-accent"  type="button" ng-disabled="! pallet.location" ng-click="ValidateQuarantineToActiveTPVRControllerPopup(pallet,$event)">Update</button> -->
                                                        </div>

                                                    </td>
                                                    <td md-cell>
                                                        {{ship.Status}}

                                                        <md-button class="md-button md-raised md-primary" ng-click="ValidateQuarantineToActiveTPVRControllerPopup(ship,$event)" style="min-height:30px; line-height:30px; min-width: 70px;" ng-show="ship.Status == 'Quarantine' ">
                                                            <span ng-show="!ship.busy">Active</span>
                                                            <span ng-show="ship.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                                        </md-button>

                                                    </td>

                                                    <td md-cell>
                                                        {{ship.RemovalCode}}
                                                    </td>
                                                    
                                                    <td md-cell>
                                                        {{ship.ContainerNotes}}
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.ReferenceID}}
                                                    </td>
                                                    <td md-cell>
                                                        {{ship.ReferenceType}}
                                                    </td>
                                                    <td>
                                                        <span ng-if="ship.BatchRecovery == '1'">Yes</span>
                                                        <span ng-if="ship.BatchRecovery == '0'">No</span>
                                                    </td> 

                                                    <td md-cell style="text-align: center;">                                                    
                                                        <md-button class="md-button md-raised md-default" ng-click="CloseContainer(ship,$event)" ng-show="ship.StatusID == '1'" style="min-height:30px; line-height:30px; min-width: 70px;"><span class="text-danger">Close</span></md-button>
                                                        <md-button class="md-button md-raised md-primary" ng-click="ReopenContainer(ship,$event)" ng-show="ship.StatusID == '6' && ship.ASNContainer == '0'" style="min-height:30px; line-height:30px; min-width: 70px;">Reopen</md-button>
                                                    </td>

                                                    <td md-cell style="text-align: center;">  
                                                        <i class="material-icons text-danger" style="cursor: pointer;" ng-click="DeleteContainer(ship,$event)">delete</i>                                                  
                                                        <!--<md-button class="md-button md-raised md-default" ng-click="DeleteContainer(ship,$event)" style="min-height:30px; line-height:30px;"><span class="text-danger">Delete</span></md-button>-->
                                                    </td>

                                                    <!-- <td md-cell>
                                                        <a class="md-button md-raised md-default" ng-click="exportshipment(ship.ShippingID)" style="min-height:30px; line-height:30px;">
                                                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg"></md-icon> <span>Export</span>
                                                        </a>
                                                    </td> -->
                                                </tr>                                                
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="20">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>
                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        <!-- </md-table-container> -->
                    </md-card-content>
                </md-card>



                <!--List Start-->                
                <md-card class="no-margin-h" style="display: none;">
                    
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ShipmentremovallistPanel = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="ShipmentremovallistPanel = !ShipmentremovallistPanel">
                            <i class="material-icons md-primary" ng-show="ShipmentremovallistPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ShipmentremovallistPanel">keyboard_arrow_down</i>
                            <span>list Of Shipment Removal</span>
                        </div>
                    </md-toolbar>
                    
                    <md-card-content style="padding: 0px 16px;" ng-show="ShipmentremovallistPanel">
                        <md-table-container>
                            <table md-table class="table mb-0 multi-tables">
                                <thead md-head>
                                    <tr>
                                        <th md-column>Action</th>
                                        <th md-column style="min-width: 160px;">Ticket ID</th>
                                        <th md-column>Removal Type</th>
                                        <th md-column>Destination Location</th>
                                        <th md-column>Destination POC</th> 
                                        <th md-column></th>
                                        <th md-column></th>
                                        <th md-column>Delete</th>                                                                                                                                                
                                    </tr>
                                </thead>
                                <tbody md-body>
                                    <tr md-row>
                                        <td md-cell class="actionicons">  
                                            <i class="material-icons add text-warning" ng-click="ContainerPanel0 = !ContainerPanel0" ng-show="ContainerPanel0">remove</i>
                                            <i class="material-icons add text-success" ng-click="ContainerPanel0 = !ContainerPanel0" ng-show="! ContainerPanel0">add</i>                                           
                                            <i class="material-icons edit text-danger">edit</i>
                                            <i class="material-icons print">print</i>
                                        </td>
                                        <td md-cell>WW-RZRR-123</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>CVG110</td>
                                        <td md-cell>caseyc</td>
                                        <td md-cell>
                                            <button class="md-button md-raised md-primary" style="display: flex; min-height: 30px; min-width: 100px;">
                                                <i class="material-icons mr-5">local_shipping</i> Remove
                                            </button>
                                        </td>
                                        <td md-cell>
                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                <md-select class="bordered" name="Deleteshipment0" placeholder="Choose Bin" ng-model="shipmentprepremoval.Deleteshipment0" required>
                                                    <md-option value="dlship1"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                    <md-option value="dlship12"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                </md-select>
                                            </md-input-container>
                                        </td>
                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>                              
                                    </tr>

                                    <tr ng-show="ContainerPanel0">
                                        <td colspan="8">
                                            
                                            <table md-table class="table" style="background-color: transparent;">
                                                <thead md-head>
                                                    <tr md-row>
                                                        <th style="min-width:100px" md-column>Action</th>
                                                        <th md-column>Container ID</th>
                                                        <th md-column>Container Type</th>
                                                        <th md-column>Custom ID</th>
                                                        <th md-column>Seal ID</th>
                                                        <th md-column>Controller Login</th>
                                                        <th md-column>Container Notes</th>
                                                        <th md-column></th>
                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                    </tr>
                                                </thead>
                                                <tbody md-body>
                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel00 = !SerialIdsPanel00" ng-show="SerialIdsPanel00">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel00 = !SerialIdsPanel00" ng-show="! SerialIdsPanel00">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003444</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>22334455</td>
                                                        <td md-cell>B003456</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                <md-select class="bordered" name="DeleteLocation1122" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation1122" required>
                                                                    <md-option value="dlbin1122"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin11222"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr>

                                                    <tr ng-show="SerialIdsPanel00">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th> 
                                                                        <th md-column></th> 
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341920</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-NS1</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput"  style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationOne1" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationOne1" required>
                                                                                    <md-option value="dlbinone1"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4one12"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>

                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel11 = !SerialIdsPanel11" ng-show="SerialIdsPanel11">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel11 = !SerialIdsPanel11" ng-show="! SerialIdsPanel11">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003466</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>24334456</td>
                                                        <td md-cell>B003458</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                <md-select class="bordered" name="DeleteLocation1103" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation1103" required>
                                                                    <md-option value="dlbin1103"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin11032"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr> 
                
                                                    <tr ng-show="SerialIdsPanel11">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th> 
                                                                        <th md-column></th>  
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341909</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-QB</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationTwo2" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationTwo2" required>
                                                                                    <md-option value="dlbintwo2"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4two22"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>
                                                                                  
                                                </tbody>
                
                                            </table>

                                        </td>
                                    </tr>

                                    <tr md-row>
                                        <td md-cell class="actionicons">  
                                            <i class="material-icons add text-warning" ng-click="ContainerPanel1 = !ContainerPanel1" ng-show="ContainerPanel1">remove</i>
                                            <i class="material-icons add text-success" ng-click="ContainerPanel1 = !ContainerPanel1" ng-show="! ContainerPanel1">add</i>                                           
                                            <i class="material-icons edit text-danger">edit</i>
                                            <i class="material-icons print">print</i>
                                        </td>
                                        <td md-cell>WW-RZRR-125</td>
                                        <td md-cell>n/a</td>
                                        <td md-cell>CVG110</td>
                                        <td md-cell>caseyc</td>
                                        <td md-cell>
                                            <button class="md-button md-raised md-primary" style="display: flex; min-height: 30px; min-width: 100px;">
                                                <i class="material-icons mr-5">local_shipping</i> Remove
                                            </button>
                                        </td>
                                        <td md-cell>
                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                <md-select class="bordered" name="Deleteshipment1" placeholder="Choose Bin" ng-model="shipmentprepremoval.Deleteshipment1" required>
                                                    <md-option value="dlship21"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                    <md-option value="dlship22"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                </md-select>
                                            </md-input-container>
                                        </td>
                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>                              
                                    </tr>
                                    
                                    <tr ng-show="ContainerPanel1">
                                        <td colspan="8">
                                            
                                            <table md-table class="table" style="background-color: transparent;">
                                                <thead md-head>
                                                    <tr md-row>
                                                        <th style="min-width:100px" md-column>Action</th>
                                                        <th md-column>Container ID</th>
                                                        <th md-column>Container Type</th>
                                                        <th md-column>Custom ID</th>
                                                        <th md-column>Seal ID</th>
                                                        <th md-column>Controller Login</th>
                                                        <th md-column>Container Notes</th>
                                                        <th md-column></th>
                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                    </tr>
                                                </thead>
                                                <tbody md-body>
                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel0 = !SerialIdsPanel0" ng-show="SerialIdsPanel0">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel0 = !SerialIdsPanel0" ng-show="! SerialIdsPanel0">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003456</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>22334455</td>
                                                        <td md-cell>B003456</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style=" display: none;">
                                                                <md-select class="bordered" name="DeleteLocation112" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation112" required>
                                                                    <md-option value="dlbin1"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin2"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr>

                                                    <tr ng-show="SerialIdsPanel0">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th>
                                                                        <th md-column></th>  
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341920</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-NS1</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationOne" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationOne" required>
                                                                                    <md-option value="dlbin3"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>

                                                    <tr md-row>
                                                        <td md-cell class="actionicons">  
                                                            <i class="material-icons add text-warning" ng-click="SerialIdsPanel1 = !SerialIdsPanel1" ng-show="SerialIdsPanel1">remove</i>
                                                            <i class="material-icons add text-success" ng-click="SerialIdsPanel1 = !SerialIdsPanel1" ng-show="! SerialIdsPanel1">add</i>                                           
                                                            <i class="material-icons edit text-danger">edit</i>
                                                            <i class="material-icons print">print</i>
                                                        </td>
                                                        <td md-cell>B003458</td>
                                                        <td md-cell>Cotton Box</td>
                                                        <td md-cell>24334456</td>
                                                        <td md-cell>B003458</td>
                                                        <td md-cell>krogerm</td>
                                                        <td md-cell>n/a</td>
                                                        <td md-cell>
                                                            <md-input-container md-no-float class="md-block tdinput" style=" display: none;">
                                                                <md-select class="bordered" name="DeleteLocation110" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocation110" required>
                                                                    <md-option value="dlbin11"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                    <md-option value="dlbin22"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                </md-select>
                                                            </md-input-container>
                                                        </td>
                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                    </tr> 
                
                                                    <tr ng-show="SerialIdsPanel1">
                                                        <td colspan="11">
                                                            
                                                            <table md-table class="table nested_tbale" style="margin-left: 30px;">
                                                                <thead md-head>
                                                                    <tr md-row>
                                                                        <th md-column style="min-width: 100px;">SN</th>
                                                                        <th md-column>Part Type</th>
                                                                        <th md-column>MPN</th>
                                                                        <th md-column></th>  
                                                                        <th md-column style="width: 60px;">Delete</th>                                                                                                                                                     
                                                                    </tr>
                                                                </thead>
                                                                <tbody md-body>
                                                                    <tr md-row>
                                                                        <td md-cell>PDX640341909</td>
                                                                        <td md-cell>Hard disk</td>
                                                                        <td md-cell>K2T-NS2</td>
                                                                        <td md-cell>
                                                                            <md-input-container md-no-float class="md-block tdinput" style="min-width: 200px; display: none;">
                                                                                <md-select class="bordered" name="DeleteLocationTwo" placeholder="Choose Bin" ng-model="shipmentprepremoval.DeleteLocationTwo" required>
                                                                                    <md-option value="dlbin3"> CVG110.PARTS.Temporary-Sanitization-004</md-option>
                                                                                    <md-option value="dlbin4"> CVG110.PARTS.Temporary-Sanitization-005</md-option>
                                                                                </md-select>
                                                                            </md-input-container>
                                                                        </td>
                                                                        <td md-cell><i class="material-icons text-danger" style="cursor: pointer;"> close</i></td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                
                                                        </td>
                                                    </tr>
                                                                                  
                                                </tbody>
                
                                            </table>

                                        </td>
                                    </tr>
                                                                  
                                </tbody>

                            </table>
                        </md-table-container>
                    </md-card-content>

                </md-card>
                <!--List Close-->

                <md-card class="no-margin-h" ng-show="CurrentShipment.ShippingID">          
                    <md-toolbar class="md-table-toolbar md-default" ng-init="ShipmentremovalPanel = true;">
                        <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="ShipmentremovalPanel = !ShipmentremovalPanel">                            
                            <i class="material-icons md-primary" ng-show="ShipmentremovalPanel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! ShipmentremovalPanel">keyboard_arrow_down</i>
                            <span>Shipment Removal</span>
                        </div>
                    </md-toolbar>
                    <div class="row" ng-show="ShipmentremovalPanel">  

                        <form name="shipmentForm">
                            <div class="col-md-12">
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Ticket ID</label>
                                        <input name="ShippingID" ng-disabled="true" ng-model="CurrentShipment.ShippingID" />
                                    </md-input-container>
                                </div>                                
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Removal Type</label>
                                        <input name="disposition" ng-disabled="true" ng-model="CurrentShipment.disposition" />
                                    </md-input-container>
                                </div>
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Destination</label>
                                        <input name="VendorName" ng-disabled="true" ng-model="CurrentShipment.VendorName" />
                                    </md-input-container>
                                </div>
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Destination POC</label>
                                        <input name="ContactName" ng-disabled="true" ng-model="CurrentShipment.ContactName" />
                                    </md-input-container>
                                </div>  
                                <div class="col-md-3">
                                    <md-input-container class="md-block" flex-gt-sm>
                                        <label>Next Step Action</label>
                                        <input required name="NextStep" ng-model="CurrentShipment.NextStep" ng-maxlength="1000" >
                                        <div ng-messages="shipmentForm.NextStep.$error" multiple ng-if='shipmentForm.NextStep.$dirty'>                            
                                            <div ng-message="required">This is required.</div> 
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 1000.</div> 
                                        </div>
                                    </md-input-container>
                                </div>                         
                            </div>                        
                            <div class="col-md-12">
                                <div class="bg-grey-light">
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Approver Login</label>
                                            <input type="text" required name="ApproverLogin" ng-model="CurrentShipment.ApproverLogin" ng-maxlength="100" >
                                            <div ng-messages="shipmentForm.ApproverLogin.$error" multiple ng-if='shipmentForm.ApproverLogin.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div> 
                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Approval Date</label>
                                            <md-datepicker name="ApprovedDate" ng-model="CurrentShipment.ApprovedDate" aria-label="Enter date" required ></md-datepicker>
                                            <div ng-messages="shipmentForm.ApprovedDate.$error" multiple ng-if='shipmentForm.ApprovedDate.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>                                    
                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Removal Date</label>
                                            <md-datepicker name="ShippedDate" ng-model="CurrentShipment.ShippedDate" aria-label="Enter date" required ></md-datepicker>
                                            <div ng-messages="shipmentForm.ShippedDate.$error" multiple ng-if='shipmentForm.ShippedDate.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-3">
                                        <!-- <md-input-container class="md-block">
                                            <label>Removal Time</label>
                                            <input type="time" name="RemovalTime" />
                                        </md-input-container> -->

                                        <md-input-container class="md-block">
                                            <label>Removal Time</label>
                                            <input type="time" required name="ShippedTime" ng-model="CurrentShipment.ShippedTime" >
                                            <div ng-messages="shipmentForm.ShippedTime.$error" multiple ng-if='shipmentForm.ShippedTime.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-3">
                                        <md-input-container class="md-block" flex-gt-sm>
                                            <label>Escort Login</label>
                                            <input type="text" required name="EscortLogin" ng-model="CurrentShipment.EscortLogin" ng-maxlength="100" >
                                            <div ng-messages="shipmentForm.EscortLogin.$error" multiple ng-if='shipmentForm.EscortLogin.$dirty'>                            
                                                <div ng-message="required">This is required.</div> 
                                                <div ng-message="minlength">Min length 3.</div>
                                                <div ng-message="maxlength">Max length 100.</div> 
                                            </div>
                                        </md-input-container>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="col-md-12 btns-row">
                                    <button class="md-button md-raised btn-w-md md-default" ng-click="CurrentShipment = {}">
                                        Cancel
                                    </button>                                            
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                        data-ng-disabled="shipmentForm.$invalid || CurrentShipment.busy" ng-click="ShipShipment()">
                                        <span ng-show="! CurrentShipment.busy">Ship</span>
                                        <span ng-show="CurrentShipment.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </div>
                            </div>
                        </form>

                    </div>                    


                </md-card>                


            </article>
        </div>
    </div>
</div>