<?php
session_start();
include_once("../database/Truck.class.php");
$obj = new TruckClass();

	if($_POST['ajax'] == "SaveParkTypeConfiguration"){
		$result = $obj->SaveParkTypeConfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetParkTypeConfigurationDetails"){
		$result = $obj->GetParkTypeConfigurationDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetParkTypeConfigurationList"){
  		$result = $obj->GetParkTypeConfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveTruckTypeConfiguration"){
		$result = $obj->SaveTruckTypeConfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckTypeConfigurationDetails"){
		$result = $obj->GetTruckTypeConfigurationDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckTypeConfigurationList"){
  		$result = $obj->GetTruckTypeConfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveTDRSOPConfiguration"){
		$result = $obj->SaveTDRSOPConfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTDRSOPConfigurationDetails"){
		$result = $obj->GetTDRSOPConfigurationDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTDRSOPConfigurationList"){
  		$result = $obj->GetTDRSOPConfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "SaveParkingLocationConfiguration"){
		$result = $obj->SaveParkingLocationConfiguration($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetParkingLocationConfigurationDetails"){
		$result = $obj->GetParkingLocationConfigurationDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetParkingLocationConfigurationList"){
  		$result = $obj->GetParkingLocationConfigurationList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetParkType"){
  		$result = $obj->GetParkType($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "UploadParkingLocationFile") {
		$result = $obj->UploadParkingLocationFile($_FILES);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateParkTypeConfigurationxls"){
  		$result = $obj->GenerateParkTypeConfigurationxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateTruckTypeConfigurationxls"){
  		$result = $obj->GenerateTruckTypeConfigurationxls($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateTDRSOPConfigurationxls"){
  		$result = $obj->GenerateTDRSOPConfigurationxls($_POST);
		echo $result;
	}
	

?>