<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['TruckListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "TruckBookingList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Truck Booking List');
$header = array('Facility','Load #','Shipment Type','Parking Location','Carrier','Arrival Date','Expected Arrival Time','Expected Departure Time','Load Type','Truck Reg','Trailer No','Driver','Driver ID','Notes','Status');

$sql = "select T.*,PL.ParkingLocationName,F.FacilityName,C.CarrierName from Truck T left join ParkingLocation PL on T.ParkingLocationID = PL.ParkingLocationID left join facility F on T.FacilityID = F.FacilityID left join Carrier C on T.CarrierID = C.CarrierID where T.TruckID";
if($data[0] && count($data[0]) > 0) {
           foreach ($data[0] as $key => $value) {
                if ($value != '') {
                    if ($key == 'FacilityName') {
                        $query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'CarrierName') {
                        $query = $query . " AND C.CarrierName like '%" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ParkingLocationName') {
                        $query = $query . " AND PL.ParkingLocationName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ArrivalType') {
                        $query = $query . " AND T.ArrivalType like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ArrivalDate') {
                        $query = $query . " AND T.ArrivalDate like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'ArrivalTime') {
                        $query = $query . " AND T.ArrivalTime like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'LoadType') {
                        $query = $query . " AND T.LoadType like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'TruckReg') {
                        $query = $query . " AND T.TruckReg like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'DriverName') {
                        $query = $query . " AND T.DriverName like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'DriverID') {
                        $query = $query . " AND T.DriverID like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'Notes') {
                        $query = $query . " AND T.Notes like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'LoadNumber') {
                        $query = $query . " AND T.LoadNumber like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'TrailerNumber') {
                        $query = $query . " AND T.TrailerNumber like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }
                    if ($key == 'Status') {
                        $query = $query . " AND T.Status like '" . mysqli_real_escape_string($connectionlink, $value) . "%' ";
                    }

                }
            }
        }
        if($data['OrderBy'] != '') {
            if($data['OrderByType'] == 'asc') {
                $order_by_type = 'asc';
            } else {
                $order_by_type = 'desc';
            }

            if ($data['OrderBy'] == 'FacilityName') {
                    $query = $query . " order by F.FacilityName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'CarrierName') {
                    $query = $query . " order by C.CarrierName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ParkingLocationName') {
                    $query = $query . " order by PL.ParkingLocationName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ArrivalType') {
                    $query = $query . " order by T.ArrivalType " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ArrivalDate') {
                    $query = $query . " order by T.ArrivalDate " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'ArrivalTime') {
                    $query = $query . " order by T.ArrivalTime " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'LoadType') {
                    $query = $query . " order by T.LoadType " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'TruckReg') {
                    $query = $query . " order by T.TruckReg " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'DriverName') {
                    $query = $query . " order by T.DriverName " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'DriverID') {
                    $query = $query . " order by T.DriverID " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'Notes') {
                    $query = $query . " order by T.Notes " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'LoadNumber') {
                    $query = $query . " order by T.LoadNumber " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'TrailerNumber') {
                    $query = $query . " order by T.TrailerNumber " . $order_by_type . " ";
                } else if ($data['OrderBy'] == 'Status') {
                    $query = $query . " order by T.Status " . $order_by_type . " ";
                } 
        } else {
            $sql = $sql . " order by T.TruckID desc ";
        }
$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);    
}
while($row = mysqli_fetch_assoc($query))
{
    if (!empty($row['ArrivalTime'])) {
        $arrival = new DateTime($row['ArrivalTime']);
        $arrival->modify('+30 minutes');
        $row['DepartureTime'] = $arrival->format('H:i');
    } else {
        $row['DepartureTime'] = null;
    }

    $row2  = array($row['FacilityName'],$row['LoadNumber'],$row['ArrivalType'],$row['ParkingLocationName'],$row['CarrierName'],$row['ArrivalDate'],$row['ArrivalTime'],$row['DepartureTime'],$row['LoadType'],$row['TruckReg'],$row['TrailerNumber'],$row['DriverName'],$row['DriverID'],$row['Notes'],$row['Status']);
    $rows[] = $row2;
}

$sheet_name = 'Truck Booking List';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 