(function () {
    'use strict';

    angular.module('app').controller("shipment_prep_new", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window,$location) {
        
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Shipment Prep',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.COOList = [];
        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.Facilities = [];
        $scope.shipping = {'Bins': []};
        $scope.Dispositions = [];
        $scope.Vendors = [];
        $scope.PackageTypes = [];
        $scope.ContainerSerials = [];
        $scope.ByProducts = [];
        $scope.ReferenceType = [];
        $scope.newBin = {};
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllReferenceTypes',
            success: function (data) {                
                if (data.Success) {                    
                    $scope.ReferenceType = data.Result;         
                } else {                                        
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });








        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSesstionFacility',
            success: function (data) {
                if (data.Success) {                    
                    $scope.shipping.FacilityID = data.FacilityID;         
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );                    
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCOOList',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                    
                    $scope.COOList = data.COOList;         
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );                    
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });


        $scope.GetShippingdetails = function (ShippingID) {
            //$scope.ShippingID = $stateParams.ShippingID;
            $scope.ShippingID = ShippingID;

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetShipmentDetails&ShippingID=' + $scope.ShippingID,
                success: function (data) {
                    if (data.Success) {
                        $scope.GetDispositionVendors(data.Result.disposition_id);
                        $scope.shipping = data.Result;
                        $scope.GetFacilityPackageTypes();
                        $scope.GetFacilityByProducts();
                        $scope.GetDestinationRemovalTypes();

                        $scope.EditCurrentBin();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };

        if ($stateParams.ShippingID) {   
            $scope.GetShippingdetails($stateParams.ShippingID);
        } else {
            $scope.ShippingID = '';
        }

        $scope.GetFacilityPackageTypes = function () {
            console.log('called')
            if($scope.shipping.FacilityID > 0) {

                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetPackageTypes&FacilityID='+$scope.shipping.FacilityID,
                    success: function (data) {
                        if (data.Success) {
                            $scope.PackageTypes = data.Result;
                        } else {
                            $scope.PackageTypes = [];
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $scope.PackageTypes = [];
            }
        };

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.Dispositions = data.Result;
                } else {
                    $scope.Dispositions = [];
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.GetFacilityByProducts = function () {
            //if($scope.shipping.FacilityID > 0 && $scope.shipping.disposition_id > 0) {
            if($scope.newBin.FacilityID > 0 && $scope.newBin.disposition_id > 0) {
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=GetByProducts&FacilityID='+$scope.shipping.FacilityID+'&disposition_id='+$scope.shipping.disposition_id,
                    data: 'ajax=GetByProducts&FacilityID='+$scope.newBin.FacilityID+'&disposition_id='+$scope.newBin.disposition_id,
                    success: function (data) {
                        if (data.Success) {
                            $scope.ByProducts = data.Result;
                        } else {
                            $scope.ByProducts = [];
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.ByProducts = [];
            }
        };
        

        $scope.GetDestinationRemovalTypes = function () {
            if ($scope.shipping.VendorID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetDestinationRemovalTypes&VendorID=' + $scope.shipping.VendorID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.Dispositions = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.Dispositions = [];
                            $scope.shipping.disposition_id = '';
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.shipping.disposition_id = '';
            }
        };

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                } else {
                    $scope.Facilities = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        jQuery.ajax({
            url: host+'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllVendors',
            success: function(data){
                if(data.Success) {
                    $scope.Vendors = data.Result;
                } else {
                    $scope.Vendors = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.GetDispositionVendors = function (disposition_id) {
            if (disposition_id > 0) {
                $scope.Vendors = [];
                $scope.shipping.VendorID = '';
                $scope.shipping.DestinationPOC = '';
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetDispositionVendors&disposition_id=' + disposition_id,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.Vendors = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.Vendors = [];
                            $scope.shipping.VendorID = '';
                            $scope.shipping.DestinationPOC = '';
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });


            } else {
                $scope.Vendors = [];
                $scope.shipping.VendorID = '';
                $scope.shipping.DestinationPOC = '';
            }
        };

        $scope.GetVendorPOC = function () {
            if ($scope.shipping.VendorID > 0) {
                for (var i = 0; i < $scope.Vendors.length; i++) {
                    if ($scope.Vendors[i].VendorID == $scope.shipping.VendorID) {
                        $scope.shipping.DestinationPOC = $scope.Vendors[i].ContactName;
                    }
                }
            } else {
                $scope.shipping.DestinationPOC = '';
            }
        };

        $scope.CancelTicket = function () {
             var facilityID = $scope.shipping.FacilityID; // Preserve FacilityID
            $scope.shipping = {FacilityID: facilityID};
            window.location = "#!/ShipmentPrepNew";
        };

        $scope.CreateShipment = function () {
            if(!$scope.shipping.VendorID && !$scope.shipping.DestinationFacilityID) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Select Destination or Destination Facility')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                $scope.shipping.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateShipment&' + $.param($scope.shipping),
                    success: function (data) {
                        $scope.shipping.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            if (data.ShippingID) {
                                var earl = '/ShipmentPrepNew/' + data.ShippingID;
                                $location.path(earl);
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            }
        };

        // Simple Add Bin Function - shows existing form (matching bin.html)
        $scope.addingBin = false;
        $scope.ManageBin = function () {
            $scope.addingBin = true;

            // Initialize newBin object exactly like cpalletForm in bin.html
            // LocationType will be automatically determined from location group
            $scope.newBin = {
                'ShippingID': $scope.shipping.ShippingID,
                'FacilityID': $scope.shipping.FacilityID,  // Auto-populate from shipment
                'BinType': 'Physical',
                'MaxLimitRequired': '0',
                'CustomerLock': '0',
                'ReferenceIDRequired': '0',
                'AcceptAllDisposition': '0',
                'BinName': '',
                'idPackage': '',
                'disposition_id': '',
                'ReferenceTypeID': '',
                'ReferenceID': '',
                'MaximumAssets': '',
                'AWSCustomerID': '',
                'Description': '',
                'group': '',
                'LocationGroupID': '',
                'LocationName': '',
                'ParentBinName': ''  // Added parent bin field
            };

            // Load required data for the form (matching bin.html)
            $scope.GetBinPackageTypes();  // Load package types for the facility
            $scope.GetAWSCustomers();     // Load customers
            // Reference types are loaded globally on controller init

            // Focus on first field after a short delay
            setTimeout(function () {
                $window.document.getElementById('BinNameID').focus();
            }, 500);
        };

        // GetBinPackageTypes function (exactly matching bin.html)
        $scope.GetBinPackageTypes = function () {
            if($scope.newBin.FacilityID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'administration/includes/admin_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinPackageTypes&FacilityID=' + $scope.newBin.FacilityID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.PackageTypes = data.Result;
                        } else {
                            $scope.PackageTypes = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.PackageTypes = [];
                        $scope.$apply();
                    }
                });
            } else {
                $scope.PackageTypes = [];
            }
        };

        // GetAWSCustomers function
        $scope.GetAWSCustomers = function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetAWSCustomers',
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.customers = data.Result;
                    } else {
                        $scope.customers = [];
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.customers = [];
                    $scope.$apply();
                }
            });
        };

        // Load Reference Types (exactly matching bin.html)
        // jQuery.ajax({
        //     url: host + 'administration/includes/admin_submit.php',
        //     dataType: 'json',
        //     type: 'post',
        //     data: 'ajax=GetAllReferenceTypes',
        //     success: function (data) {
        //         if (data.Success) {
        //             $scope.ReferenceType = data.Result;
        //         } else {
        //             $scope.ReferenceType = [];
        //         }
        //         initSessionTime(); $scope.$apply();
        //     }, error: function (data) {
        //         $scope.ReferenceType = [];
        //         initSessionTime(); $scope.$apply();
        //     }
        // });

        // Helper functions for the bin form

        $scope.clearLocationGroup = function() {
            $scope.newBin.group = '';
        };



        $scope.onMaxLimitRequiredChange = function() {
            if ($scope.newBin.MaxLimitRequired == '0') {
                $scope.newBin.MaximumAssets = '';
            }
        };

        $scope.onCustomerLockChange = function() {
            if ($scope.newBin.CustomerLock == '0') {
                $scope.newBin.AWSCustomerID = '';
            }
        };

        // GetReferenceType function (exactly matching bin.html)
        $scope.GetReferenceType = function () {
            $rootScope.$broadcast('preloader:active');

            $scope.newBin.ReferenceIDRequired = '0';
            $scope.newBin.ReferenceID = '';
            $scope.newBin.ReferenceType = '';

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReferenceType&ReferenceTypeID=' + $scope.newBin.ReferenceTypeID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        if(data.Result) {
                            var reference_type = data.Result;
                            if(reference_type.ReferenceIDRequired == '1') {
                                $scope.newBin.ReferenceIDRequired = '1';
                            }
                            $scope.newBin.ReferenceTypeID = reference_type.ReferenceTypeID;
                            if(reference_type.ReferenceID) {
                                $scope.newBin.ReferenceID = reference_type.ReferenceID;
                            }
                            $scope.newBin.ReferenceType = reference_type.ReferenceType;
                        }
                    } else {
                        $scope.newBin.ReferenceIDRequired = '0';
                        $scope.newBin.ReferenceID = '';
                        $scope.newBin.ReferenceType = '';

                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    $scope.newBin.ReferenceIDRequired = '0';
                    $scope.newBin.ReferenceID = '';
                    $scope.newBin.ReferenceType = '';
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        // Auto Generate Bin Name function for Shipping module
        $scope.AutoGenerateBinName = function() {
            // Validate that container type is selected
            if (!$scope.newBin.idPackage) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please select a Bin Type first')
                        .action('OK')
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-warning md-block')
                );
                return;
            }

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                type: 'post',
                data: {
                    ajax: 'GenerateBinName',
                    PackageID: $scope.newBin.idPackage
                },
                dataType: 'json',
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.newBin.BinName = data.Result;
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Bin name generated successfully: ' + data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success md-block')
                        );
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result || 'Failed to generate bin name')
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-error md-block')
                        );
                    }
                    $scope.$apply();
                },
                error: function(xhr, status, error) {
                    $rootScope.$broadcast('preloader:hide');
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error generating bin name: ' + error)
                            .action('OK')
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-error md-block')
                    );
                    $scope.$apply();
                }
            });
        };



        // GetLocationGroups function
        $scope.GetLocationGroups = function() {
            if($scope.newBin.group && $scope.newBin.group.trim() !== '') {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'administration/includes/admin_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetLocationGroups&LocationGroup=' + encodeURIComponent($scope.newBin.group) + '&FacilityID=' + $scope.newBin.FacilityID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            // Handle successful response
                            console.log('Location Groups loaded:', data.Result);
                            // You can process the location groups data here if needed
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.$apply();
                    },
                    error: function(xhr, status, error) {
                        $rootScope.$broadcast('preloader:hide');
                        console.log('GetLocationGroups Error:', xhr, status, error);
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Error loading location groups')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.$apply();
                    }
                });
            }
        };
        $scope.currentCustomPalletID = '';

        $scope.EditCurrentBin = function () {
            if($scope.currentCustomPalletID > 0 && $scope.shipping.Bins.length > 0) {
                for(var i=0;i<$scope.shipping.Bins.length;i++) {
                    if($scope.shipping.Bins[i].CustomPalletID == $scope.currentCustomPalletID) {
                        $scope.EditBin($scope.shipping.Bins[i]);
                        break;
                    }
                }
            }
        };

        // Create/Update Bin Function
        $scope.CreateBin = function () {
            // Check if this is a child bin
            var isChildBin = $scope.newBin && $scope.newBin.ParentBinName && $scope.newBin.ParentBinName.trim() !== '';

            // Validate mutual exclusivity between Parent Bin and Location Group
            if (isChildBin && $scope.newBin.group && $scope.newBin.group.trim() !== '') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('A bin cannot have both a Parent Bin and a Location Group. Please choose one or the other.')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            // Validate required fields for bin creation/update
            var missingFields = [];
            if (!$scope.newBin.BinName) missingFields.push('Bin Name');
            if (!$scope.newBin.idPackage) missingFields.push('Bin Type');
            if (!$scope.newBin.BinType) missingFields.push('Physical/Logical');
            if (!$scope.newBin.disposition_id) missingFields.push('Disposition');

            // Only validate location fields for non-child bins
            if (!isChildBin) {
                if (!$scope.newBin.group) missingFields.push('Location Group');
            }

            if (missingFields.length > 0) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Please fill all required fields: ' + missingFields.join(', '))
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            $scope.newBin.busy = true;
            $rootScope.$broadcast('preloader:active');

            // Determine if this is an edit or create operation
            var isEdit = $scope.newBin.CustomPalletID && $scope.newBin.CustomPalletID !== '';
            var ajaxAction = isEdit ? 'UpdateBinInShipment' : 'CreateBinAndAddToShipment';

            // Prepare bin data for creation/update with all fields from bin.html
            // LocationType is automatically determined from location group, so not included
            var binData = {
                BinName: $scope.newBin.BinName,
                idPackage: $scope.newBin.idPackage,
                FacilityID: $scope.newBin.FacilityID || $scope.shipping.FacilityID,  // Use newBin.FacilityID or fallback
                BinType: $scope.newBin.BinType,
                disposition_id: $scope.newBin.disposition_id,
                LocationGroupID: $scope.newBin.group,  // Use group name as LocationGroupID
                ParentBinName: $scope.newBin.ParentBinName,  // Add parent bin information
                ReferenceTypeID: $scope.newBin.ReferenceTypeID,
                ReferenceID: $scope.newBin.ReferenceID,
                ReferenceIDRequired: $scope.newBin.ReferenceIDRequired,
                MaxLimitRequired: $scope.newBin.MaxLimitRequired,
                MaximumAssets: $scope.newBin.MaximumAssets,
                CustomerLock: $scope.newBin.CustomerLock,
                AWSCustomerID: $scope.newBin.AWSCustomerID,
                Description: $scope.newBin.Description,
                ShippingID: $scope.shipping.ShippingID,
                AcceptAllDisposition: $scope.newBin.AcceptAllDisposition || '0'
            };

            // Add CustomPalletID for edit operations
            if (isEdit) {
                binData.CustomPalletID = $scope.newBin.CustomPalletID;
            }

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: {
                    ajax: ajaxAction,
                    binData: binData
                },
                success: function (data) {
                    $scope.newBin.busy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if (data.Success) {
                        var successMessage = isEdit ? 'Bin updated successfully' : data.Result;
                        $mdToast.show(
                            $mdToast.simple()
                                .content(successMessage)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );

                        // Reset form and hide the add/edit bin section
                        $scope.newBin = {
                            'ShippingID': $scope.shipping.ShippingID,
                            'FacilityID': $scope.shipping.FacilityID,
                            'BinType': 'Physical',
                            'MaxLimitRequired': '0',
                            'CustomerLock': '0',
                            'ReferenceIDRequired': '0',
                            'AcceptAllDisposition': '0',
                            'BinName': '',
                            'idPackage': '',
                            'disposition_id': '',
                            'ReferenceTypeID': '',
                            'ReferenceID': '',
                            'MaximumAssets': '',
                            'AWSCustomerID': '',
                            'Description': '',
                            'group': ''
                        };
                        $scope.addingBin = false;

                        // Refresh the page to show the updated bin list
                        //location.reload();
                        $scope.GetShippingdetails($scope.shipping.ShippingID);
                        $scope.currentCustomPalletID = data.CustomPalletID;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function (xhr, status, error) {
                    $scope.newBin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    console.log('CreateBin AJAX Error:', xhr, status, error);

                    var errorMessage = 'Error occurred while creating bin';
                    if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.Result) {
                                errorMessage = response.Result;
                            }
                        } catch (e) {
                            errorMessage += ': ' + xhr.responseText;
                        }
                    }

                    $mdToast.show(
                        $mdToast.simple()
                            .content(errorMessage)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        $scope.CreateContainer = function () {
            if ($scope.newBin.CloseContainer && $scope.newBin.SealID == '') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Enter Seal ID')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                //if (!$scope.newBin.PasswordVerified) {
                if (false) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Controller Login ID is not Verified')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    $scope.newBin.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=CreateContainer&' + $.param($scope.newBin) + '&FacilityID=' + $scope.shipping.FacilityID,
                        success: function (data) {
                            $scope.newBin.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            if (data.Success) {
                                if (data.Container) {
                                    $scope.shipping.Containers.push(data.Container);
                                    $scope.newBin = data.Container;
                                    $scope.GetFacilityByProducts();
                                    $scope.newBin.PasswordVerified = true;
                                    $scope.newBin.Notes = 'n/a';
                                    $scope.newBin.SanitizationVerificationID = 'n/a';
                                    $scope.newBin.InventoryNotes = 'n/a';
                                    $scope.newBin.ServerSanitizationVerificationID = 'n/a';
                                    $scope.newBin.sanitization_seal_id = 'n/a';
                                    $scope.newBin.NotesServer = 'n/a';
                                }
                                setTimeout(function () {
                                    $window.document.getElementById('SerialNumber').focus();
                                }, 100);
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }
            }
        };         


        // EditBin function for editing bins
        $scope.EditBin = function (bin) {
            $scope.addingBin = true;
            $scope.newBin = angular.copy(bin); // Use angular.copy to avoid reference issues

            // Check if this is a child bin
            var isChildBin = bin.ParentBinName && bin.ParentBinName.trim() !== '';

            // Set location group for autocomplete (only for non-child bins)
            if (!isChildBin) {
                // Try multiple possible field names for the group
                var groupName = bin.group || bin.GroupName || bin.LocationGroupName || bin.LocationName || '';
                $scope.newBin.group = groupName;

                // For autocomplete to work properly, we need to ensure the field is properly validated
                if (groupName) {
                    // Set the selected item for the autocomplete
                    $scope.newBin.selectedLocationItem = { value: groupName, GroupName: groupName };

                    // Trigger the selection change after a delay to ensure form is ready
                    setTimeout(function() {
                        try {
                            $scope.selectedLocationChange1($scope.newBin.selectedLocationItem);
                            // Force form validation update
                            if ($scope.binForm && $scope.binForm.group) {
                                $scope.binForm.group.$setValidity('required', true);
                                $scope.binForm.group.$setTouched();
                            }
                            $scope.$apply();
                        } catch (e) {
                            // Silently handle any errors
                        }
                    }, 200);
                }
            } else {
                $scope.newBin.group = ''; // Clear for child bins
                $scope.newBin.selectedLocationItem = null;
            }

            // Set parent bin for autocomplete (only for child bins)
            if (isChildBin && bin.ParentBinName) {
                $scope.newBin.ParentBinName = bin.ParentBinName;

                // Set the selected item for the parent bin autocomplete
                $scope.newBin.selectedParentBinItem = {
                    BinName: bin.ParentBinName,
                    CustomPalletID: bin.ParentCustomPalletID,
                    disposition: bin.ParentDisposition || 'Unknown'
                };

                // Trigger the selection change after a delay to ensure form is ready
                setTimeout(function() {
                    try {
                        $scope.selectedParentBinChange($scope.newBin.selectedParentBinItem);
                        // Force form validation update
                        if ($scope.binForm && $scope.binForm.ParentBinName) {
                            $scope.binForm.ParentBinName.$setValidity('required', true);
                            $scope.binForm.ParentBinName.$setTouched();
                        }
                        $scope.$apply();
                    } catch (e) {
                        // Silently handle any errors
                    }
                }, 200);
            } else {
                $scope.newBin.ParentBinName = ''; // Clear for non-child bins
                $scope.newBin.selectedParentBinItem = null;
            }

            // Ensure all required fields are set
            $scope.newBin.FacilityID = bin.FacilityID || $scope.shipping.FacilityID;
            $scope.newBin.ShippingID = $scope.shipping.ShippingID;
            $scope.newBin.MaximumAssets = parseInt($scope.newBin.MaximumAssets) || 0;

            // Store original values for validation (AssetsCount is already copied from bin object)

            $scope.newBin.Notes = 'n/a';
            $scope.newBin.SanitizationVerificationID = 'n/a';
            $scope.newBin.InventoryNotes = 'n/a';
            $scope.newBin.NotesServer = 'n/a';

            $scope.newBin.ServerSanitizationVerificationID = 'n/a';
            $scope.newBin.sanitization_seal_id = 'n/a';

            $scope.GetFacilityByProducts();

            // Load required data for the form
            $scope.GetBinPackageTypes();  // Load package types for the facility
            $scope.GetAWSCustomers();     // Load customers
            // Reference types are loaded globally

            $scope.CallServerFunction(0);

            // Focus on first field after a short delay
            setTimeout(function () {                
                var binNameField = $window.document.getElementById('SerialNumber');
                if (binNameField) {
                    binNameField.focus();
                }
            }, 500);
        };

        // Validation functions for bin editing with serials
        $scope.isDispositionDisabled = function() {
            return $scope.newBin && $scope.newBin.AssetsCount && parseInt($scope.newBin.AssetsCount) > 0;
        };

        $scope.isCustomerLockDisabled = function() {
            return $scope.newBin && $scope.newBin.AssetsCount && parseInt($scope.newBin.AssetsCount) > 0;
        };

        $scope.isCustomerDisabled = function() {
            return $scope.newBin && $scope.newBin.AssetsCount && parseInt($scope.newBin.AssetsCount) > 0;
        };

        $scope.getMaxLimitMin = function() {
            if ($scope.newBin && $scope.newBin.AssetsCount && parseInt($scope.newBin.AssetsCount) > 0) {
                return parseInt($scope.newBin.AssetsCount);
            }
            return 1;
        };

        $scope.validateMaxLimit = function() {
            if ($scope.newBin && $scope.newBin.AssetsCount && parseInt($scope.newBin.AssetsCount) > 0 && $scope.newBin.MaxLimitRequired == '1') {
                var maxAssets = parseInt($scope.newBin.MaximumAssets) || 0;
                var currentAssets = parseInt($scope.newBin.AssetsCount);
                if (maxAssets < currentAssets) {
                    $scope.newBin.MaximumAssets = currentAssets;
                }
            }
        };

        // Check if the current bin is a child bin (has a parent)
        $scope.isChildBin = function() {
            return $scope.newBin && $scope.newBin.ParentBinName && $scope.newBin.ParentBinName.trim() !== '';
        };



        // Check if Location Group should be disabled (for child bins)
        $scope.isLocationGroupDisabled = function() {
            return $scope.isChildBin();
        };



        $scope.EditContainer = function (container) {
            $scope.addingContainer = true;
            $scope.newBin = container;
            $scope.newBin.ShippingControllerLoginID = '';
            $scope.newBin.location = container.LocationName;

            $scope.newBin.Notes = 'n/a';
            $scope.newBin.SanitizationVerificationID = 'n/a';
            $scope.newBin.InventoryNotes = 'n/a';
            $scope.newBin.NotesServer = 'n/a';

            $scope.newBin.ServerSanitizationVerificationID = 'n/a';
            $scope.newBin.sanitization_seal_id = 'n/a';

            $scope.GetFacilityByProducts();

            $scope.ContainerSerials = [];
            $scope.CallServerFunction(0);
        };

        $scope.ScannedServerCount = function () {
            if($scope.ContainerSerials.length > 0) {
                return $scope.Scanned_Servers;
                // var count = 0;
                // for(var i=0;i<$scope.ContainerSerials.length;i++) {
                //     if($scope.ContainerSerials[i].ServerSerialNumber != '' && $scope.ContainerSerials[i].part_type == 'Server') {
                //         count = count + 1;
                //     }
                // }
                // return count;
            } else {
                return 0;
            }
        };

        // $scope.canServerCountbeShown = function () {
        //     if(!$scope.shipping.disposition_id || $scope.shipping.disposition_id == '') {
        //         return false;
        //     } else {
        //         if($scope.Dispositions.length > 0) {
        //             var matching_disposition = false;
        //             for(var i=0;i<$scope.Dispositions.length;i++) {
        //                 if(($scope.Dispositions[i].disposition_id == $scope.shipping.disposition_id) && $scope.Dispositions[i].disposition == 'Terminal-ServerRecycleProcessed') {
        //                     matching_disposition = true;
        //                     break;
        //                 }
        //             }
        //             return matching_disposition;
        //         } else {
        //             return false;
        //         }                
        //     }
        // };

        $scope.canServerCountbeShown = function () {
            if(!$scope.newBin.disposition_id || $scope.newBin.disposition_id == '') {
                return false;
            } else {
                if($scope.Dispositions.length > 0) {
                    var matching_disposition = false;
                    for(var i=0;i<$scope.Dispositions.length;i++) {
                        if(($scope.Dispositions[i].disposition_id == $scope.newBin.disposition_id) && $scope.Dispositions[i].disposition == 'Terminal-ServerRecycleProcessed') {
                            matching_disposition = true;
                            break;
                        }
                    }
                    return matching_disposition;
                } else {
                    return false;
                }                
            }
        };
        

        $scope.ClearContainer = function () {
            // $scope.newBin = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','ServerSanitizationVerificationID': 'n/a','sanitization_seal_id' : 'n/a' };
            // $scope.addingContainer = false;

            $scope.ManageBin();
        };

        $scope.AutoNavigateInventorySave = function () {
            $window.document.getElementById('scan_for_save').focus();
        }

        $scope.AutoNavigateSubInventorySave = function () {
            $window.document.getElementById('scan_for_subcomponentsave').focus();
        }

        $scope.AddSerialToContainer = function () {
            //if($scope.newBin.PasswordVerified) {
            if(true) {

                $scope.newBin.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=AddSerialToContainer&SerialNumber=' + $scope.newBin.SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newBin.Notes + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newBin.SanitizationVerificationID + '&AssetScanID=' + $scope.newBin.AssetScanID + '&UniversalModelNumber=' + $scope.newBin.UniversalModelNumber+'&ControllerLoginID='+$scope.newBin.ShippingControllerLoginID,
                    data: 'ajax=AddSerialToContainer&SerialNumber=' + $scope.newBin.SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newBin.Notes + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newBin.SanitizationVerificationID + '&sanitization_seal_id='+ $scope.newBin.sanitization_seal_id+'&AssetScanID=' + $scope.newBin.AssetScanID + '&ControllerLoginID='+$scope.newBin.ShippingControllerLoginID+'&'+$.param({UniversalModelNumber:$scope.newBin.UniversalModelNumber})+'&serial_scan_time='+$scope.newBin.serial_scan_time+'&mpn_scan_time='+$scope.newBin.mpn_scan_time+'&COOID='+$scope.newBin.COOID+'&COO='+$scope.newBin.COO+'&CustomPalletID=' + $scope.newBin.CustomPalletID,
                    success: function (data) {
                        $scope.newBin.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newBin.SerialNumber = '';
                            $scope.newBin.AssetScanID = '';
                            $scope.newBin.UniversalModelNumber = '';
                            $scope.newBin.COOID = '';
                            $scope.newBin.COO = '';
                            $scope.newBin.Notes = 'n/a';
                            $scope.newBin.SanitizationVerificationID = 'n/a';
                            $scope.newBin.sanitization_seal_id = 'n/a';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('SerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        $scope.ValidateSerialNumber = function (SerialNumber) {
            $scope.newBin.busy = true;
            $rootScope.$broadcast('preloader:active');
            $scope.newBin.sanitization_seal_id = 'n/a';
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateSerialNumber&SerialNumber=' + SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID+'&CustomPalletID=' + $scope.newBin.CustomPalletID,
                success: function (data) {
                    $scope.newBin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        // $mdToast.show (
                        //     $mdToast.simple()
                        //         .content(data.Result)
                        //         .action('OK')
                        //         .position('right')
                        //         .hideDelay(0)
                        //         .toastClass('md-toast-success md-block')
                        // );
                        if (data.AssetScanID) {
                            $scope.newBin.AssetScanID = data.AssetScanID;
                        }
                        if (data.UniversalModelNumber) {
                            $scope.newBin.UniversalModelNumber = data.UniversalModelNumber;
                            //$window.document.getElementById('scan_for_save').focus();
                            $scope.GetCurrentTime($scope.newBin,'mpn_scan_time');
                        }
                        if(data.COOID) {
                            $scope.newBin.COOID = data.COOID;
                            $scope.GetCurrentTime($scope.newBin,'coo_scan_time');
                        }
                        if(data.COO) {
                            $scope.newBin.COO = data.COO;                            
                        }
                        if(data.sanitization_verification_id) {                            
                            //$scope.newBin.SanitizationVerificationID = data.sanitization_verification_id;
                            $window.document.getElementById('sanitization_seal_id').select();
                        } else {
                            $window.document.getElementById('scan_for_save').focus();
                        }                    
                    } else {
                        $scope.newBin.AssetScanID = '';
                        $scope.newBin.UniversalModelNumber = '';
                        $scope.newBin.COOID = '';
                        $scope.newBin.COO = '';
                        $scope.newBin.SanitizationVerificationID = 'n/a';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.ValidateInventorySerialNumber = function (SerialNumber) {
            $scope.newBin.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateInventorySerialNumber&SerialNumber=' + SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID+ '&ShippingContainerID=' + $scope.newBin.ShippingContainerID,
                success: function (data) {
                    $scope.newBin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        if (data.UniversalModelNumber) {
                            $scope.newBin.InventoryUniversalModelNumber = data.UniversalModelNumber;
                            $window.document.getElementById('scan_for_subcomponentsave').focus();
                            $scope.GetCurrentTime($scope.newBin,'inventory_mpn_scan_time');
                        }
                    } else {
                        $scope.newBin.InventoryUniversalModelNumber = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.ValidateServerSerialNumber = function (SerialNumber) {
            $scope.newBin.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateServerSerialNumberForContainer&SerialNumber=' + SerialNumber + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID +'&ShippingID=' + $scope.shipping.ShippingID+'&ByPassContainerMatching='+$scope.newBin.ByPassContainerMatching+'&CustomPalletID='+$scope.newBin.CustomPalletID,
                success: function (data) {
                    $scope.newBin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {                        
                        if (data.ServerID) {
                            $scope.newBin.ServerID = data.ServerID;
                            $scope.newBin.Type = data.Type;

                            if(data.Type == 'Server' || data.Type == 'SERVER' || data.Type == 'server') {
                                setTimeout(function () {
                                    //$window.document.getElementById('ServerSanitizationVerificationID').focus();
                                    $window.document.getElementById('ServerSanitizationVerificationID').select();
                                }, 100); 
                            } else {
                                $window.document.getElementById('scan_for_save_server').focus();
                            }
                            //$window.document.getElementById('scan_for_save_server').focus();
                        }
                        if (data.MPN) {
                            $scope.newBin.MPN = data.MPN;
                            //$window.document.getElementById('scan_for_save_server').focus();
                            $scope.GetCurrentTime($scope.newBin,'server_mpn_scan_time')
                        }
                        if(data.MediaRecovery_VerificationID) {
                            $scope.newBin.ServerSanitizationVerificationID = data.MediaRecovery_VerificationID;
                        } else {
                            $scope.newBin.ServerSanitizationVerificationID = 'n/a';
                        }                        
                    } else {
                        $scope.newBin.ServerID = '';
                        $scope.newBin.MPN = '';
                        $scope.newBin.Type = '';
                        $scope.newBin.ServerSanitizationVerificationID = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                        
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.AddServerSerialToContainer = function () {
            //if($scope.newBin.PasswordVerified) {
            if(true) {
                $scope.newBin.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=AddServerSerialToContainer&SerialNumber=' + $scope.newBin.SerialNumberServer + '&Notes=' + $scope.newBin.NotesServer + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newBin.SanitizationVerificationID + '&ServerID=' + $scope.newBin.ServerID + '&UniversalModelNumber=' + $scope.newBin.MPN+'&ControllerLoginID='+$scope.newBin.ShippingControllerLoginID+'&Type='+$scope.newBin.Type+'&ShippingID=' + $scope.shipping.ShippingID+'&ServerSanitizationVerificationID='+$scope.newBin.ServerSanitizationVerificationID+'&ByPassContainerMatching='+$scope.newBin.ByPassContainerMatching+'&serial_scan_time='+$scope.newBin.server_serial_scan_time+'&mpn_scan_time='+$scope.newBin.server_mpn_scan_time+'&CustomPalletID='+$scope.newBin.CustomPalletID,
                    success: function (data) {
                        $scope.newBin.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );            
                            $scope.newBin.SerialNumberServer = '';
                            $scope.newBin.ServerID = '';
                            $scope.newBin.MPN = '';
                            $scope.newBin.Type = '';
                            $scope.newBin.NotesServer = 'n/a';
                            $scope.newBin.ServerSanitizationVerificationID = 'n/a';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('SerialNumberServer').focus();     
                            $scope.newBin.ByPassContainerMatching = '0';                   
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.newBin.ByPassContainerMatching = '0';
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }

        };


        $scope.AddByProductToContainer = function () {
            //if($scope.newBin.PasswordVerified) {
            if(true) {
                $scope.newBin.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=AddByProductToShipmentContainer&byproduct_id=' + $scope.newBin.byproduct_id + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&ControllerLoginID='+$scope.newBin.ShippingControllerLoginID+'&ShippingID=' + $scope.shipping.ShippingID+'&CustomPalletID='+$scope.newBin.CustomPalletID,
                    success: function (data) {
                        $scope.newBin.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );            
                            $scope.newBin.byproduct_id = '';                        
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if ($scope.newBin.CustomPalletID > 0) {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetContainerSerials&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text))+'&CustomPalletID='+$scope.newBin.CustomPalletID,
                    success: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.ContainerSerials = data.Result;
                            if (data.total) {
                                $scope.total = data.total;
                            }
                            if (data.Scanned_Servers) {
                                $scope.Scanned_Servers = data.Scanned_Servers;
                            }
                        } else {
                            $scope.ContainerSerials = [];
                            if (data.Scanned_Servers) {
                                $scope.Scanned_Servers = data.Scanned_Servers;
                            }
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-info md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.ContainerSerials = [];
            }
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetContainerSerials&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text))+'&CustomPalletID='+$scope.newBin.CustomPalletID,
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.ContainerSerials = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                        if (data.Scanned_Servers) {
                            $scope.Scanned_Servers = data.Scanned_Servers;
                        }
                    } else {
                        if (data.Scanned_Servers) {
                            $scope.Scanned_Servers = data.Scanned_Servers;
                        }
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic


        $scope.DeleteSerialFromContainer = function (serial, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteSerialFromContainer&' + $.param(serial) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.ContainerSerials = [];
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };


        $scope.DeleteByProductFromContainer = function (serial, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteByProductFromContainer&' + $.param(serial) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.ContainerSerials = [];
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };




        $scope.DeleteBinFromShipment = function (bin, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to move Serials to BIN and Delete Bin ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteBinFromShipment&' + $.param(bin) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newBin = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','sanitization_seal_id' : 'n/a' };
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };

        $scope.DeleteByProductBinFromShipment = function (bin, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to remove byproduct items from this bin?')
                .content('This will remove only byproduct items from the bin, not the bin itself.')
                .ariaLabel('Remove byproducts')
                .targetEvent(ev)
                .ok('Remove')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteByProductBinFromShipment&' + $.param(bin) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .textContent(data.Result)
                                    .position('top right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.GetShipmentBins();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .textContent(data.Result)
                                    .position('top right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };

        $scope.DeleteByProductContainerFromShipment = function (container, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete Container ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteByProductContainerFromShipment&' + $.param(container) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newBin = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a' ,'sanitization_seal_id' : 'n/a'};
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };


        function CloseContainerTPVRController($scope,$mdDialog,CurrentContainer,$mdToast,$window) {
            $scope.CurrentContainer = CurrentContainer;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };   
            
            $scope.FocusNextField = function (nextid,wait) {            
                if(wait == '1') {
                    setTimeout(function () {
                        $window.document.getElementById(nextid).focus();
                    }, 100);
                } else {
                    $window.document.getElementById(nextid).focus();
                }
            };
            
        }

        $scope.CurrentContainer = {};
        $scope.confirmDetails = {};
        function afterShowAnimation1 () {            
            $window.document.getElementById("AuditController").focus();            
        }

        $scope.CloseContainer = function (container, ev) {

            $mdDialog.show({
                controller: CloseContainerTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation1,
                clickOutsideToClose:true,
                resolve: {
                    CurrentContainer: function () {
                    return container;
                    }
                }
            })
            .then(function(confirmDetails) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CloseContainer&' + $.param(container) + '&ShippingID=' + $scope.shipping.ShippingID+'&'+$.param(confirmDetails),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );                            
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
                
                
            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });

        };


        $scope.ReopenContainer = function (container, ev) {
            
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Reopen the container?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Reopen')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $scope.CurrentShipment = {};
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ReopenContainer&ShippingID=' + $scope.shipping.ShippingID+'&'+$.param(container),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.SearchContainer = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });

        };



        $scope.AddInventorySerialToContainer = function () {
            //if($scope.newBin.PasswordVerified) {
            if(true) {

                $scope.newBin.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=AddInventorySerialToContainer&SerialNumber=' + $scope.newBin.InventorySerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newBin.InventoryNotes + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&UniversalModelNumber=' + $scope.newBin.InventoryUniversalModelNumber+'&ControllerLoginID='+$scope.newBin.ShippingControllerLoginID,
                    data: 'ajax=AddInventorySerialToContainer&SerialNumber=' + $scope.newBin.InventorySerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newBin.InventoryNotes + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&'+$.param({UniversalModelNumber : $scope.newBin.InventoryUniversalModelNumber})+'&ControllerLoginID='+$scope.newBin.ShippingControllerLoginID+'&serial_scan_time='+$scope.newBin.inventory_serial_scan_time+'&mpn_scan_time='+$scope.newBin.inventory_mpn_scan_time,
                    success: function (data) {
                        $scope.newBin.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newBin.InventorySerialNumber = '';
                            $scope.newBin.InventoryUniversalModelNumber = '';
                            $scope.newBin.InventoryNotes = 'n/a';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('InventorySerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        function SanitizationTPVRController($scope, $mdDialog, $mdToast) {
            $scope.hide = function () {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function () {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        function afterShowAnimation () {            
            $window.document.getElementById("password").focus();
        }
        $scope.confirmDetails = {};
        $scope.ValidateSanitizationControllerPopup = function (ev) {
            $scope.newBin.PasswordVerified = false;
            $mdDialog.show({
                controller: SanitizationTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose: true
            })
                .then(function (confirmDetails) {
                    $rootScope.$broadcast('preloader:active');
                    $scope.confirmDetails = confirmDetails;
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateRemovalController&UserName=' + $scope.newBin.ShippingControllerLoginID + '&Password=' + $scope.confirmDetails.Password,
                        success: function (data) {
                            if (data.Success) {
                                $scope.newBin.PasswordVerified = true;
                                if($scope.newBin.CreatedBy > 0) {//Updating existing
                                    setTimeout(function () {
                                        $window.document.getElementById('SerialNumber').focus();
                                    }, 100);
                                } else {
                                    setTimeout(function () {
                                        $window.document.getElementById('save_button').focus();
                                    }, 100);
                                }                                
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                $scope.newBin.PasswordVerified = false;
                            }
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $scope.newBin.PasswordVerified = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }, function (confirmDetails) {
                    $scope.confirmDetails = confirmDetails;
                });
        };


        function LocationChange(text) {
            $scope.newBin.location = text;
        }

        function selectedLocationChange(item) {
            if (item) {
                if (item.value) {
                    $scope.newBin.location = item.value;
                } else {
                    $scope.newBin.location = '';
                }
            } else {
                $scope.newBin.location = '';
            }
        }

        $scope.queryLocationSearch = queryLocationSearch;
        $scope.LocationChange = LocationChange;
        $scope.selectedLocationChange = selectedLocationChange;
        function queryLocationSearch(query) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + $scope.shipping.FacilityID+'&LocationType=Outbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }

        // Parent Bin Search Functions
        function ParentBinChange(text) {
            $scope.newBin.ParentBinName = text;
            // Clear location group when parent bin is selected
            if (text && text.trim() !== '') {
                $scope.newBin.group = '';
                $scope.newBin.selectedLocationItem = null;
            }
        }

        function selectedParentBinChange(item) {
            if (item) {
                $scope.newBin.ParentBinName = item.BinName;
                $scope.newBin.ParentCustomPalletID = item.CustomPalletID;
                $scope.newBin.ParentDisposition = item.disposition;
                // Clear location group when parent bin is selected
                $scope.newBin.group = '';
                $scope.newBin.selectedLocationItem = null;

                // Validate parent bin disposition compatibility
                if ($scope.newBin.disposition_id && $scope.newBin.disposition_id !== item.disposition_id) {
                    // $mdToast.show(
                    //     $mdToast.simple()
                    //         .content('Warning: Parent bin disposition (' + item.disposition + ') differs from selected disposition. They must match.')
                    //         .action('OK')
                    //         .position('right')
                    //         .hideDelay(5000)
                    //         .toastClass('md-toast-warning md-block')
                    // );
                }
            } else {
                $scope.newBin.ParentBinName = '';
                $scope.newBin.ParentCustomPalletID = '';
                $scope.newBin.ParentDisposition = '';
            }
        }

        $scope.queryParentBinSearch = queryParentBinSearch;
        $scope.ParentBinChange = ParentBinChange;
        $scope.selectedParentBinChange = selectedParentBinChange;
        function queryParentBinSearch(query) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'shipping/includes/shipping_submit.php?ajax=GetShipmentBinsForParent&keyword=' + encodeURIComponent(query) + '&ShippingID=' + $scope.shipping.ShippingID + '&FacilityID=' + $scope.shipping.FacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    return res.data.Result;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }

        function LocationChange1(text) {
            $scope.newBin.group = text;
            // Clear parent bin when location group is selected
            if (text && text.trim() !== '') {
                $scope.newBin.ParentBinName = '';
                $scope.newBin.selectedParentBinItem = null;
            }
        }

        function selectedLocationChange1(item) {
            if (item) {
                if (item.value) {
                    $scope.newBin.group = item.value;
                    // Clear parent bin when location group is selected
                    $scope.newBin.ParentBinName = '';
                    $scope.newBin.selectedParentBinItem = null;
                    // Call GetLocationGroups when location group is selected
                    //$scope.GetLocationGroups();
                } else {
                    $scope.newBin.group = '';
                }
            } else {
                $scope.newBin.group = '';
            }
        }

        $scope.queryLocationSearch1 = queryLocationSearch1;
        $scope.LocationChange1 = LocationChange1;
        $scope.selectedLocationChange1 = selectedLocationChange1;
        function queryLocationSearch1(query) {
            if (query) {
                if (query != '' && query != 'undefined') {                    
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + $scope.shipping.FacilityID+'&LocationType=WIPOROutbound')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }


        $scope.AddBinToShipment = function (ev) {
            if($scope.SearchBin != '') {
                
                var confirm = $mdDialog.confirm()
                    .title('Are you sure, You want to add Bin to Shipment?')
                    .content('')
                    .ariaLabel('Lucky day')
                    .targetEvent(ev)
                    .ok('ADD')
                    .cancel('Cancel');
                $mdDialog.show(confirm).then(function () {
                    $scope.CurrentShipment = {};
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=AddBinToShipment&ShippingID=' + $scope.shipping.ShippingID+'&BinName='+$scope.SearchBin,
                        success: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            if (data.Success) {
                                $scope.SearchBin = '';
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                // Refresh the bins list to show the newly added bin
                                location.reload();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }, function () {

                });

            }
        };


        $scope.FocusNextField = function (nextid,wait) {            
            if(wait == '1') {
                setTimeout(function () {
                    $window.document.getElementById(nextid).focus();
                }, 100);
            } else {
                $window.document.getElementById(nextid).focus();
            }
        };

        $scope.GetExactMPN = function (MPN) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTrimmedMPN&MPN=' + MPN,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.newBin.UniversalModelNumber = data.ExactMPN;    
                        $scope.AutoNavigateInventorySave();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };

        $scope.GetExactMPNSN = function (MPN) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTrimmedMPN&MPN=' + MPN,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.newBin.InventoryUniversalModelNumber = data.ExactMPN;    
                        $scope.AutoNavigateSubInventorySave();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };



        $scope.AddMediaToContainer = function () {
            //if($scope.newBin.PasswordVerified) {
            if(true) {
                if($scope.newBin.MediaID > 0) {                    
                    $scope.newBin.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',                    
                        data: 'ajax=AddMediaToContainer&MediaSerialNumber=' + $scope.newBin.MediaSerialNumber + '&ShippingContainerID=' + $scope.newBin.ShippingContainerID + '&'+$.param({UniversalModelNumber : $scope.newBin.ReuseMediaUniversalModelNumber})+'&ControllerLoginID='+$scope.newBin.ShippingControllerLoginID+'&ShippingID='+$scope.shipping.ShippingID+'&serial_scan_time='+$scope.newBin.media_serial_scan_time+'&mpn_scan_time='+$scope.newBin.media_mpn_scan_time,
                        success: function (data) {
                            $scope.newBin.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            if (data.Success) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                $scope.newBin.MediaSerialNumber = '';
                                $scope.newBin.ReuseMediaUniversalModelNumber = '';
                                $scope.newBin.ReuseMedia_part_type = '';
                                $scope.CallServerFunction(0);
                                $window.document.getElementById('MediaSerialNumber').focus();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }
                    });
                    
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Invalid Media')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }              
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }

        };

        $scope.ValidateMediaSerialNumber = function (MediaSerialNumber) {

            $scope.newBin.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateMediaSerialNumber&SerialNumber=' + MediaSerialNumber + '&ShippingID=' + $scope.shipping.ShippingID+'&ShippingContainerID=' + $scope.newBin.ShippingContainerID+'&CustomPalletID='+$scope.newBin.CustomPalletID,
                success: function (data) {
                    $scope.newBin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {                        
                        if (data.MediaID) {
                            $scope.newBin.MediaID = data.MediaID;
                        }
                        if (data.UniversalModelNumber) {
                            $scope.newBin.ReuseMediaUniversalModelNumber = data.UniversalModelNumber;
                            $window.document.getElementById('ReuseMediaUniversalModelNumber').focus();
                            $scope.GetCurrentTime($scope.newBin,'media_mpn_scan_time');
                        }
                        if(data.MediaType) {
                            $scope.newBin.ReuseMedia_part_type = data.MediaType
                        }
                    } else {
                        $scope.newBin.MediaID = '';
                        $scope.newBin.ReuseMediaUniversalModelNumber = '';
                        $scope.newBin.ReuseMedia_part_type = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });


        };



        $scope.Unlink = function (container, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Unlink Container from Shipment?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Unlink')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=UnlinkContainerFromShipment&' + $.param(container),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newBin = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','sanitization_seal_id' : 'n/a' };
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };

        $scope.ValidateSanitizationSealID = function (sanitization_seal_id,SerialNumber,AssetScanID) {
            
            $scope.newBin.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateSanitizationSealID&SerialNumber=' + SerialNumber + '&AssetScanID=' + AssetScanID +'&sanitization_seal_id='+sanitization_seal_id,
                success: function (data) {
                    $scope.newBin.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5)
                                .toastClass('md-toast-success md-block')
                        );   
                        if(data.sanitization_verification_id) {                            
                            $scope.newBin.SanitizationVerificationID = data.sanitization_verification_id;                            
                        } 
                        $scope.AutoNavigateInventorySave();                     
                    } else {                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
            
        };

        $scope.DestinationChanged = function () {
            $scope.shipping.DestinationFacilityID = '';
        };

        $scope.DestinationFacilityChanged = function () {
            $scope.shipping.VendorID = '';
            $scope.shipping.DestinationPOC = '';
        };

        // Close Bin Controller for TPVR modal
        function CloseBinControllerShipping($scope, $mdDialog, CurrentBin, $window) {
            $scope.CurrentBin = CurrentBin;
            $scope.confirmDetails = {};

            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel();
            };

            $scope.FocusNextField = function (nextid, wait) {
                if(wait == '1') {
                    setTimeout(function () {
                        $window.document.getElementById(nextid).focus();
                    }, 100);
                } else {
                    $window.document.getElementById(nextid).focus();
                }
            };

            // Focus on first field when modal opens
            setTimeout(function() {
                $window.document.getElementById("AuditController").focus();
            }, 100);
        }

        // Close Bin Function
        $scope.CloseBin = function(bin, $event) {
            $event.preventDefault();
            $event.stopPropagation();



            $mdDialog.show({
                controller: CloseBinControllerShipping,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: $event,
                clickOutsideToClose: true,
                resolve: {
                    CurrentBin: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails) {
                // Process close bin with TPVR details
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: {
                        ajax: 'CloseBin',
                        CustomPalletID: bin.CustomPalletID,
                        ShippingID: $scope.shipping.ShippingID,
                        AuditController: confirmDetails.AuditController,
                        Password: confirmDetails.Password,
                        NewSealID: confirmDetails.NewSealID,
                        BinWeight: confirmDetails.BinWeight
                    },
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        console.log('CloseBin Response:', data);

                        // Handle both string and object responses
                        if (typeof data === 'string') {
                            try {
                                data = JSON.parse(data);
                            } catch (e) {
                                console.error('Failed to parse response:', data);
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Invalid response format')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                return;
                            }
                        }

                        if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            // Refresh the bins list - use page reload for reliability
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                    },
                    error: function(xhr, status, error) {
                        $rootScope.$broadcast('preloader:hide');
                        console.log('CloseBin AJAX Error:', xhr, status, error);
                        var errorMessage = 'Error occurred while closing bin';
                        if (xhr.responseText) {
                            try {
                                var response = JSON.parse(xhr.responseText);
                                if (response.Result) {
                                    errorMessage = response.Result;
                                }
                            } catch (e) {
                                errorMessage += ': ' + xhr.responseText;
                            }
                        }
                        $mdToast.show(
                            $mdToast.simple()
                                .content(errorMessage)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                });
            });
        };

        // Reopen Bin Function
        $scope.ReopenBin = function(bin, $event) {
            $event.preventDefault();
            $event.stopPropagation();

            var confirm = $mdDialog.confirm()
                .title('Are you sure you want to reopen this bin?')
                .content('Bin: ' + bin.BinName + ' will be reopened and made available for use.')
                .ariaLabel('Reopen Bin')
                .targetEvent($event)
                .ok('Reopen')
                .cancel('Cancel');

            $mdDialog.show(confirm).then(function() {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: {
                        ajax: 'ReopenBin',
                        CustomPalletID: bin.CustomPalletID,
                        ShippingID: $scope.shipping.ShippingID
                    },
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        console.log('ReopenBin Response:', data);

                        // Handle both string and object responses
                        if (typeof data === 'string') {
                            try {
                                data = JSON.parse(data);
                            } catch (e) {
                                console.error('Failed to parse response:', data);
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Invalid response format')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                return;
                            }
                        }

                        if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            // Refresh the bins list - use page reload for reliability
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                    },
                    error: function() {
                        $rootScope.$broadcast('preloader:hide');
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Error occurred while reopening bin')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                });
            });
        };

        // Unlink Bin Function
        $scope.UnlinkBin = function(bin, $event) {
            $event.preventDefault();
            $event.stopPropagation();

            // Validate that bin is not inside a parent
            if (bin.ParentBinName && bin.ParentBinName.trim() !== '') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Child bins cannot be unlinked independently. Please unlink the parent bin instead.')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            var confirm = $mdDialog.confirm()
                .title('Are you sure you want to unlink this bin from the shipment?')
                .content('Bin: ' + bin.BinName + ' will be removed from this shipment.')
                .ariaLabel('Unlink Bin')
                .targetEvent($event)
                .ok('Unlink')
                .cancel('Cancel');

            $mdDialog.show(confirm).then(function() {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: {
                        ajax: 'UnlinkBin',
                        CustomPalletID: bin.CustomPalletID,
                        ShippingID: $scope.shipping.ShippingID
                    },
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        console.log('UnlinkBin Response:', data);

                        // Handle both string and object responses
                        if (typeof data === 'string') {
                            try {
                                data = JSON.parse(data);
                            } catch (e) {
                                console.error('Failed to parse response:', data);
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content('Invalid response format')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                return;
                            }
                        }

                        if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            // Refresh the bins list - use page reload for reliability
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                    },
                    error: function(xhr, status, error) {
                        $rootScope.$broadcast('preloader:hide');
                        console.log('UnlinkBin AJAX Error:', xhr, status, error);
                        var errorMessage = 'Error occurred while unlinking bin';
                        if (xhr.responseText) {
                            try {
                                var response = JSON.parse(xhr.responseText);
                                if (response.Result) {
                                    errorMessage = response.Result;
                                }
                            } catch (e) {
                                errorMessage += ': ' + xhr.responseText;
                            }
                        }
                        $mdToast.show(
                            $mdToast.simple()
                                .content(errorMessage)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                });
            });
        };




    });
})();
