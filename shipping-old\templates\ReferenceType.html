<div class="row page" data-ng-controller="reference_type">
    <div class="col-md-12">
        <article class="article">            
            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Reference Type Configuration</span>
                        <div flex></div>                              
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">                        
                        <form name="material_signup_form" class="form-validation" >                            
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Facility</label>
                                    <md-select name="FacilityIDList" ng-model="referencetype.FacilityIDList" required multiple ng-change="FacilityDropdownChanged()">
                                        <md-option value="ALL"> ALL </md-option>
                                        <md-option ng-repeat="facility in Facilities | filter:{ FacilityStatus : '!InActive'}" value="{{facility.FacilityID}}" > {{facility.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.FacilityIDList.$error" multiple ng-if='material_signup_form.FacilityIDList.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Reference Type Name</label>
                                    <input type="text" name="ReferenceType" ng-model="referencetype['ReferenceType']"  required ng-maxlength="100" ng-minlength="3">
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ReferenceType.$error" multiple ng-if='material_signup_form.ReferenceType.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>                                                                                    
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Description</label>
                                    <input type="text" name="ReferenceTypeDescription" ng-model="referencetype['ReferenceTypeDescription']"  required ng-maxlength="1000" ng-minlength="3">
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.ReferenceTypeDescription.$error" multiple ng-if='material_signup_form.ReferenceTypeDescription.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 1000.</div>                                                                                    
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="referencetype.Status" required>
                                        <md-option value="Active"> Active </md-option>
                                        <md-option value="Inactive"> Inactive </md-option>
                                    </md-select>
                                    <div class="error-sapce">
                                        <div ng-messages="material_signup_form.Status.$error" multiple ng-if='material_signup_form.Status.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block mb-20">
                                    <md-checkbox ng-model="referencetype.ReferenceIDRequired" ng-true-value="1" ng-false-value="0" style="margin-bottom: 6px;" ng-click="ReferenceIDClicked()">Reference ID Required</md-checkbox>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>


                            <div class="col-md-3">
                                <md-input-container class="md-block" >
                                    <label>Reference ID</label>
                                    <input type="text" name="ReferenceID" ng-model="referencetype['ReferenceID']" ng-minlength="3"  ng-maxlength="500" ng-disabled="referencetype.ReferenceIDRequired == '0' || !referencetype.ReferenceIDRequired">
                                    <div class="error-sapce">                                        
                                        <div ng-messages="material_signup_form.ReferenceID.$error" multiple ng-if='material_signup_form.ReferenceID.$dirty'>                                                                        
                                            <div ng-message="minlength">Min length 3.</div>                                            
                                            <div ng-message="maxlength">Max length 500.</div>                           
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            
                            <div class="col-md-6">
                                <md-input-container class="md-block">
                                    <label>Removal Type</label>
                                    <md-select name="disposition_id_list" ng-model="referencetype.disposition_id_list" required aria-label="select" multiple ng-change="DispositionDropdownChanged()">
                                        <md-option value="ALL" > ALL </md-option>
                                        <md-option ng-repeat="disposition in Dispositions" value="{{disposition.disposition_id}}" > {{disposition.disposition}} </md-option>
                                    </md-select>
                                    <div class="error-sapce"></div>
                                </md-input-container>
                            </div>                                                    
                            <div class="col-md-12 btns-row">
                                <md-button class="md-button md-raised btn-w-md md-default"
                                ng-click="CancelConfiguration()">
                                        Cancel
                                    </md-button>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid" ng-click="ManageReferenceType()">
                                <span ng-show="! referencetype.busy">Save</span>
                                <span ng-show="referencetype.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>
  
            <md-card class="no-margin-h pt-0">
                <md-toolbar class="md-table-toolbar md-default" ng-init="sortconfigurationList = true;">
                    <div class="md-toolbar-tools" style="cursor: pointer;">

                        <i ng-click="sortconfigurationList = !sortconfigurationList" class="material-icons md-primary" ng-show="sortconfigurationList">keyboard_arrow_up</i>
                        <i ng-click="sortconfigurationList = !sortconfigurationList" class="material-icons md-primary" ng-show="! sortconfigurationList">keyboard_arrow_down</i>
                        <span ng-click="sortconfigurationList = !sortconfigurationList">Reference Types List</span>
                        <div flex></div>   
                        <a href="#!/ReferenceType" ng-click="ReferenceTypeListxls()" class="md-button md-raised btn-w-md md-default pull-right" style="display: flex; margin-top: -10px;">
                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                        </a>                             
                    </div>
                </md-toolbar>
                <div class="row"  ng-show="sortconfigurationList">
                    <div class="col-md-12 tabs_section" style="margin-top: 10px;">
                        <div>                            
                            <md-content class="md-padding">
                                <!--<h5><strong>Active list</strong></h5>-->
                                <p>
                                <!--Active List Start-->
                                    <div class="row">
                                        <div class="col-md-12">

                                            <div ng-show="pagedItems1" class="pull-right mr-5">
                                                    <small>
                                                    Showing Results <span style="font-weight:bold;">{{(currentPage1 * itemsPerPage1) + 1}}</span>
                                                    to <span style="font-weight:bold;" ng-show="total1 >= (currentPage1 * itemsPerPage1) + itemsPerPage1">{{(currentPage1 * itemsPerPage1) + itemsPerPage1}}</span>
                                                        <span style="font-weight:bold;" ng-show="total1 < (currentPage1 * itemsPerPage1) + itemsPerPage1">{{total1}}</span>
                                                    of <span style="font-weight:bold;">{{total1}}</span>
                                                    </small>
                                            </div>

                                            <div style="clear:both;"></div>
                                            <div class="table-responsive" style="overflow: auto;">
                                                <table class="table table-striped mb-0">
                                                    <thead>

                                                        <tr class="th_sorting">
                                                            <th style="min-width: 40px;">Edit</th>
                                                            <th style="cursor:pointer;" ng-click="MakeOrderBy1('FacilityNameList')" ng-class="{'orderby' : OrderBy1 == 'FacilityNameList'}">
                                                                <div style="min-width:140px;">
                                                                    Facility<i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'FacilityNameList'"></i>
                                                                    <span ng-show="OrderBy1 == 'FacilityNameList'">
                                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                    </span>
                                                                </div>
                                                            </th>

                                                            <th style="cursor:pointer;" ng-click="MakeOrderBy1('ReferenceType')" ng-class="{'orderby' : OrderBy1 == 'ReferenceType'}">
                                                                <div style="min-width: 200px;">
                                                                    Reference Type<i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'ReferenceType'"></i>
                                                                    <span ng-show="OrderBy1 == 'ReferenceType'">
                                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                    </span>
                                                                </div>
                                                            </th>

                                                            <th style="cursor:pointer;" ng-click="MakeOrderBy1('ReferenceTypeDescription')" ng-class="{'orderby' : OrderBy1 == 'ReferenceTypeDescription'}">
                                                                <div style="min-width: 200px;">
                                                                    Description <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'ReferenceTypeDescription'"></i>
                                                                    <span ng-show="OrderBy1 == 'ReferenceTypeDescription'">
                                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                    </span>
                                                                </div>
                                                            </th>
                                                            <th style="cursor:pointer;" ng-click="MakeOrderBy1('Status')" ng-class="{'orderby' : OrderBy1 == 'Status'}">
                                                                <div>
                                                                    Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                                    <span ng-show="OrderBy == 'Status'">
                                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                                    </span>
                                                                </div>
                                                            </th>
                                                            <th style="cursor:pointer;" ng-click="MakeOrderBy1('ReferenceIDRequired')" ng-class="{'orderby' : OrderBy1 == 'ReferenceIDRequired'}">
                                                                <div>
                                                                    Reference ID Required <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'ReferenceIDRequired'"></i>
                                                                    <span ng-show="OrderBy1 == 'ReferenceIDRequired'">
                                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                    </span>
                                                                </div>
                                                            </th>
                                                            <th style="cursor:pointer;" ng-click="MakeOrderBy1('ReferenceID')" ng-class="{'orderby' : OrderBy1 == 'ReferenceID'}">
                                                                <div>
                                                                    Reference ID <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'ReferenceID'"></i>
                                                                    <span ng-show="OrderBy1 == 'ReferenceID'">
                                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                    </span>
                                                                </div>
                                                            </th>
                                                            <th style="cursor:pointer;" ng-click="MakeOrderBy1('disposition_list')" ng-class="{'orderby' : OrderBy1 == 'disposition_list'}">
                                                                <div style="min-width: 220px;">
                                                                    Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy1 != 'disposition_list'"></i>
                                                                    <span ng-show="OrderBy1 == 'disposition_list'">
                                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType1 == 'asc'"></i>
                                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType1 == 'desc'"></i>
                                                                    </span>
                                                                </div>
                                                            </th>
                                                        </tr>

                                                        <tr class="errornone">
                                                            <td>&nbsp;</td>
                                                            <td>
                                                                <md-input-container class="md-block mt-0">
                                                                    <input type="text" name="FacilityNameList" ng-model="filter_text1[0].FacilityNameList" ng-change="MakeFilter1()"  aria-label="text" />
                                                                </md-input-container>
                                                            </td>
                                                            <td>
                                                                <md-input-container class="md-block mt-0">
                                                                    <input type="text" name="ReferenceType" ng-model="filter_text1[0].ReferenceType" ng-change="MakeFilter1()" aria-label="text" />
                                                                </md-input-container>
                                                            </td>
                                                            <td>
                                                                <md-input-container class="md-block mt-0">
                                                                    <input type="text" name="ReferenceTypeDescription" ng-model="filter_text1[0].ReferenceTypeDescription" ng-change="MakeFilter1()" aria-label="text" />
                                                                </md-input-container>
                                                            </td>
                                                            <td>
                                                                <md-input-container class="md-block mt-0">
                                                                    <input type="text" name="Status" ng-model="filter_text1[0].Status" ng-change="MakeFilter1()" aria-label="text" />
                                                                </md-input-container>
                                                            </td>

                                                            <td>
                                                                <!-- <md-input-container class="md-block mt-0">
                                                                    <input type="text" name="ReferenceIDRequired" ng-model="filter_text1[0].ReferenceIDRequired" ng-change="MakeFilter1()" aria-label="text" />
                                                                </md-input-container> -->
                                                            </td>
                                                            <td>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="ReferenceID" ng-model="filter_text1[0].ReferenceID" ng-change="MakeFilter1()" aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                        <td>
                                                            <md-input-container class="md-block mt-0">
                                                                <input type="text" name="disposition_list" ng-model="filter_text1[0].disposition_list" ng-change="MakeFilter1()" aria-label="text" />
                                                            </md-input-container>
                                                        </td>
                                                           
                                                        </tr>
                                                    </thead>

                                                    <tbody ng-show="pagedItems1.length > 0">
                                                        <tr ng-repeat="product in pagedItems1">
                                                            <td class="action-icons" style="min-width: 40px;">
                                                                <span ng-click="EditReferenceType(product,$event)"><i class="material-icons text-danger edit">edit</i></span>
                                                            </td>
                                                            <td>
                                                                {{product.FacilityNameList}}
                                                            </td>
                                                            <td>
                                                                {{product.ReferenceType}}
                                                            </td>
                                                            <td>
                                                                {{product.ReferenceTypeDescription}}
                                                            </td>
                                                            <td>
                                                                {{product.Status}}
                                                            </td>
                                                            <td>
                                                                {{product.ReferenceIDRequiredText}}
                                                            </td>
                                                            <td>
                                                                {{product.ReferenceID}}
                                                            </td>
                                                            <td>
                                                                {{product.disposition_list}}
                                                            </td>
                                                        </tr>
                                                    </tbody>

                                                    <tfoot>
                                                        <tr>
                                                            <td colspan="9">
                                                                <div>
                                                                    <ul class="pagination">
                                                                        <li ng-class="prevPageDisabled(1)">
                                                                            <a href ng-click="firstPage(1)"><< First</a>
                                                                        </li>
                                                                        <li ng-class="prevPageDisabled(1)">
                                                                            <a href ng-click="prevPage(1)"><< Prev</a>
                                                                        </li>
                                                                        <li ng-repeat="n in range(1)" ng-class="{active: n == currentPage1}" ng-click="setPage(n,1)" ng-show="n >= 0">
                                                                            <a style="cursor:pointer;">{{n+1}}</a>
                                                                        </li>
                                                                        <li ng-class="nextPageDisabled(1)">
                                                                            <a href ng-click="nextPage(1)">Next >></a>
                                                                        </li>
                                                                        <li ng-class="nextPageDisabled(1)">
                                                                            <a href ng-click="lastPage(1)">Last >></a>
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tfoot>

                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <!--Active List Close-->
                                </p>
                            </md-content>
                        </div>
                    </div>
                </div>
            </md-card>
        </article>
    </div>
</div>
  