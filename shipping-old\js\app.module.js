(function () {
    'use strict';

    var module = angular.module('app', [
        // Core modules
        'app.core'

        // Custom Feature modules
        , 'app.ui'
        , 'app.ui.form'
        , 'app.ui.form.validation'


        // 3rd party feature modules
        , 'md.data.table'
        , 'global'
        , 'angularFileUpload'
        , 'md.data.table'
        ,'angularMoment'
    ]);

    module.config(['$stateProvider', '$urlRouterProvider', '$ocLazyLoadProvider',
        function ($stateProvider, $urlRouterProvider, $ocLazyLoadProvider) {

            $stateProvider
                .state('ShipmentPrep', {
                    url: '/ShipmentPrep',
                    templateUrl: "templates/shipmentprep.html"
                })
                .state('ShipmentPrep/:ShippingID', {
                    url: '/ShipmentPrep/:ShippingID',
                    templateUrl: "templates/shipmentprep.html"
                })
                .state('ShipmentRemoval', {
                    url: '/ShipmentRemoval',
                    templateUrl: "templates/shipmentremoval.html"
                })
                .state('RemovedShipments', {
                    url: '/RemovedShipments',
                    templateUrl: "templates/removedshipments.html"
                })
                .state('ShipmentContainer', {
                    url: '/ShipmentContainer',
                    templateUrl: "templates/shipmentcontainer.html"
                })
                
                .state('ShipmentContainer/:ShippingContainerID', {
                    url: '/ShipmentContainer/:ShippingContainerID',
                    templateUrl: "templates/shipmentcontainer.html"
                })

                .state('PendingOutboundShipments', {
                    url: '/PendingOutboundShipments',
                    templateUrl: "templates/PendingOutboundShipments.html"
                })

                .state('ReferenceType', {
                    url: '/ReferenceType',
                    templateUrl: "templates/ReferenceType.html"
                })

                .state('TransferContainerConversion', {
                    url: '/TransferContainerConversion',
                    templateUrl: "templates/TransferContainerConversion.html"
                })

            $urlRouterProvider
                .when('/', '/ShipmentPrep')
                .otherwise('/ShipmentPrep');
        }
    ]);

    module.controller("shipment_prep", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Shipment Prep',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.COOList = [];
        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.Facilities = [];
        $scope.shipping = {'Containers': []};
        $scope.Dispositions = [];
        $scope.Vendors = [];
        $scope.PackageTypes = [];
        $scope.ContainerSerials = [];
        $scope.ByProducts = [];
        $scope.ReferenceType = [];

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllReferenceTypes',
            success: function (data) {                
                if (data.Success) {                    
                    $scope.ReferenceType = data.Result;         
                } else {                                        
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });


        $scope.GetReferenceTypeDetails = function () {
            if($scope.shipping.FacilityID > 0 && $scope.newContainer.disposition_id > 0) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetReferenceTypeDetails&FacilityID=' + $scope.shipping.FacilityID + '&disposition_id=' + $scope.newContainer.disposition_id,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            if(data.Result) {
                                var reference_type = data.Result;
                                if(reference_type.ReferenceIDRequired == '1') {
                                    $scope.newContainer.ReferenceIDRequired = '1';
                                }
                                $scope.newContainer.ReferenceTypeID = reference_type.ReferenceTypeID;
                                if(reference_type.ReferenceID) {
                                    $scope.newContainer.ReferenceID = reference_type.ReferenceID;
                                }
                                $scope.newContainer.ReferenceType = reference_type.ReferenceType;
                            }
                        } else {  
                            $scope.newContainer.ReferenceIDRequired = '0';     
                            $scope.newContainer.ReferenceTypeID = '';                 
                            $scope.newContainer.ReferenceID = '';
                            $scope.newContainer.ReferenceType = '';
                            
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $scope.newContainer.ReferenceTypeID ='';
                $scope.newContainer.ReferenceIDRequired ='0';
            }
        };


        $scope.GetReferenceType = function () {                            
            $rootScope.$broadcast('preloader:active');

            $scope.newContainer.ReferenceIDRequired = '0';                           
            $scope.newContainer.ReferenceID = '';
            $scope.newContainer.ReferenceType = '';

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReferenceType&ReferenceTypeID=' + $scope.newContainer.ReferenceTypeID,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        if(data.Result) {
                            var reference_type = data.Result;
                            if(reference_type.ReferenceIDRequired == '1') {
                                $scope.newContainer.ReferenceIDRequired = '1';
                            }
                            $scope.newContainer.ReferenceTypeID = reference_type.ReferenceTypeID;
                            if(reference_type.ReferenceID) {
                                $scope.newContainer.ReferenceID = reference_type.ReferenceID;
                            }
                            $scope.newContainer.ReferenceType = reference_type.ReferenceType;
                        }
                    } else {  
                        $scope.newContainer.ReferenceIDRequired = '0';     
                        //$scope.newContainer.ReferenceTypeID = '';                 
                        $scope.newContainer.ReferenceID = '';
                        $scope.newContainer.ReferenceType = '';
                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });           
        };


        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSesstionFacility',
            success: function (data) {
                if (data.Success) {                    
                    $scope.shipping.FacilityID = data.FacilityID;         
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );                    
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCOOList',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                    
                    $scope.COOList = data.COOList;         
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );                    
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });

        if ($stateParams.ShippingID) {
            $scope.ShippingID = $stateParams.ShippingID;

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetShipmentDetails&ShippingID=' + $scope.ShippingID,
                success: function (data) {
                    if (data.Success) {
                        $scope.GetDispositionVendors(data.Result.disposition_id);
                        $scope.shipping = data.Result;
                        $scope.GetFacilityPackageTypes();
                        $scope.GetFacilityByProducts();
                        $scope.GetDestinationRemovalTypes();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        } else {
            $scope.ShippingID = '';
        }

        $scope.GetFacilityPackageTypes = function () {
            console.log('called')
            if($scope.shipping.FacilityID > 0) {

                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetPackageTypes&FacilityID='+$scope.shipping.FacilityID,
                    success: function (data) {
                        if (data.Success) {
                            $scope.PackageTypes = data.Result;
                        } else {
                            $scope.PackageTypes = [];
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $scope.PackageTypes = [];
            }
        };

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.Dispositions = data.Result;
                } else {
                    $scope.Dispositions = [];
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.GetFacilityByProducts = function () {
            //if($scope.shipping.FacilityID > 0 && $scope.shipping.disposition_id > 0) {
            if($scope.newContainer.FacilityID > 0 && $scope.newContainer.disposition_id > 0) {
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=GetByProducts&FacilityID='+$scope.shipping.FacilityID+'&disposition_id='+$scope.shipping.disposition_id,
                    data: 'ajax=GetByProducts&FacilityID='+$scope.newContainer.FacilityID+'&disposition_id='+$scope.newContainer.disposition_id,
                    success: function (data) {
                        if (data.Success) {
                            $scope.ByProducts = data.Result;
                        } else {
                            $scope.ByProducts = [];
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.ByProducts = [];
            }
        };
        

        $scope.GetDestinationRemovalTypes = function () {
            if ($scope.shipping.VendorID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetDestinationRemovalTypes&VendorID=' + $scope.shipping.VendorID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.Dispositions = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.Dispositions = [];
                            $scope.shipping.disposition_id = '';
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.shipping.disposition_id = '';
            }
        };

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                } else {
                    $scope.Facilities = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        jQuery.ajax({
            url: host+'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllVendors',
            success: function(data){
                if(data.Success) {
                    $scope.Vendors = data.Result;
                } else {
                    $scope.Vendors = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.GetDispositionVendors = function (disposition_id) {
            if (disposition_id > 0) {
                $scope.Vendors = [];
                $scope.shipping.VendorID = '';
                $scope.shipping.DestinationPOC = '';
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetDispositionVendors&disposition_id=' + disposition_id,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.Vendors = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.Vendors = [];
                            $scope.shipping.VendorID = '';
                            $scope.shipping.DestinationPOC = '';
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });


            } else {
                $scope.Vendors = [];
                $scope.shipping.VendorID = '';
                $scope.shipping.DestinationPOC = '';
            }
        };

        $scope.GetVendorPOC = function () {
            if ($scope.shipping.VendorID > 0) {
                for (var i = 0; i < $scope.Vendors.length; i++) {
                    if ($scope.Vendors[i].VendorID == $scope.shipping.VendorID) {
                        $scope.shipping.DestinationPOC = $scope.Vendors[i].ContactName;
                    }
                }
            } else {
                $scope.shipping.DestinationPOC = '';
            }
        };

        $scope.CancelTicket = function () {
             var facilityID = $scope.shipping.FacilityID; // Preserve FacilityID
            $scope.shipping = {FacilityID: facilityID};
            window.location = "#!/ShipmentPrep";
        };

        $scope.CreateShipment = function () {
            if(!$scope.shipping.VendorID && !$scope.shipping.DestinationFacilityID) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Select Destination or Destination Facility')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                $scope.shipping.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateShipment&' + $.param($scope.shipping),
                    success: function (data) {
                        $scope.shipping.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            if (data.ShippingID) {
                                var earl = '/ShipmentPrep/' + data.ShippingID;
                                $location.path(earl);
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            }
        };

        $scope.newContainer = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','NotesServer': 'n/a','ServerSanitizationVerificationID': 'n/a','sanitization_seal_id' : 'n/a'};
        $scope.addingContainer = false;
        $scope.ManageContainer = function () {
            $scope.addingContainer = true;
            //$scope.newContainer = {'ShippingID' : $scope.ShippingID,'CustomID' : 'n/a','ContainerNotes' : 'n/a','InventoryNotes' : 'n/a'};
            $scope.newContainer = { 'ShippingID': $scope.ShippingID, 'ContainerNotes': 'n/a', 'SealID': '', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','ServerSanitizationVerificationID': 'n/a','sanitization_seal_id' : 'n/a' };
            setTimeout(function () {
                $window.document.getElementById('ShippingConID').focus();
            }, 500);
        };

        $scope.CreateContainer = function () {
            if ($scope.newContainer.CloseContainer && $scope.newContainer.SealID == '') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Enter Seal ID')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                //if (!$scope.newContainer.PasswordVerified) {
                if (false) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Controller Login ID is not Verified')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {
                    $scope.newContainer.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=CreateContainer&' + $.param($scope.newContainer) + '&FacilityID=' + $scope.shipping.FacilityID,
                        success: function (data) {
                            $scope.newContainer.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            if (data.Success) {
                                if (data.Container) {
                                    $scope.shipping.Containers.push(data.Container);
                                    $scope.newContainer = data.Container;
                                    $scope.GetFacilityByProducts();
                                    $scope.newContainer.PasswordVerified = true;
                                    $scope.newContainer.Notes = 'n/a';
                                    $scope.newContainer.SanitizationVerificationID = 'n/a';
                                    $scope.newContainer.InventoryNotes = 'n/a';
                                    $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
                                    $scope.newContainer.sanitization_seal_id = 'n/a';
                                    $scope.newContainer.NotesServer = 'n/a';
                                }
                                setTimeout(function () {
                                    $window.document.getElementById('SerialNumber').focus();
                                }, 100);
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }
            }
        };

        $scope.EditContainer = function (container) {
            $scope.addingContainer = true;
            $scope.newContainer = container;
            $scope.newContainer.ShippingControllerLoginID = '';
            $scope.newContainer.location = container.LocationName;

            $scope.newContainer.Notes = 'n/a';
            $scope.newContainer.SanitizationVerificationID = 'n/a';
            $scope.newContainer.InventoryNotes = 'n/a';
            $scope.newContainer.NotesServer = 'n/a';

            $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
            $scope.newContainer.sanitization_seal_id = 'n/a';

            $scope.GetFacilityByProducts();

            $scope.ContainerSerials = [];
            $scope.CallServerFunction(0);
        };

        $scope.ScannedServerCount = function () {
            if($scope.ContainerSerials.length > 0) {
                return $scope.Scanned_Servers;
                // var count = 0;
                // for(var i=0;i<$scope.ContainerSerials.length;i++) {
                //     if($scope.ContainerSerials[i].ServerSerialNumber != '' && $scope.ContainerSerials[i].part_type == 'Server') {
                //         count = count + 1;
                //     }
                // }
                // return count;
            } else {
                return 0;
            }
        };

        // $scope.canServerCountbeShown = function () {
        //     if(!$scope.shipping.disposition_id || $scope.shipping.disposition_id == '') {
        //         return false;
        //     } else {
        //         if($scope.Dispositions.length > 0) {
        //             var matching_disposition = false;
        //             for(var i=0;i<$scope.Dispositions.length;i++) {
        //                 if(($scope.Dispositions[i].disposition_id == $scope.shipping.disposition_id) && $scope.Dispositions[i].disposition == 'Terminal-ServerRecycleProcessed') {
        //                     matching_disposition = true;
        //                     break;
        //                 }
        //             }
        //             return matching_disposition;
        //         } else {
        //             return false;
        //         }                
        //     }
        // };

        $scope.canServerCountbeShown = function () {
            if(!$scope.newContainer.disposition_id || $scope.newContainer.disposition_id == '') {
                return false;
            } else {
                if($scope.Dispositions.length > 0) {
                    var matching_disposition = false;
                    for(var i=0;i<$scope.Dispositions.length;i++) {
                        if(($scope.Dispositions[i].disposition_id == $scope.newContainer.disposition_id) && $scope.Dispositions[i].disposition == 'Terminal-ServerRecycleProcessed') {
                            matching_disposition = true;
                            break;
                        }
                    }
                    return matching_disposition;
                } else {
                    return false;
                }                
            }
        };
        

        $scope.ClearContainer = function () {
            $scope.newContainer = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','ServerSanitizationVerificationID': 'n/a','sanitization_seal_id' : 'n/a' };
            $scope.addingContainer = false;
        };

        $scope.AutoNavigateInventorySave = function () {
            $window.document.getElementById('scan_for_save').focus();
        }

        $scope.AutoNavigateSubInventorySave = function () {
            $window.document.getElementById('scan_for_subcomponentsave').focus();
        }

        $scope.AddSerialToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {

                $scope.newContainer.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=AddSerialToContainer&SerialNumber=' + $scope.newContainer.SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newContainer.Notes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newContainer.SanitizationVerificationID + '&AssetScanID=' + $scope.newContainer.AssetScanID + '&UniversalModelNumber=' + $scope.newContainer.UniversalModelNumber+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID,
                    data: 'ajax=AddSerialToContainer&SerialNumber=' + $scope.newContainer.SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newContainer.Notes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newContainer.SanitizationVerificationID + '&sanitization_seal_id='+ $scope.newContainer.sanitization_seal_id+'&AssetScanID=' + $scope.newContainer.AssetScanID + '&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&'+$.param({UniversalModelNumber:$scope.newContainer.UniversalModelNumber})+'&serial_scan_time='+$scope.newContainer.serial_scan_time+'&mpn_scan_time='+$scope.newContainer.mpn_scan_time+'&COOID='+$scope.newContainer.COOID+'&COO='+$scope.newContainer.COO,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer.SerialNumber = '';
                            $scope.newContainer.AssetScanID = '';
                            $scope.newContainer.UniversalModelNumber = '';
                            $scope.newContainer.COOID = '';
                            $scope.newContainer.COO = '';
                            $scope.newContainer.Notes = 'n/a';
                            $scope.newContainer.SanitizationVerificationID = 'n/a';
                            $scope.newContainer.sanitization_seal_id = 'n/a';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('SerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        $scope.ValidateSerialNumber = function (SerialNumber) {
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');
            $scope.newContainer.sanitization_seal_id = 'n/a';
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateSerialNumber&SerialNumber=' + SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID+'&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        // $mdToast.show (
                        //     $mdToast.simple()
                        //         .content(data.Result)
                        //         .action('OK')
                        //         .position('right')
                        //         .hideDelay(0)
                        //         .toastClass('md-toast-success md-block')
                        // );
                        if (data.AssetScanID) {
                            $scope.newContainer.AssetScanID = data.AssetScanID;
                        }
                        if (data.UniversalModelNumber) {
                            $scope.newContainer.UniversalModelNumber = data.UniversalModelNumber;
                            //$window.document.getElementById('scan_for_save').focus();
                            $scope.GetCurrentTime($scope.newContainer,'mpn_scan_time');
                        }
                        if(data.COOID) {
                            $scope.newContainer.COOID = data.COOID;
                            $scope.GetCurrentTime($scope.newContainer,'coo_scan_time');
                        }
                        if(data.COO) {
                            $scope.newContainer.COO = data.COO;                            
                        }
                        if(data.sanitization_verification_id) {                            
                            //$scope.newContainer.SanitizationVerificationID = data.sanitization_verification_id;
                            $window.document.getElementById('sanitization_seal_id').select();
                        } else {
                            $window.document.getElementById('scan_for_save').focus();
                        }                    
                    } else {
                        $scope.newContainer.AssetScanID = '';
                        $scope.newContainer.UniversalModelNumber = '';
                        $scope.newContainer.COOID = '';
                        $scope.newContainer.COO = '';
                        $scope.newContainer.SanitizationVerificationID = 'n/a';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.ValidateInventorySerialNumber = function (SerialNumber) {
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateInventorySerialNumber&SerialNumber=' + SerialNumber + '&ShippingID=' + $scope.shipping.ShippingID+ '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        if (data.UniversalModelNumber) {
                            $scope.newContainer.InventoryUniversalModelNumber = data.UniversalModelNumber;
                            $window.document.getElementById('scan_for_subcomponentsave').focus();
                            $scope.GetCurrentTime($scope.newContainer,'inventory_mpn_scan_time');
                        }
                    } else {
                        $scope.newContainer.InventoryUniversalModelNumber = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.ValidateServerSerialNumber = function (SerialNumber) {
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateServerSerialNumberForContainer&SerialNumber=' + SerialNumber + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID +'&ShippingID=' + $scope.shipping.ShippingID+'&ByPassContainerMatching='+$scope.newContainer.ByPassContainerMatching,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {                        
                        if (data.ServerID) {
                            $scope.newContainer.ServerID = data.ServerID;
                            $scope.newContainer.Type = data.Type;

                            if(data.Type == 'Server') {
                                setTimeout(function () {
                                    //$window.document.getElementById('ServerSanitizationVerificationID').focus();
                                    $window.document.getElementById('ServerSanitizationVerificationID').select();
                                }, 100); 
                            } else {
                                $window.document.getElementById('scan_for_save_server').focus();
                            }
                            //$window.document.getElementById('scan_for_save_server').focus();
                        }
                        if (data.MPN) {
                            $scope.newContainer.MPN = data.MPN;
                            //$window.document.getElementById('scan_for_save_server').focus();
                            $scope.GetCurrentTime($scope.newContainer,'server_mpn_scan_time')
                        }
                        if(data.MediaRecovery_VerificationID) {
                            $scope.newContainer.ServerSanitizationVerificationID = data.MediaRecovery_VerificationID;
                        } else {
                            $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
                        }                        
                    } else {
                        $scope.newContainer.ServerID = '';
                        $scope.newContainer.MPN = '';
                        $scope.newContainer.Type = '';
                        $scope.newContainer.ServerSanitizationVerificationID = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                        
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.AddServerSerialToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {
                $scope.newContainer.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=AddServerSerialToContainer&SerialNumber=' + $scope.newContainer.SerialNumberServer + '&Notes=' + $scope.newContainer.NotesServer + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newContainer.SanitizationVerificationID + '&ServerID=' + $scope.newContainer.ServerID + '&UniversalModelNumber=' + $scope.newContainer.MPN+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&Type='+$scope.newContainer.Type+'&ShippingID=' + $scope.shipping.ShippingID+'&ServerSanitizationVerificationID='+$scope.newContainer.ServerSanitizationVerificationID+'&ByPassContainerMatching='+$scope.newContainer.ByPassContainerMatching+'&serial_scan_time='+$scope.newContainer.server_serial_scan_time+'&mpn_scan_time='+$scope.newContainer.server_mpn_scan_time,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );            
                            $scope.newContainer.SerialNumberServer = '';
                            $scope.newContainer.ServerID = '';
                            $scope.newContainer.MPN = '';
                            $scope.newContainer.Type = '';
                            $scope.newContainer.NotesServer = 'n/a';
                            $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('SerialNumberServer').focus();     
                            $scope.newContainer.ByPassContainerMatching = '0';                   
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.newContainer.ByPassContainerMatching = '0';
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }

        };


        $scope.AddByProductToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {
                $scope.newContainer.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=AddByProductToShipmentContainer&byproduct_id=' + $scope.newContainer.byproduct_id + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );            
                            $scope.newContainer.byproduct_id = '';                        
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };

        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            if ($scope.newContainer.CreatedBy > 0) {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetContainerSerials&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                    success: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.ContainerSerials = data.Result;
                            if (data.total) {
                                $scope.total = data.total;
                            }
                            if (data.Scanned_Servers) {
                                $scope.Scanned_Servers = data.Scanned_Servers;
                            }
                        } else {
                            if (data.Scanned_Servers) {
                                $scope.Scanned_Servers = data.Scanned_Servers;
                            }
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-info md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.ContainerSerials = [];
            }
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetContainerSerials&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.ContainerSerials = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                        if (data.Scanned_Servers) {
                            $scope.Scanned_Servers = data.Scanned_Servers;
                        }
                    } else {
                        if (data.Scanned_Servers) {
                            $scope.Scanned_Servers = data.Scanned_Servers;
                        }
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic


        $scope.DeleteSerialFromContainer = function (serial, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteSerialFromContainer&' + $.param(serial) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.ContainerSerials = [];
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };


        $scope.DeleteByProductFromContainer = function (serial, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteByProductFromContainer&' + $.param(serial) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.ContainerSerials = [];
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };




        $scope.DeleteContainerFromShipment = function (container, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to move Serials to BIN and Delete Container ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteContainerFromShipment&' + $.param(container) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','sanitization_seal_id' : 'n/a' };
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };


        $scope.DeleteByProductContainerFromShipment = function (container, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete Container ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteByProductContainerFromShipment&' + $.param(container) + '&ShippingID=' + $scope.shipping.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a' ,'sanitization_seal_id' : 'n/a'};
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };


        function CloseContainerTPVRController($scope,$mdDialog,CurrentContainer,$mdToast,$window) {
            $scope.CurrentContainer = CurrentContainer;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };   
            
            $scope.FocusNextField = function (nextid,wait) {            
                if(wait == '1') {
                    setTimeout(function () {
                        $window.document.getElementById(nextid).focus();
                    }, 100);
                } else {
                    $window.document.getElementById(nextid).focus();
                }
            };
            
        }

        $scope.CurrentContainer = {};
        $scope.confirmDetails = {};
        function afterShowAnimation1 () {            
            $window.document.getElementById("AuditController").focus();            
        }

        $scope.CloseContainer = function (container, ev) {

            $mdDialog.show({
                controller: CloseContainerTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation1,
                clickOutsideToClose:true,
                resolve: {
                    CurrentContainer: function () {
                    return container;
                    }
                }
            })
            .then(function(confirmDetails) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CloseContainer&' + $.param(container) + '&ShippingID=' + $scope.shipping.ShippingID+'&'+$.param(confirmDetails),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );                            
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
                
                
            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });

        };


        $scope.ReopenContainer = function (container, ev) {
            
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Reopen the container?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Reopen')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $scope.CurrentShipment = {};
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ReopenContainer&ShippingID=' + $scope.shipping.ShippingID+'&'+$.param(container),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.SearchContainer = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });

        };



        $scope.AddInventorySerialToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {

                $scope.newContainer.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=AddInventorySerialToContainer&SerialNumber=' + $scope.newContainer.InventorySerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newContainer.InventoryNotes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&UniversalModelNumber=' + $scope.newContainer.InventoryUniversalModelNumber+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID,
                    data: 'ajax=AddInventorySerialToContainer&SerialNumber=' + $scope.newContainer.InventorySerialNumber + '&ShippingID=' + $scope.shipping.ShippingID + '&Notes=' + $scope.newContainer.InventoryNotes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&'+$.param({UniversalModelNumber : $scope.newContainer.InventoryUniversalModelNumber})+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&serial_scan_time='+$scope.newContainer.inventory_serial_scan_time+'&mpn_scan_time='+$scope.newContainer.inventory_mpn_scan_time,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer.InventorySerialNumber = '';
                            $scope.newContainer.InventoryUniversalModelNumber = '';
                            $scope.newContainer.InventoryNotes = 'n/a';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('InventorySerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        function SanitizationTPVRController($scope, $mdDialog, $mdToast) {
            $scope.hide = function () {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function () {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        function afterShowAnimation () {            
            $window.document.getElementById("password").focus();
        }
        $scope.confirmDetails = {};
        $scope.ValidateSanitizationControllerPopup = function (ev) {
            $scope.newContainer.PasswordVerified = false;
            $mdDialog.show({
                controller: SanitizationTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose: true
            })
                .then(function (confirmDetails) {
                    $rootScope.$broadcast('preloader:active');
                    $scope.confirmDetails = confirmDetails;
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateRemovalController&UserName=' + $scope.newContainer.ShippingControllerLoginID + '&Password=' + $scope.confirmDetails.Password,
                        success: function (data) {
                            if (data.Success) {
                                $scope.newContainer.PasswordVerified = true;
                                if($scope.newContainer.CreatedBy > 0) {//Updating existing
                                    setTimeout(function () {
                                        $window.document.getElementById('SerialNumber').focus();
                                    }, 100);
                                } else {
                                    setTimeout(function () {
                                        $window.document.getElementById('save_button').focus();
                                    }, 100);
                                }                                
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                $scope.newContainer.PasswordVerified = false;
                            }
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $scope.newContainer.PasswordVerified = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }, function (confirmDetails) {
                    $scope.confirmDetails = confirmDetails;
                });
        };


        function LocationChange(text) {
            $scope.newContainer.location = text;
        }

        function selectedLocationChange(item) {
            if (item) {
                if (item.value) {
                    $scope.newContainer.location = item.value;
                } else {
                    $scope.newContainer.location = '';
                }
            } else {
                $scope.newContainer.location = '';
            }
        }

        $scope.queryLocationSearch = queryLocationSearch;
        $scope.LocationChange = LocationChange;
        $scope.selectedLocationChange = selectedLocationChange;
        function queryLocationSearch(query) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'administration/includes/admin_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + $scope.shipping.FacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }



        function LocationChange1(text) {
            $scope.newContainer.group = text;
        }

        function selectedLocationChange1(item) {
            if (item) {
                if (item.value) {
                    $scope.newContainer.group = item.value;
                } else {
                    $scope.newContainer.group = '';
                }
            } else {
                $scope.newContainer.group = '';
            }
        }

        $scope.queryLocationSearch1 = queryLocationSearch1;
        $scope.LocationChange1 = LocationChange1;
        $scope.selectedLocationChange1 = selectedLocationChange1;
        function queryLocationSearch1(query) {
            if (query) {
                if (query != '' && query != 'undefined') {                    
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + $scope.shipping.FacilityID+'&LocationType=Outbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }


        $scope.AddContainerToShipment = function (ev) {
            if($scope.SearchContainer != '') {
                
                var confirm = $mdDialog.confirm()
                    .title('Are you sure, You want to add Container to Shipment?')
                    .content('')
                    .ariaLabel('Lucky day')
                    .targetEvent(ev)
                    .ok('ADD')
                    .cancel('Cancel');
                $mdDialog.show(confirm).then(function () {
                    $scope.CurrentShipment = {};
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=AddContainerToShipment&ShippingID=' + $scope.shipping.ShippingID+'&ShippingContainerID='+$scope.SearchContainer,
                        success: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            if (data.Success) {
                                $scope.SearchContainer = '';
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                if(data.Container) {
                                    $scope.shipping.Containers.push(data.Container);
                                }
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }, function () {

                });

            }
        };


        $scope.FocusNextField = function (nextid,wait) {            
            if(wait == '1') {
                setTimeout(function () {
                    $window.document.getElementById(nextid).focus();
                }, 100);
            } else {
                $window.document.getElementById(nextid).focus();
            }
        };

        $scope.GetExactMPN = function (MPN) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTrimmedMPN&MPN=' + MPN,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.newContainer.UniversalModelNumber = data.ExactMPN;    
                        $scope.AutoNavigateInventorySave();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };

        $scope.GetExactMPNSN = function (MPN) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTrimmedMPN&MPN=' + MPN,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.newContainer.InventoryUniversalModelNumber = data.ExactMPN;    
                        $scope.AutoNavigateSubInventorySave();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };



        $scope.AddMediaToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {
                if($scope.newContainer.MediaID > 0) {                    
                    $scope.newContainer.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',                    
                        data: 'ajax=AddMediaToContainer&MediaSerialNumber=' + $scope.newContainer.MediaSerialNumber + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&'+$.param({UniversalModelNumber : $scope.newContainer.ReuseMediaUniversalModelNumber})+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&ShippingID='+$scope.shipping.ShippingID+'&serial_scan_time='+$scope.newContainer.media_serial_scan_time+'&mpn_scan_time='+$scope.newContainer.media_mpn_scan_time,
                        success: function (data) {
                            $scope.newContainer.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            if (data.Success) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                $scope.newContainer.MediaSerialNumber = '';
                                $scope.newContainer.ReuseMediaUniversalModelNumber = '';
                                $scope.newContainer.ReuseMedia_part_type = '';
                                $scope.CallServerFunction(0);
                                $window.document.getElementById('MediaSerialNumber').focus();
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }
                    });
                    
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Invalid Media')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }              
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }

        };

        $scope.ValidateMediaSerialNumber = function (MediaSerialNumber) {

            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateMediaSerialNumber&SerialNumber=' + MediaSerialNumber + '&ShippingID=' + $scope.shipping.ShippingID+'&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {                        
                        if (data.MediaID) {
                            $scope.newContainer.MediaID = data.MediaID;
                        }
                        if (data.UniversalModelNumber) {
                            $scope.newContainer.ReuseMediaUniversalModelNumber = data.UniversalModelNumber;
                            $window.document.getElementById('ReuseMediaUniversalModelNumber').focus();
                            $scope.GetCurrentTime($scope.newContainer,'media_mpn_scan_time');
                        }
                        if(data.MediaType) {
                            $scope.newContainer.ReuseMedia_part_type = data.MediaType
                        }
                    } else {
                        $scope.newContainer.MediaID = '';
                        $scope.newContainer.ReuseMediaUniversalModelNumber = '';
                        $scope.newContainer.ReuseMedia_part_type = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });


        };



        $scope.Unlink = function (container, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Unlink Container from Shipment?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Unlink')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=UnlinkContainerFromShipment&' + $.param(container),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a','sanitization_seal_id' : 'n/a' };
                            location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };

        $scope.ValidateSanitizationSealID = function (sanitization_seal_id,SerialNumber,AssetScanID) {
            
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateSanitizationSealID&SerialNumber=' + SerialNumber + '&AssetScanID=' + AssetScanID +'&sanitization_seal_id='+sanitization_seal_id,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5)
                                .toastClass('md-toast-success md-block')
                        );   
                        if(data.sanitization_verification_id) {                            
                            $scope.newContainer.SanitizationVerificationID = data.sanitization_verification_id;                            
                        } 
                        $scope.AutoNavigateInventorySave();                     
                    } else {                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
            
        };

        $scope.DestinationChanged = function () {
            $scope.shipping.DestinationFacilityID = '';
        };

        $scope.DestinationFacilityChanged = function () {
            $scope.shipping.VendorID = '';
            $scope.shipping.DestinationPOC = '';
        };

    });


    //Shipment Removal Start
    module.controller("shipment_removal", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Shipment Removal',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.today = new Date();
        //$scope.time = $scope.today.getHours()+':'+$scope.today.getMinutes();
        //d.getSeconds(); // => 51
        $scope.Shipments = [];
        $scope.CurrentShipment = {};
        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSesstionFacility',
            success: function (data) {
                if (data.Success) {
                    $scope.filter_text[0].FacilityName = data.FacilityName;
                    $scope.currentPage = 0;
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    $scope.currentPage = 0;
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetActiveShipments&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.Shipments = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetActiveShipments&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.Shipments = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic


        $scope.GetShipmentContainers = function (shipment) {
            if (shipment.Containers.length == 0 || shipment.PalletID) {
                shipment.loading = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetShipmentContainers&ShippingID=' + shipment.ShippingID,
                    success: function (data) {
                        if (data.Success) {
                            shipment.Containers = data.Result;
                        } else {
                            shipment.Containers = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        if (shipment.PalletID) {
                            window.open('../label/master/examples/shipment_pallet_label.php?id=' + shipment.PalletID, '_blank');
                        }
                        shipment.loading = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        shipment.loading = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            }
        };

        $scope.CheckClosedContainers = function (shipment) {

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CheckClosedContainers&ShippingID=' + shipment.ShippingID,
                success: function (data) {
                    if (data.Success) {//All containers of the shipment are closed                        
                        $rootScope.$broadcast('preloader:hide');
                    } else {
                        $rootScope.$broadcast('preloader:hide');
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }                    
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
            //End check If all containers are closed 

        };

        $scope.Carriers = [];
        $scope.MakeCurrentShipment = function (shipment) {

            //Start check If all containers are closed
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=CheckClosedContainers&ShippingID=' + shipment.ShippingID,
                success: function (data) {
                    if (data.Success) {//All containers of the shipment are closed
                        //Start getting facility carriers
                        jQuery.ajax({
                            url: host + 'shipping/includes/shipping_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=GetShipmentCarriers&FacilityID=' + shipment.FacilityID,
                            success: function (data) {
                                if (data.Success) {
                                    $scope.Carriers = data.Result;
                                } else {
                                    $scope.Carriers = [];
                                }                    
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();;
                            }, error: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();;
                            }
                        });
                        //End getting fcility carriers


                         //Start Get Next Step            
                         
                        $scope.CurrentShipment = shipment;
                        setTimeout(function () {
                            $window.document.getElementById('ApproverLogin').focus();
                        }, 500);
                         
                        // jQuery.ajax({
                        //     url: host + 'shipping/includes/shipping_submit.php',
                        //     dataType: 'json',
                        //     type: 'post',
                        //     data: 'ajax=GetDispositioNextStep&disposition_id=' + shipment.disposition_id,
                        //     success: function (data) {
                        //         if (data.Success) {
                        //             shipment.NextStep = data.Result;
                        //             shipment.PONumber = 'n/a';
                        //             $scope.CurrentShipment = shipment;
                        //             setTimeout(function () {
                        //                 $window.document.getElementById('ApproverLogin').focus();
                        //             }, 500);
                        //         } else {
                        //             $mdToast.show(
                        //                 $mdToast.simple()
                        //                     .content(data.Result)
                        //                     .action('OK')
                        //                     .position('right')
                        //                     .hideDelay(0)
                        //                     .toastClass('md-toast-danger md-block')
                        //             );
                        //         }
                        //         $rootScope.$broadcast('preloader:hide');
                        //         initSessionTime(); $scope.$apply();;
                        //     }, error: function (data) {
                        //         $rootScope.$broadcast('preloader:hide');
                        //         initSessionTime(); $scope.$apply();;
                        //     }
                        // });

                        //End Get Next Step

                        
                    } else {
                        $rootScope.$broadcast('preloader:hide');
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }                    
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
            //End check If all containers are closed           
        };

        $scope.ShipShipment = function (ev) {
            if (!$scope.CurrentShipment.PasswordVerified) {
                // $mdToast.show(
                //     $mdToast.simple()
                //         .content('Approver Login ID is not Verified')
                //         .action('OK')
                //         .position('right')
                //         .hideDelay(0)
                //         .toastClass('md-toast-danger md-block')
                // );

                $scope.ValidateSanitizationControllerPopup(ev);
            } else if ($scope.CurrentShipment.ShippingID) {
                // alert($scope.CurrentShipment.ShippedDate);
                // alert($scope.today);

                var timeString = $scope.CurrentShipment.ShippedTime.getHours() + ':' + $scope.CurrentShipment.ShippedTime.getMinutes() + ':00';
                var dateString = $scope.CurrentShipment.ShippedDate.getMonth() + 1 + '/' + $scope.CurrentShipment.ShippedDate.getDate() + '/' + $scope.CurrentShipment.ShippedDate.getFullYear()
                var dateObj = new Date(dateString + ' ' + timeString);

                if (dateObj.getTime() > $scope.today.getTime()) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Removal Time should not be Future Time')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                } else {

                    $scope.CurrentShipment.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ShipShipment&' + $.param($scope.CurrentShipment)+'&'+$.param($scope.confirmDetails),
                        success: function (data) {
                            if (data.Success) {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );

                                if($scope.CurrentShipment.DestinationFacilityName == 'MDT110') {
                                    $scope.GenerateDemanASN($scope.CurrentShipment,ev);
                                }
                                $scope.CallServerFunction(0);
                                $scope.CurrentShipment = {};
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.CurrentShipment.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $scope.CurrentShipment.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }
            }
        };

        $scope.RecordUserNavigationTransaction = function (TransactionType,Description,PageURL,id) {

            jQuery.ajax({
                url: host + 'receive/includes/receive_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserNavigationTransaction&TransactionType=' + TransactionType + '&Description=' + Description + '&PageURL=' + PageURL+id,
                success: function (data) {
                    
                    if (data.Success) {                        
                    } else {                        
                    }
                    initSessionTime(); $scope.$apply();
                }, error: function (data) {                    
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });

        };

        $scope.NavigateShipmentPrep = function (ship) {
            $scope.RecordUserNavigationTransaction('Shipping ---> Shipment Removal','Hit on Edit Button','shipping/#!/ShipmentPrep/',ship.ShippingID);
            $window.location.href = '#!/ShipmentPrep/' + ship.ShippingID;
        };


        $scope.DeleteShipment = function (ship, ind, ev) {

            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete Shipment?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $scope.CurrentShipment = {};
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteShipment&ShippingID=' + ship.ShippingID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });

        };
        $scope.exportshipment = function (shipment) {

            jQuery.ajax({
                url: host + 'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserAdminActivity&Description=Shipment Exported (' + shipment + ') &Type=Shipping ---> Shipment Removal',
                success: function (data) {
                    if (data.Success == true) {
                    } else {
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    //alert(data);
                    initSessionTime(); $scope.$apply();;
                }
            });

            window.location = "templates/shipmentdetailsxlsx.php?ShippingID=" + shipment;
        }
        $scope.exportpacking = function (shipment) {

            jQuery.ajax({
                url: host + 'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserAdminActivity&Description=Shipment Packing Slip Exported (' + shipment + ') &Type=Shipping ---> Shipment Removal',
                success: function (data) {
                    if (data.Success == true) {
                    } else {
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    //alert(data);
                    initSessionTime(); $scope.$apply();;
                }
            });

            window.location = "templates/standardpackingslipxlsx.php?ShippingID=" + shipment;
        }
        $scope.exportstandardasn = function (shipment) {

            jQuery.ajax({
                url: host + 'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserAdminActivity&Description=Shipment Standard ASN Exported (' + shipment + ') &Type=Shipping ---> Shipment Removal',
                success: function (data) {
                    if (data.Success == true) {
                    } else {
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    //alert(data);
                    initSessionTime(); $scope.$apply();;
                }
            });

            window.location = "templates/standardasnxlsx.php?ShippingID=" + shipment;
        }
        $scope.exportdeliveryasn = function (shipment) {

            jQuery.ajax({
                url: host + 'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserAdminActivity&Description=Shipment VMI ASN Exported (' + shipment + ') &Type=Shipping ---> Shipment Removal',
                success: function (data) {
                    if (data.Success == true) {
                    } else {
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    //alert(data);
                    initSessionTime(); $scope.$apply();;
                }
            });

            window.location = "templates/deliveryasnxlsx.php?ShippingID=" + shipment;
        }

        $scope.GenerateShipmentPalletID = function (ship, ev) {
            if (ship.Containers.length > 0) {
                var selected = 0;
                var selected_containers = { 'Containers': [] };
                for (var i = 0; i < ship.Containers.length; i++) {
                    if (ship.Containers[i].Selected == '1') {
                        selected = selected + 1;
                        //selected_containers.Containers[ship.Containers[i].ShippingContainerID] = '1';
                        selected_containers.Containers.push({ 'ContainerID': ship.Containers[i].ShippingContainerID });
                    }
                }
                if (selected > 0) {

                    var confirm = $mdDialog.confirm()
                        .title('Are you sure, You want to generate Pallet ID with selected ' + selected + ' containers ?')
                        .content('')
                        .ariaLabel('Lucky day')
                        .targetEvent(ev)
                        .ok('Generate')
                        .cancel('Cancel');
                    $mdDialog.show(confirm).then(function () {
                        $rootScope.$broadcast('preloader:active');
                        jQuery.ajax({
                            url: host + 'shipping/includes/shipping_submit.php',
                            dataType: 'json',
                            type: 'post',
                            data: 'ajax=GenerateShipmentPalletID&' + $.param(selected_containers) + '&ShippingID=' + ship.ShippingID,
                            success: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                if (data.Success) {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );
                                    if (data.PalletID) {
                                        ship.PalletID = data.PalletID;
                                        $scope.GetShipmentContainers(ship);
                                        //window.open('../label/master/examples/shipment_pallet_label.php?id='+data.PalletID, '_blank');
                                    }
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                }
                                initSessionTime(); $scope.$apply();;
                            }, error: function (data) {
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();;
                            }
                        });

                    }, function () {

                    });

                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('No Containers Selected')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('No Containers Available')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };



        $scope.RemovePalletFromContainer = function (container, ev) {

            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to remove Container from Pallet ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Yes')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $scope.CurrentShipment = {};
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=RemovePalletFromContainer&ShippingContainerID=' + container.ShippingContainerID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            container.PalletID = '';
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });

        };


        $scope.FocusNextField = function (nextid,wait) {            
            if(wait == '1') {
                setTimeout(function () {
                    $window.document.getElementById(nextid).focus();
                }, 100);
            } else {
                $window.document.getElementById(nextid).focus();
            }
        };




        function SanitizationTPVRController($scope, $mdDialog, $mdToast) {
            $scope.hide = function () {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function () {
                $mdDialog.cancel($scope.confirmDetails);
            };

            $scope.NavigateToPassword = function () {
                $window.document.getElementById("Password").focus();
            };
        }
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();
        }
        $scope.confirmDetails = {};
        $scope.ValidateSanitizationControllerPopup = function (ev) {
            $scope.CurrentShipment.PasswordVerified = false;
            $mdDialog.show({
                controller: SanitizationTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose: true
            })
            .then(function (confirmDetails) {
                $rootScope.$broadcast('preloader:active');
                $scope.confirmDetails = confirmDetails;
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ValidateRemovalController1&UserName=' + $scope.confirmDetails.AuditController + '&Password=' + $scope.confirmDetails.Password,
                    success: function (data) {
                        if (data.Success) {
                            $scope.CurrentShipment.PasswordVerified = true;
                            //$window.document.getElementById('ApprovedDate').focus();                               
                            //$scope.ShipShipment(ev);
                            $window.document.getElementById('ShipmentButton').focus();
                            
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.CurrentShipment.PasswordVerified = false;
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $scope.CurrentShipment.PasswordVerified = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function (confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });
        };

        $scope.GenerateDemanASN = function (ship,ev) {

            $scope.CurrentShipment = {};
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateDemanASN&ShippingID=' + ship.ShippingID,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });


            // var confirm = $mdDialog.confirm()
            //     .title('Are you sure, You want to generate ASN for Deman ?')
            //     .content('')
            //     .ariaLabel('Lucky day')
            //     .targetEvent(ev)
            //     .ok('Yes')
            //     .cancel('Cancel');
            // $mdDialog.show(confirm).then(function () {
            //     $scope.CurrentShipment = {};
            //     $rootScope.$broadcast('preloader:active');
            //     jQuery.ajax({
            //         url: host + 'shipping/includes/shipping_submit.php',
            //         dataType: 'json',
            //         type: 'post',
            //         data: 'ajax=GenerateDemanASN&ShippingID=' + ship.ShippingID,
            //         success: function (data) {
            //             $rootScope.$broadcast('preloader:hide');
            //             if (data.Success) {
            //                 $mdToast.show(
            //                     $mdToast.simple()
            //                         .content(data.Result)
            //                         .action('OK')
            //                         .position('right')
            //                         .hideDelay(0)
            //                         .toastClass('md-toast-success md-block')
            //                 );
            //             } else {
            //                 $mdToast.show(
            //                     $mdToast.simple()
            //                         .content(data.Result)
            //                         .action('OK')
            //                         .position('right')
            //                         .hideDelay(0)
            //                         .toastClass('md-toast-danger md-block')
            //                 );
            //             }
            //             initSessionTime(); $scope.$apply();;
            //         }, error: function (data) {
            //             $rootScope.$broadcast('preloader:hide');
            //             initSessionTime(); $scope.$apply();;
            //         }
            //     });

            // }, function () {

            // });

        };




    });
    //Shipment Removal end



    //Removed Shipments Start
    module.controller("removed_shipments", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Removed Shipments',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.Shipments = [];
        $scope.CurrentShipment = {};
        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        //$scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSesstionFacility',
            success: function (data) {
                if (data.Success) {
                    $scope.filter_text[0].FacilityName = data.FacilityName;
                    $scope.currentPage = 0;
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );
                    $scope.currentPage = 0;
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetRemovedShipments&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.Shipments = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetRemovedShipments&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.Shipments = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic


        $scope.GetShipmentContainers = function (shipment) {
            if (shipment.Containers.length == 0) {
                shipment.loading = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRemovedShipmentContainers&ShippingID=' + shipment.ShippingID,
                    success: function (data) {
                        if (data.Success) {
                            shipment.Containers = data.Result;
                        } else {
                            shipment.Containers = [];
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        shipment.loading = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        shipment.loading = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            }
        };

        $scope.RemoveshipXLS = function () {

            jQuery.ajax({
                url: host + 'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserAdminActivity&Description=Removed Shipments Exported&Type=Shipping ---> Removed Shipments',
                success: function (data) {
                    if (data.Success == true) {
                    } else {
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    alert(data);
                    initSessionTime(); $scope.$apply();;
                }
            });

            window.location = "templates/shipmentexport.php";
        };

        $scope.exportshipment = function (shipment) {

            jQuery.ajax({
                url: host + 'administration/includes/admin_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=RecordUserAdminActivity&Description=Shipment Exported (' + shipment + ') &Type=Shipping ---> Removed Shipments',
                success: function (data) {
                    if (data.Success == true) {
                    } else {
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    alert(data);
                    initSessionTime(); $scope.$apply();;
                }
            });

            window.location = "templates/shipmentdetailsxlsx.php?ShippingID=" + shipment;
        }
    });
    //Removed Shipments End


    //Start Shipment Container Start
    module.controller("shipment_container", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Shipment Container',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });


        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        


        $scope.newContainer = { 'ContainerNotes': 'n/a', 'SanitizationVerificationID': 'n/a', 'Notes': 'n/a', 'InventoryNotes': 'n/a', 'NotesServer': 'n/a', 'ServerSanitizationVerificationID': 'n/a','sanitization_seal_id' : 'n/a' };
        $scope.addingContainer = false;

        $scope.Dispositions = [];
        $scope.PackageTypes = [];
        $scope.Facilities = [];
        $scope.ByProducts = [];
        $scope.ReferenceType = [];

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllReferenceTypes',
            success: function (data) {                
                if (data.Success) {                    
                    $scope.ReferenceType = data.Result;         
                } else {                                        
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSesstionFacility',
            success: function (data) {
                if (data.Success) {                    
                    $scope.newContainer.FacilityID = data.FacilityID;         
                    $scope.GetFacilityContainers();
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );                    
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetCOOList',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                    
                    $scope.COOList = data.COOList;         
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );                    
                }
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.FocusNextField = function (nextid,wait) {            
            if(wait == '1') {
                setTimeout(function () {
                    $window.document.getElementById(nextid).focus();
                }, 100);
            } else {
                $window.document.getElementById(nextid).focus();
            }
        };

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                } else {
                    $scope.Facilities = [];
                }                
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {                
                initSessionTime(); $scope.$apply();;
            }
        });

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.Dispositions = data.Result;
                } else {
                    $scope.Dispositions = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        

        $scope.GetFacilityByProducts = function () {            
            if($scope.newContainer.FacilityID > 0 && $scope.newContainer.disposition_id > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetByProducts&FacilityID='+$scope.newContainer.FacilityID+'&disposition_id='+$scope.newContainer.disposition_id,
                    success: function (data) {
                        if (data.Success) {
                            $scope.ByProducts = data.Result;
                        } else {
                            $scope.ByProducts = [];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.ByProducts = [];
            }
            
        };
        
        $scope.GetFacilityContainers = function () {
            if($scope.newContainer.FacilityID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetFacilityPackageTypes&FacilityID='+$scope.newContainer.FacilityID,
                    success: function (data) {
                        if (data.Success) {
                            $scope.PackageTypes = data.Result;
                        } else {
                            $scope.PackageTypes = [];
                            $scope.newContainer.idPackage = '';
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.PackageTypes = [];
                $scope.newContainer.idPackage = '';
            }           
        };
        

        $scope.CreateContainer = function () {
            
            //if (!$scope.newContainer.PasswordVerified) {
            if (false) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            } else {
                $scope.newContainer.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateContainerWithoutShippingID&' + $.param($scope.newContainer),
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            
                            if(data.ShippingContainerID) {
                                //$window.location.href = '#!/ShipmentContainer/' + data.ShippingContainerID;
                                $scope.GetShippingDetails(data.ShippingContainerID,'old');
                            } else {
                                //location.reload();
                                $scope.GetShippingDetails($scope.newContainer.ShippingContainerID,'old');
                            }
                            if (data.Container) {
                                $scope.shipping.Containers.push(data.Container);
                                $scope.newContainer = data.Container;
                                $scope.newContainer.PasswordVerified = true;
                                $scope.newContainer.Notes = 'n/a';
                                $scope.newContainer.SanitizationVerificationID = 'n/a';
                                $scope.newContainer.InventoryNotes = 'n/a';
                                $scope.newContainer.NotesServer = 'n/a';
                                $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
                                $scope.newContainer.sanitization_seal_id = 'n/a';
                            }
                            // setTimeout(function () {
                            //     $window.document.getElementById('SerialNumber').focus();
                            // }, 100);                            
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }            
        };



        function SanitizationTPVRController($scope, $mdDialog, $mdToast) {
            $scope.hide = function () {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function () {
                $mdDialog.cancel($scope.confirmDetails);
            };
        }
        function afterShowAnimation () {            
            $window.document.getElementById("password").focus();
        }
        $scope.confirmDetails = {};
        $scope.ValidateSanitizationControllerPopup = function (ev) {
            $scope.newContainer.PasswordVerified = false;
            $mdDialog.show({
                controller: SanitizationTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose: true
            })
                .then(function (confirmDetails) {
                    $rootScope.$broadcast('preloader:active');
                    $scope.confirmDetails = confirmDetails;
                    jQuery.ajax({
                        url: host + 'shipping/includes/shipping_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=ValidateRemovalController&UserName=' + $scope.newContainer.ShippingControllerLoginID + '&Password=' + $scope.confirmDetails.Password,
                        success: function (data) {
                            if (data.Success) {
                                $scope.newContainer.PasswordVerified = true;
                                if($scope.newContainer.CreatedBy > 0) {//Updating existing
                                    setTimeout(function () {
                                        $window.document.getElementById('SerialNumber').focus();
                                    }, 100);
                                } else {
                                    // setTimeout(function () {
                                    //     $window.document.getElementById('save_button').focus();
                                    // }, 100);
                                    if(!$scope.containerForm.$invalid) {
                                        $scope.CreateContainer();
                                    }                                    
                                }                                
                            } else {
                                $mdToast.show(
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                $scope.newContainer.PasswordVerified = false;
                            }
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();;
                        }, error: function (data) {
                            $scope.newContainer.PasswordVerified = false;
                            $rootScope.$broadcast('preloader:hide');
                            $scope.data = data;
                            initSessionTime(); $scope.$apply();;
                        }
                    });

                }, function (confirmDetails) {
                    $scope.confirmDetails = confirmDetails;
                });
        };


        function LocationChange(text) {
            $scope.newContainer.location = text;
        }

        function selectedLocationChange(item) {
            if (item) {
                if (item.value) {
                    $scope.newContainer.location = item.value;
                } else {
                    $scope.newContainer.location = '';
                }
            } else {
                $scope.newContainer.location = '';
            }
        }

        $scope.queryLocationSearch = queryLocationSearch;
        $scope.LocationChange = LocationChange;
        $scope.selectedLocationChange = selectedLocationChange;
        function queryLocationSearch(query) {
            if (query) {
                if (query != '' && query != 'undefined') {
                    return $http.get(host + 'administration/includes/admin_submit.php?ajax=GetMatchingLocations&keyword=' + query + '&FacilityID=' + $scope.newContainer.FacilityID)
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['LocationName'], LocationName: res.data.Result[i]['LocationName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }


        function LocationChange1(text) {
            $scope.newContainer.group = text;
        }

        function selectedLocationChange1(item) {
            if (item) {
                if (item.value) {
                    $scope.newContainer.group = item.value;
                } else {
                    $scope.newContainer.group = '';
                }
            } else {
                $scope.newContainer.group = '';
            }
        }

        $scope.queryLocationSearch1 = queryLocationSearch1;
        $scope.LocationChange1 = LocationChange1;
        $scope.selectedLocationChange1 = selectedLocationChange1;
        function queryLocationSearch1(query) {
            if (query) {
                if (query != '' && query != 'undefined') {                    
                    return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&FacilityID=' + $scope.newContainer.FacilityID+'&LocationType=Outbound Storage')
                        .then(function (res) {
                            if (res.data.Success == true) {
                                if (res.data.Result.length > 0) {
                                    var result_array = [];
                                    for (var i = 0; i < res.data.Result.length; i++) {
                                        result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            } else {
                                return [];
                            }
                        });
                } else {
                    return [];
                }
            } else {
                return [];
            }
        }




        $scope.GetShippingDetails = function (ShippingContainerID,type) {

            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                //data: 'ajax=GetShippingContainerDetails&ShippingContainerID='+ShippingContainerID,
                data: 'ajax=GetShippingContainerDetails1&ShippingContainerID='+ShippingContainerID,
                success: function (data) {
                    if (data.Success == true) {
                        $scope.newContainer = data.Result;
                        $scope.GetFacilityContainers();
                        $scope.GetFacilityByProducts()
                        $scope.CallServerFunction(0);
                        if(type == 'old') {
                            $scope.newContainer.PasswordVerified = true;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $scope.newContainer.Notes = 'n/a';
                    $scope.newContainer.SanitizationVerificationID = 'n/a';
                    $scope.newContainer.InventoryNotes = 'n/a';
                    $scope.newContainer.NotesServer = 'n/a';
                    $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
                    $scope.newContainer.sanitization_seal_id = 'n/a';
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    alert('error');
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });

        };

        if ($stateParams.ShippingContainerID) {
            $scope.GetShippingDetails($stateParams.ShippingContainerID,'new');
        }


        $scope.ValidateSerialNumber = function (SerialNumber) {
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');
            $scope.newContainer.sanitization_seal_id = 'n/a';
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateSerialNumberForShipmentContainer&SerialNumber=' + SerialNumber + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {                        
                        if (data.AssetScanID) {
                            $scope.newContainer.AssetScanID = data.AssetScanID;
                        }
                        if (data.UniversalModelNumber) {
                            $scope.newContainer.UniversalModelNumber = data.UniversalModelNumber;
                            //$window.document.getElementById('scan_for_save').focus();
                            $scope.GetCurrentTime($scope.newContainer,'mpn_scan_time');
                        }

                        if(data.COOID) {
                            $scope.newContainer.COOID = data.COOID;
                            $scope.GetCurrentTime($scope.newContainer,'coo_scan_time');
                        }
                        if(data.COO) {
                            $scope.newContainer.COO = data.COO;                            
                        }

                        if(data.sanitization_verification_id) {                            
                            //$scope.newContainer.SanitizationVerificationID = data.sanitization_verification_id;
                            $window.document.getElementById('sanitization_seal_id').select();
                        } else {
                            $window.document.getElementById('scan_for_save').focus();
                        }                   
                    } else {
                        $scope.newContainer.AssetScanID = '';
                        $scope.newContainer.UniversalModelNumber = '';
                        $scope.newContainer.COOID = '';
                        $scope.newContainer.COO = '';
                        $scope.newContainer.SanitizationVerificationID = 'n/a';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.ValidateInventorySerialNumber = function (SerialNumber) {
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateInventorySerialNumberForShipmentContainer&SerialNumber=' + SerialNumber + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        if (data.UniversalModelNumber) {
                            $scope.newContainer.InventoryUniversalModelNumber = data.UniversalModelNumber;
                            $window.document.getElementById('scan_for_subcomponentsave').focus();
                            $scope.GetCurrentTime($scope.newContainer,'inventory_mpn_scan_time');
                        }
                    } else {
                        $scope.newContainer.InventoryUniversalModelNumber = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.ValidateServerSerialNumber = function (SerialNumber) {
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateServerSerialNumberForShipmentContainer&SerialNumber=' + SerialNumber + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID+'&ByPassContainerMatching='+$scope.newContainer.ByPassContainerMatching,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {                        
                        if (data.ServerID) {
                            $scope.newContainer.ServerID = data.ServerID;
                            $scope.newContainer.Type = data.Type;   
                            //$window.document.getElementById('scan_for_save_server').focus();                         
                            if(data.Type == 'Server') {
                                setTimeout(function () {
                                    //$window.document.getElementById('ServerSanitizationVerificationID').focus();
                                    $window.document.getElementById('ServerSanitizationVerificationID').select();
                                }, 100); 
                            } else {
                                $window.document.getElementById('scan_for_save_server').focus();
                            }
                        }
                        if (data.MPN) {
                            $scope.newContainer.MPN = data.MPN;
                            $scope.GetCurrentTime($scope.newContainer,'server_mpn_scan_time')
                            //$window.document.getElementById('scan_for_save_server').focus();
                        }
                        if(data.MediaRecovery_VerificationID) {
                            $scope.newContainer.ServerSanitizationVerificationID = data.MediaRecovery_VerificationID;
                        } else {
                            $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
                        }
                    } else {
                        $scope.newContainer.ServerID = '';
                        $scope.newContainer.MPN = '';
                        $scope.newContainer.Type = '';
                        $scope.newContainer.ServerSanitizationVerificationID = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };


        $scope.AddSerialToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {
                $scope.newContainer.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=AddSerialToShipmentContainer&SerialNumber=' + $scope.newContainer.SerialNumber + '&Notes=' + $scope.newContainer.Notes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newContainer.SanitizationVerificationID + '&AssetScanID=' + $scope.newContainer.AssetScanID + '&UniversalModelNumber=' + $scope.newContainer.UniversalModelNumber+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID,
                    data: 'ajax=AddSerialToShipmentContainer&SerialNumber=' + $scope.newContainer.SerialNumber + '&Notes=' + $scope.newContainer.Notes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newContainer.SanitizationVerificationID + '&sanitization_seal_id='+$scope.newContainer.sanitization_seal_id+'&AssetScanID=' + $scope.newContainer.AssetScanID + '&'+$.param({UniversalModelNumber : $scope.newContainer.UniversalModelNumber})+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&serial_scan_time='+$scope.newContainer.serial_scan_time+'&mpn_scan_time='+$scope.newContainer.mpn_scan_time+'&COOID='+$scope.newContainer.COOID+'&COO='+$scope.newContainer.COO,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer.SerialNumber = '';
                            $scope.newContainer.AssetScanID = '';
                            $scope.newContainer.UniversalModelNumber = '';
                            $scope.newContainer.COOID = '';
                            $scope.newContainer.COO = '';
                            $scope.newContainer.Notes = 'n/a';
                            $scope.newContainer.SanitizationVerificationID = 'n/a';   
                            $scope.newContainer.sanitization_seal_id = 'n/a';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('SerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }

        };



        $scope.AddInventorySerialToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {
                $scope.newContainer.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    //data: 'ajax=AddInventorySerialToShipmentContainer&SerialNumber=' + $scope.newContainer.InventorySerialNumber + '&Notes=' + $scope.newContainer.InventoryNotes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&UniversalModelNumber=' + $scope.newContainer.InventoryUniversalModelNumber+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID,
                    data: 'ajax=AddInventorySerialToShipmentContainer&SerialNumber=' + $scope.newContainer.InventorySerialNumber + '&Notes=' + $scope.newContainer.InventoryNotes + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&'+$.param({UniversalModelNumber : $scope.newContainer.InventoryUniversalModelNumber})+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&serial_scan_time='+$scope.newContainer.inventory_serial_scan_time+'&mpn_scan_time='+$scope.newContainer.inventory_mpn_scan_time,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer.InventorySerialNumber = '';
                            $scope.newContainer.InventoryUniversalModelNumber = '';
                            $scope.newContainer.InventoryNotes = 'n/a';                            
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('InventorySerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }

        };


        $scope.AddServerSerialToContainer = function () {
            $scope.newContainer.busy = true;

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=AddServerSerialToShipmentContainer&SerialNumber=' + $scope.newContainer.SerialNumberServer + '&Notes=' + $scope.newContainer.NotesServer + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&SanitizationVerificationID=' + $scope.newContainer.SanitizationVerificationID + '&ServerID=' + $scope.newContainer.ServerID + '&UniversalModelNumber=' + $scope.newContainer.MPN+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&Type='+$scope.newContainer.Type+'&ServerSanitizationVerificationID='+$scope.newContainer.ServerSanitizationVerificationID+'&ByPassContainerMatching='+$scope.newContainer.ByPassContainerMatching+'&serial_scan_time='+$scope.newContainer.server_serial_scan_time+'&mpn_scan_time='+$scope.newContainer.server_mpn_scan_time,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );            
                        $scope.newContainer.SerialNumberServer = '';
                        $scope.newContainer.ServerID = '';
                        $scope.newContainer.MPN = '';
                        $scope.newContainer.Type = '';
                        $scope.newContainer.NotesServer = 'n/a';
                        $scope.newContainer.ServerSanitizationVerificationID = 'n/a';
                        $scope.CallServerFunction(0);
                        $window.document.getElementById('SerialNumberServer').focus();      
                        $scope.newContainer.ByPassContainerMatching = '0';                   
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.newContainer.ByPassContainerMatching = '0'; 
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });

        };


        $scope.AddByProductToContainer = function () {
            //if($scope.newContainer.PasswordVerified) {
            if(true) {
                $scope.newContainer.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=AddByProductToContainer&byproduct_id=' + $scope.newContainer.byproduct_id + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );            
                            $scope.newContainer.byproduct_id = '';                        
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };



        $scope.DeleteSerialFromContainer = function (serial, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteSerialFromShipmentContainer&' + $.param(serial) + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.ContainerSerials = [];
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };


        $scope.DeleteByProductFromContainer = function (serial, ev) {
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Delete ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Delete')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=DeleteByProductFromShipmentContainer&' + $.param(serial) + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.ContainerSerials = [];
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });
        };



        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function () {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if (start > $scope.pageCount() - rangeSize) {
                start = $scope.pageCount() - rangeSize;
            }
            for (var i = start; i < start + rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function () {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function () {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function () {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function () {
            $scope.currentPage = $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function () {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function () {
            return Math.ceil($scope.total / $scope.itemsPerPage);
        };
        $scope.setPage = function (n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.Scanned_Servers = 0;
        $scope.CallServerFunction = function (newValue) {
            if ($scope.newContainer.CreatedBy > 0) {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetContainerSerials&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&limit=' + $scope.itemsPerPage + '&skip=' + newValue * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                    success: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.ContainerSerials = data.Result;
                            if (data.total) {
                                $scope.total = data.total;
                            }
                            if (data.Scanned_Servers) {
                                $scope.Scanned_Servers = data.Scanned_Servers;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-info md-block')
                            );

                            if (data.Scanned_Servers) {
                                $scope.Scanned_Servers = data.Scanned_Servers;
                            }
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $scope.ContainerSerials = [];
            }
        };
        $scope.$watch("currentPage", function (newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for (var i = 0; i < multiarray.length; i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if ($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetContainerSerials&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&limit=' + $scope.itemsPerPage + '&skip=' + $scope.currentPage * $scope.itemsPerPage + '&OrderBy=' + $scope.OrderBy + '&OrderByType=' + $scope.OrderByType + '&' + $.param($scope.convertSingle($scope.filter_text)),
                success: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.ContainerSerials = data.Result;
                        if (data.total) {
                            $scope.total = data.total;
                        }
                        if (data.Scanned_Servers) {
                            $scope.Scanned_Servers = data.Scanned_Servers;
                        }
                    } else {
                        if (data.Scanned_Servers) {
                            $scope.Scanned_Servers = data.Scanned_Servers;
                        }
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-info md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();;
                }
            });
        };
        $scope.MakeFilter = function () {
            if ($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic


        $scope.ClearContainer = function () {
            var FacilityID = $scope.newContainer.FacilityID;
            $scope.newContainer = {
                FacilityID: FacilityID 
            };  
            window.location = '#!/ShipmentContainer';
        };

        $scope.AutoNavigateInventorySave = function () {
            $window.document.getElementById('scan_for_save').focus();
        }

        $scope.AutoNavigateSubInventorySave = function () {
            $window.document.getElementById('scan_for_subcomponentsave').focus();
        }

        $scope.GetExactMPN = function (MPN) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTrimmedMPN&MPN=' + MPN,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.newContainer.UniversalModelNumber = data.ExactMPN;    
                        $scope.AutoNavigateInventorySave();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };

        $scope.GetExactMPNSN = function (MPN) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetTrimmedMPN&MPN=' + MPN,
                success: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $scope.newContainer.InventoryUniversalModelNumber = data.ExactMPN;    
                        $scope.AutoNavigateSubInventorySave();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
        };

        //Start Reuse Media 

        $scope.ValidateMediaSerialNumber = function (MediaSerialNumber) {

            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateMediaSerialNumberForShipmentContainer&SerialNumber=' + MediaSerialNumber + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID,                
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {                        
                        if (data.MediaID) {
                            $scope.newContainer.MediaID = data.MediaID;
                        }
                        if (data.UniversalModelNumber) {
                            $scope.newContainer.ReuseMediaUniversalModelNumber = data.UniversalModelNumber;
                            $scope.GetCurrentTime($scope.newContainer,'media_mpn_scan_time');
                            //$window.document.getElementById('scan_for_save').focus();
                        }
                        if(data.MediaType) {
                            $scope.newContainer.ReuseMedia_part_type = data.MediaType
                        }
                    } else {
                        $scope.newContainer.MediaID = '';
                        $scope.newContainer.ReuseMediaUniversalModelNumber = '';
                        $scope.newContainer.ReuseMedia_part_type = '';
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });


        };


        $scope.AddMediaToContainer = function () {
            if($scope.newContainer.PasswordVerified) {
                $scope.newContainer.busy = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',                    
                    data: 'ajax=AddMediaToShipmentContainer&MediaSerialNumber=' + $scope.newContainer.MediaSerialNumber + '&ShippingContainerID=' + $scope.newContainer.ShippingContainerID + '&'+$.param({UniversalModelNumber : $scope.newContainer.ReuseMediaUniversalModelNumber})+'&ControllerLoginID='+$scope.newContainer.ShippingControllerLoginID+'&serial_scan_time='+$scope.newContainer.media_serial_scan_time+'&mpn_scan_time='+$scope.newContainer.media_mpn_scan_time,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.newContainer.MediaSerialNumber = '';
                            $scope.newContainer.ReuseMediaUniversalModelNumber = '';
                            $scope.newContainer.ReuseMedia_part_type = '';
                            $scope.CallServerFunction(0);
                            $window.document.getElementById('MediaSerialNumber').focus();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
            } else {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Controller Login ID is not Verified')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }

        };

        //End Reuse Media




        function CloseContainerTPVRController($scope,$mdDialog,CurrentContainer,$mdToast,$window) {
            $scope.CurrentContainer = CurrentContainer;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };   
            
            $scope.FocusNextField = function (nextid,wait) {            
                if(wait == '1') {
                    setTimeout(function () {
                        $window.document.getElementById(nextid).focus();
                    }, 100);
                } else {
                    $window.document.getElementById(nextid).focus();
                }
            };
        }

        $scope.CurrentContainer = {};
        $scope.confirmDetails = {};
        function afterShowAnimation1 () {            
            $window.document.getElementById("AuditController").focus();            
        }

        $scope.CloseContainer = function (container, ev) {

            $mdDialog.show({
                controller: CloseContainerTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation1,
                clickOutsideToClose:true,
                resolve: {
                    CurrentContainer: function () {
                    return container;
                    }
                }
            })
            .then(function(confirmDetails) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CloseShipmentContainer&' + $.param(container) + '&'+$.param(confirmDetails),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );                            
                            $scope.GetShippingDetails(container.ShippingContainerID,'new');
                            //location.reload();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });
                
                
            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });

        };


        $scope.ReopenContainer = function (container, ev) {
            
            var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Reopen the container?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Reopen')
                .cancel('Cancel');
            $mdDialog.show(confirm).then(function () {
                $scope.CurrentShipment = {};
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ReopenContainer1&'+$.param(container),
                    success: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            $scope.SearchContainer = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.GetShippingDetails(container.ShippingContainerID,'new');
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            }, function () {

            });

        };



        $scope.ValidateSanitizationSealID = function (sanitization_seal_id,SerialNumber,AssetScanID) {
            
            $scope.newContainer.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ValidateSanitizationSealIDforShipmentContainer&SerialNumber=' + SerialNumber + '&AssetScanID=' + AssetScanID +'&sanitization_seal_id='+sanitization_seal_id,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(5)
                                .toastClass('md-toast-success md-block')
                        );  
                        if(data.sanitization_verification_id) {                            
                            $scope.newContainer.SanitizationVerificationID = data.sanitization_verification_id;                            
                        }   
                        $scope.AutoNavigateInventorySave();                    
                    } else {                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });
            
        };


        $scope.canServerCountbeShown = function () {
            if(!$scope.newContainer.disposition_id || $scope.newContainer.disposition_id == '') {
                return false;
            } else {
                if($scope.Dispositions.length > 0) {
                    var matching_disposition = false;
                    for(var i=0;i<$scope.Dispositions.length;i++) {
                        if(($scope.Dispositions[i].disposition_id == $scope.newContainer.disposition_id) && $scope.Dispositions[i].disposition == 'Terminal-ServerRecycleProcessed') {
                            matching_disposition = true;
                            break;
                        }
                    }
                    return matching_disposition;
                } else {
                    return false;
                }                
            }
        };

        $scope.ScannedServerCount = function () {
            if($scope.ContainerSerials.length > 0) {
                return $scope.Scanned_Servers;
                // var count = 0;
                // for(var i=0;i<$scope.ContainerSerials.length;i++) {
                //     if($scope.ContainerSerials[i].ServerSerialNumber != '' && $scope.ContainerSerials[i].part_type == 'Server') {
                //         count = count + 1;
                //     }
                // }
                // return count;
            } else {
                return 0;
            }
        };

        $scope.GetReferenceTypeDetails = function () {
            if($scope.newContainer.FacilityID > 0 && $scope.newContainer.disposition_id > 0) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetReferenceTypeDetails&FacilityID=' + $scope.newContainer.FacilityID + '&disposition_id=' + $scope.newContainer.disposition_id,
                    success: function (data) {
                        $scope.newContainer.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if (data.Success) {
                            if(data.Result) {
                                var reference_type = data.Result;
                                if(reference_type.ReferenceIDRequired == '1') {
                                    $scope.newContainer.ReferenceIDRequired = '1';
                                }
                                $scope.newContainer.ReferenceTypeID = reference_type.ReferenceTypeID;
                                if(reference_type.ReferenceID) {
                                    $scope.newContainer.ReferenceID = reference_type.ReferenceID;
                                }
                                $scope.newContainer.ReferenceType = reference_type.ReferenceType;
                            }
                        } else {  
                            $scope.newContainer.ReferenceIDRequired = '0';     
                            $scope.newContainer.ReferenceTypeID = '';                 
                            $scope.newContainer.ReferenceID = '';
                            $scope.newContainer.ReferenceType = '';
                            
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();;
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();;
                    }
                });

            } else {
                $scope.newContainer.ReferenceTypeID ='';
                $scope.newContainer.ReferenceIDRequired ='0';
            }
        };


        $scope.GetReferenceType = function () {                            
            $rootScope.$broadcast('preloader:active');

            $scope.newContainer.ReferenceIDRequired = '0';                           
            $scope.newContainer.ReferenceID = '';
            $scope.newContainer.ReferenceType = '';

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReferenceType&ReferenceTypeID=' + $scope.newContainer.ReferenceTypeID,
                success: function (data) {
                    $scope.newContainer.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if (data.Success) {
                        if(data.Result) {
                            var reference_type = data.Result;
                            if(reference_type.ReferenceIDRequired == '1') {
                                $scope.newContainer.ReferenceIDRequired = '1';
                            }
                            $scope.newContainer.ReferenceTypeID = reference_type.ReferenceTypeID;
                            if(reference_type.ReferenceID) {
                                $scope.newContainer.ReferenceID = reference_type.ReferenceID;
                            }
                            $scope.newContainer.ReferenceType = reference_type.ReferenceType;
                        }
                    } else {  
                        $scope.newContainer.ReferenceIDRequired = '0';     
                        //$scope.newContainer.ReferenceTypeID = '';                 
                        $scope.newContainer.ReferenceID = '';
                        $scope.newContainer.ReferenceType = '';
                        
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });           
        };

    });
    //End Shipment Container End


    module.controller("reference_type", function ($scope, $http, $rootScope, $mdToast, $mdDialog, $stateParams, $upload, $location, $window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Reference Type',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });


        $scope.referencetype = {};

        $scope.ReferenceIDClicked = function () {
            if($scope.referencetype.ReferenceIDRequired == '1') {
                $scope.referencetype.ReferenceID = '';
            }
        };

        $rootScope.$broadcast('preloader:active');

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositions',
            success: function (data) {
                if (data.Success) {
                    $scope.Dispositions = data.Result;
                } else {
                    $scope.Dispositions = [];
                }                
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        jQuery.ajax({
            url: host + 'shipping/includes/shipping_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilities',
            success: function (data) {
                if (data.Success) {
                    $scope.Facilities = data.Result;
                } else {
                    $scope.Facilities = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();;
            }
        });

        $scope.DispositionDropdownChanged = function () {                        
            if ($scope.referencetype.disposition_id_list.slice(-1)[0] === "ALL" && $scope.referencetype.disposition_id_list) {
                $scope.referencetype.disposition_id_list = ['ALL'];
            } else {
                $scope.referencetype.disposition_id_list = $scope.referencetype.disposition_id_list.filter(function(value) {
                    return value !== "ALL";
                });
            }
        };

        $scope.FacilityDropdownChanged = function () {                        
            if ($scope.referencetype.FacilityIDList.slice(-1)[0] === "ALL" && $scope.referencetype.FacilityIDList) {
                $scope.referencetype.FacilityIDList = ['ALL'];
            } else {
                $scope.referencetype.FacilityIDList = $scope.referencetype.FacilityIDList.filter(function(value) {
                    return value !== "ALL";
                });
            }
        };
        
        $scope.ManageReferenceType = function () {
            $rootScope.$broadcast('preloader:active');
            $scope.referencetype.busy = true;
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=ManageReferenceType&'+$.param($scope.referencetype),
                success: function (data) {
                    if (data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        ); 
                        $scope.referencetype = {};
                        $scope.CallServerFunction1(0);
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );                        
                    }
                    $scope.referencetype.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }, error: function (data) {
                    $scope.referencetype.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();;
                }
            });

        };

        $scope.EditReferenceType = function (referencetype) {
            $scope.referencetype = referencetype;
            $scope.referencetype.ReferenceIDRequired = Number($scope.referencetype.ReferenceIDRequired);
            $scope.referencetype.FacilityIDList = $scope.referencetype.FacilityIDList.split(",");
            $scope.referencetype.disposition_id_list = $scope.referencetype.disposition_id_list.split(",");
            // jQuery.ajax({
            //     url: host + 'shipping/includes/shipping_submit.php',
            //     dataType: 'json',
            //     type: 'post',
            //     data: 'ajax=GetEditReferenceTypeDetails&ReferenceTypeID=' + referencetype.ReferenceTypeID,
            //     success: function (data) {
            //         if (data.Success == true) {
            //             $scope.referencetype = data.Result;
            //             $scope.GetDispositions();
            //             $scope.GetFacilities();
            //         } else {
            //             $mdToast.show(
            //                 $mdToast.simple()
            //                     .content(data.Result)
            //                     .action('OK')
            //                     .position('right')
            //                     .hideDelay(0)
            //                     .toastClass('md-toast-danger md-block')
            //             );
            //             $scope.referencetype = [];
            //         }
            //         $scope.$apply();
            //     }, error: function (data) {
            //         $scope.$apply();
            //     }
            // });
        }

         $scope.busy = false;
        $scope.sortconfigurationList = [];
        $scope.pagedItems = [];
        //Start Pagination Logic
        //$scope.itemsPerPage = 20;

        // Pagination variables for Table 1
        $scope.currentPage1 = 0;
        $scope.itemsPerPage1 = 10;
        //$scope.totalPages1 = Math.ceil($scope.table1Data.length / $scope.itemsPerPage1);

        // Pagination variables for Table 2
        $scope.currentPage2 = 0;
        $scope.itemsPerPage2 = 10;
        //$scope.totalPages2 = Math.ceil($scope.table2Data.length / $scope.itemsPerPage2);

        //$scope.currentPage = 0;
        $scope.OrderBy1 = '';
        $scope.OrderByType1 = '';
        $scope.filter_text1 = [{}];

        $scope.OrderBy2 = '';
        $scope.OrderByType2 = '';
        $scope.filter_text2 = [{}];

        $scope.range = function(table) {
        if(table == 1){
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage1;
            if ( start > $scope.pageCount(table)-rangeSize ) {
                start = $scope.pageCount(table)-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        }
        if(table == 2){
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage2;
            if ( start > $scope.pageCount(table)-rangeSize ) {
                start = $scope.pageCount(table)-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        }
        };
        $scope.prevPage = function(table) {
            /*if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }*/
            //console.log('table = '+table);
            //console.log('currentPage1 = '+$scope.currentPage1);
            if (table == 1 && $scope.currentPage1 > 0) $scope.currentPage1--;
            if (table == 2 && $scope.currentPage2 > 0) $scope.currentPage2--;
            //console.log('currentPage1 = '+$scope.currentPage1);
        };
        $scope.firstPage = function (table) {
            if(table == 1) $scope.currentPage1 = 0;
            if(table == 2) $scope.currentPage2 = 0;
        };
        $scope.prevPageDisabled = function(table) {
        if(table == 1){
            return $scope.currentPage1 === 0 ? "disabled" : "";
        }
        if(table == 2){
            return $scope.currentPage2 === 0 ? "disabled" : "";
        }
        };
        $scope.nextPage = function(table) {
            /*if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }*/
            if (table == 1 && $scope.currentPage1 < $scope.pageCount(table)) $scope.currentPage1++;
            if (table == 2 && $scope.currentPage2 < $scope.pageCount(table)) $scope.currentPage2++;
        };
        $scope.lastPage = function(table) {
        if(table == 1)  $scope.currentPage1 =  $scope.pageCount(table) - 1;
        if(table == 2)  $scope.currentPage2 =  $scope.pageCount(table) - 1;
        };
        $scope.nextPageDisabled = function(table) {
        if(table == 1){
            return $scope.currentPage1 === $scope.pageCount(table) - 1 ? "disabled" : "";
        }
        if(table == 2){
            return $scope.currentPage2 === $scope.pageCount(table) - 1 ? "disabled" : "";
        }
        };
        $scope.pageCount = function(table) {
        if(table == 1){
            return Math.ceil($scope.total1/$scope.itemsPerPage1);
        }
        if(table == 2){
            return Math.ceil($scope.total2/$scope.itemsPerPage2);
        }
        };

        $scope.setPage = function(n,table) {
            if (n >= 0 && n < $scope.pageCount(table)) {
            if (table == 1) $scope.currentPage1 = n;
            if (table == 2) $scope.currentPage2 = n;
                //$scope.currentPage = n;
            }
        };
        $scope.CallServerFunction1 = function (newValue) {
            //if($scope.CurrentStatus != '' )  {
            //console.log('in call server2 = '+newValue);
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetReferenceTypeList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=1'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems1 = data.Result;
                            if(data.total) {
                                $scope.total1 = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            //}
        };

        $scope.CallServerFunction2 = function (newValue) {
            //if($scope.CurrentStatus != '' )  {
            //console.log('in call server2 = '+newValue);
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'shipping/includes/shipping_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetReferenceTypeList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=0'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.pagedItems2 = data.Result;
                            if(data.total) {
                                $scope.total2 = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            //}
        };

        $scope.$watch("currentPage1", function(newValue, oldValue) {
            //console.log('newvalue, oldValue = '+ newValue +','+ oldValue);
            $scope.CallServerFunction1(newValue);
        });
        $scope.$watch("currentPage2", function(newValue, oldValue) {
            $scope.CallServerFunction2(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy1 = function (orderby) {
            $scope.OrderBy1 = orderby;
            if($scope.OrderByType1 == 'asc') {
                $scope.OrderByType1 = 'desc';
            } else {
                $scope.OrderByType1 = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReferenceTypeList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=1'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems1 = data.Result;
                        if(data.total) {
                            $scope.total1 = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.MakeOrderBy2 = function (orderby) {
            $scope.OrderBy2 = orderby;
            if($scope.OrderByType2 == 'asc') {
                $scope.OrderByType2 = 'desc';
            } else {
                $scope.OrderByType2 = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetReferenceTypeList&limit='+$scope.itemsPerPage1+'&skip='+$scope.currentPage1*$scope.itemsPerPage1+'&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType1+'&Status=0'+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.pagedItems2 = data.Result;
                        if(data.total) {
                            $scope.total2 = data.total;
                        }
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.MakeFilter1 = function () {
            if($scope.currentPage1 == 0) {
                $scope.CallServerFunction1($scope.currentPage1);
            } else {
                $scope.currentPage1 = 0;
            }
        };

        $scope.MakeFilter2 = function () {
            if($scope.currentPage2 == 0) {
                $scope.CallServerFunction2($scope.currentPage2);
            } else {
                $scope.currentPage2 = 0;
            }
        };

        $scope.ReferenceTypeListxls = function () {
        //alert("1");
            jQuery.ajax({
                url: host + 'shipping/includes/shipping_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateReferenceTypeListxls&OrderBy='+$scope.OrderBy1+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text1)),
                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        //console.log(data.Result);
                        window.location="templates/ReferenceTypeListxls.php";
                    } else {
                       // alert("4");
                        yaaaService.addAlert('',data.Result,5,'danger','dir1');
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.CancelConfiguration = function () {
            location.reload();
        };

    });

})();
