-- =====================================================
-- Script: Convert Shipping Containers to Bins (CustomPallet)
-- Purpose: Migrate existing shipping_containers to custompallet table
-- Date: 2024-01-XX
-- =====================================================

-- Start transaction to ensure data integrity
START TRANSACTION;
SET SQL_SAFE_UPDATES = 0;
-- =====================================================
-- STEP 1: Create temporary table to track conversions
-- =====================================================
CREATE TEMPORARY TABLE temp_container_conversion (
    ShippingContainerID VARCHAR(50),
    NewCustomPalletID INT,
    ConversionDate DATETIME DEFAULT NOW()
);

-- =====================================================
-- STEP 2: Convert shipping_containers to custompallet
-- =====================================================
INSERT INTO custompallet (
    FacilityID,
    LocationID,
    Description,
    MaximumAssets,
    AssetsCount,
    CreatedDate,
    CreatedBy,
    LastModifiedDate,
    LastModifiedBy,
    StatusID,
    StatusModifiedDate,
    StatusModifiedBy,
    disposition_id,
    BinName,
    SealID,
    ShippingControllerLoginID,
    ContainerWeight,
    RecentSealDate,
    RecentSealBy,
    ReferenceID,
    ReferenceTypeID,
    ReferenceType,
    ReferenceIDRequired,
    idPackage,
    ShippingID,
    bin_added_to_shipment_time,
    ASNContainer,
    idPallet,
    BatchRecovery,
    ConvertedFromShippingContainer
)
SELECT
    sc.FacilityID,
    sc.LocationID,
    CONCAT('Converted from Container: ', sc.ShippingContainerID,
           CASE WHEN sc.ContainerNotes IS NOT NULL THEN CONCAT(' - ', sc.ContainerNotes) ELSE '' END) as Description,
    NULL as MaximumAssets, -- Will be set based on package type if needed
    (SELECT COUNT(*) FROM shipping_container_serials scs WHERE scs.ShippingContainerID = sc.ShippingContainerID) as AssetsCount,
    sc.CreatedDate,
    sc.CreatedBy,
    sc.UpdatedDate,
    sc.UpdatedBy,
    CASE
        WHEN sc.StatusID = 1 AND sc.ShippingID IS NOT NULL THEN 6  -- Active with ShippingID -> Added to Shipment
        WHEN sc.StatusID = 1 THEN 1  -- Active -> Active
        WHEN sc.StatusID = 2 THEN 1  -- Approved -> Active (no direct equivalent)
        WHEN sc.StatusID = 3 THEN 7  -- Shipped -> Shipped
        WHEN sc.StatusID = 4 THEN 1  -- Received -> Active (no direct equivalent)
        WHEN sc.StatusID = 5 THEN 2  -- Declined -> InActive
        WHEN sc.StatusID = 6 THEN 3  -- Closed -> Closed
        WHEN sc.StatusID = 7 THEN 4  -- Quarantine -> AuditLocked (closest equivalent)
        ELSE 1 -- Default to Active
    END as StatusID,
    sc.UpdatedDate as StatusModifiedDate,
    sc.UpdatedBy as StatusModifiedBy,
    CASE
        WHEN sc.disposition_id IS NOT NULL AND EXISTS (SELECT 1 FROM disposition d WHERE d.disposition_id = sc.disposition_id)
        THEN sc.disposition_id
        ELSE NULL
    END as disposition_id,
    sc.ShippingContainerID as BinName, -- Use ShippingContainerID as BinName
    sc.SealID,
    sc.ShippingControllerLoginID,
    sc.ContainerWeight,
    sc.RecentSealDate,
    sc.RecentSealBy,
    sc.ReferenceID,
    sc.ReferenceTypeID,
    sc.ReferenceType,
    sc.ReferenceIDRequired,
    sc.idPackage,
    sc.ShippingID,
    sc.container_added_to_shipment_time as bin_added_to_shipment_time,
    sc.ASNContainer,
    sc.idPallet,
    sc.BatchRecovery,
    1 as ConvertedFromShippingContainer -- Mark as converted from shipping container
FROM shipping_containers sc
WHERE NOT EXISTS (
    SELECT 1 FROM custompallet cp WHERE cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
); -- Avoid duplicates if script is run multiple times

-- =====================================================
-- STEP 3: Store conversion mapping for reference
-- =====================================================
INSERT INTO temp_container_conversion (ShippingContainerID, NewCustomPalletID)
SELECT sc.ShippingContainerID, cp.CustomPalletID
FROM shipping_containers sc
JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
WHERE cp.CreatedDate >= (SELECT MIN(CreatedDate) FROM custompallet WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers));

-- =====================================================
-- STEP 4: Create custompallet_items from shipping_container_serials
-- =====================================================
INSERT INTO custompallet_items (
    CustomPalletID,
    AssetScanID,
    DateCreated,
    CreatedBy,
    status,
    ServerID,
    MediaID,
    Quantity
)
SELECT DISTINCT
    cp.CustomPalletID,
    scs.AssetScanID,
    scs.CreatedDate as DateCreated,
    scs.CreatedBy,
    scs.StatusID as status,
    scs.ServerID,
    scs.MediaID,
    scs.Quantity
FROM shipping_container_serials scs
JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
WHERE scs.AssetScanID IS NOT NULL
   OR scs.ServerID IS NOT NULL
   OR scs.MediaID IS NOT NULL;

-- =====================================================
-- STEP 5: Update shipping_container_serials with new CustomPalletID and BinName
-- =====================================================
UPDATE shipping_container_serials scs
JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
SET
    scs.CustomPalletID = cp.CustomPalletID,
    scs.BinName = cp.BinName,
    scs.UpdatedDate = NOW(),
    scs.UpdatedBy = (SELECT MIN(UserId) FROM users WHERE UserId > 0 LIMIT 1)
WHERE scs.ShippingContainerID IS NOT NULL;

-- =====================================================
-- STEP 6: Add tracking records for all converted containers
-- =====================================================
INSERT INTO custompallet_tracking (
    CustomPalletID,
    BinName,
    Action,
    CreatedDate,
    CreatedBy,
    ModuleName
)
SELECT
    cp.CustomPalletID,
    cp.BinName,
    CONCAT('Container converted to bin from shipping_containers table. Original Container ID: ',
           tcc.ShippingContainerID,
           '. Conversion completed on ',
           DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')) as Action,
    NOW() as CreatedDate,
    (SELECT MIN(UserId) FROM users WHERE UserId > 0 LIMIT 1) as CreatedBy,
    'Container to Bin Conversion' as ModuleName
FROM temp_container_conversion tcc
JOIN custompallet cp ON cp.CustomPalletID = tcc.NewCustomPalletID;

-- =====================================================
-- STEP 7: Update asset counts in custompallet table
-- =====================================================
UPDATE custompallet cp
SET AssetsCount = (
    SELECT COUNT(*)
    FROM custompallet_items cpi
    WHERE cpi.CustomPalletID = cp.CustomPalletID
)
WHERE cp.BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers);

-- =====================================================
-- STEP 8: Ensure ConvertedFromShippingContainer flag is set
-- =====================================================
UPDATE custompallet
SET ConvertedFromShippingContainer = 1,
    LastModifiedDate = NOW(),
    LastModifiedBy = (SELECT MIN(UserId) FROM users WHERE UserId > 0 LIMIT 1)
WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers)
  AND (ConvertedFromShippingContainer IS NULL OR ConvertedFromShippingContainer = 0);

-- =====================================================
-- STEP 9: Update location table for containers with LocationID
-- =====================================================
UPDATE location l
JOIN custompallet cp ON l.LocationID = cp.LocationID
SET l.currentItemType = 'BIN',
    l.currentItemID = cp.BinName,
    l.Locked = '1' -- Mark location as locked since it now contains a bin
WHERE cp.BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers)
  AND cp.LocationID IS NOT NULL
  AND cp.ConvertedFromShippingContainer = 1;

-- =====================================================
-- STEP 10: Generate conversion summary report
-- =====================================================
SELECT
    'CONVERSION SUMMARY' as Report_Type,
    COUNT(*) as Total_Containers_Converted,
    SUM(cp.AssetsCount) as Total_Items_Migrated,
    MIN(cp.CreatedDate) as First_Container_Date,
    MAX(cp.CreatedDate) as Last_Container_Date
FROM custompallet cp
WHERE cp.BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers);

-- Report on disposition handling
SELECT
    'DISPOSITION HANDLING' as Report_Type,
    COUNT(*) as Total_Containers,
    SUM(CASE WHEN sc.disposition_id IS NOT NULL THEN 1 ELSE 0 END) as Containers_With_Disposition,
    SUM(CASE WHEN sc.disposition_id IS NOT NULL AND EXISTS (SELECT 1 FROM disposition d WHERE d.disposition_id = sc.disposition_id) THEN 1 ELSE 0 END) as Valid_Dispositions,
    SUM(CASE WHEN sc.disposition_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM disposition d WHERE d.disposition_id = sc.disposition_id) THEN 1 ELSE 0 END) as Invalid_Dispositions_Set_To_NULL
FROM shipping_containers sc;

-- Status mapping summary with actual data
SELECT
    'STATUS MAPPING SUMMARY' as Report_Type,
    sc.StatusID as Original_StatusID,
    CASE sc.StatusID
        WHEN 1 THEN 'Active'
        WHEN 2 THEN 'Approved'
        WHEN 3 THEN 'Shipped'
        WHEN 4 THEN 'Received'
        WHEN 5 THEN 'Declined'
        WHEN 6 THEN 'Closed'
        WHEN 7 THEN 'Quarantine'
        ELSE 'Unknown'
    END as Original_Status,
    cp.StatusID as New_StatusID,
    CASE cp.StatusID
        WHEN 1 THEN 'Active'
        WHEN 2 THEN 'InActive'
        WHEN 3 THEN 'Closed'
        WHEN 4 THEN 'AuditLocked'
        WHEN 5 THEN 'Deleted'
        WHEN 6 THEN 'Added to Shipment'
        WHEN 7 THEN 'Shipped'
        ELSE 'Unknown'
    END as New_Status,
    CASE
        WHEN sc.StatusID = 1 AND sc.ShippingID IS NOT NULL THEN 'Active with ShippingID -> Added to Shipment'
        WHEN sc.StatusID = 1 THEN 'Active -> Active'
        WHEN sc.StatusID = 2 THEN 'Approved -> Active'
        WHEN sc.StatusID = 3 THEN 'Shipped -> Shipped'
        WHEN sc.StatusID = 4 THEN 'Received -> Active'
        WHEN sc.StatusID = 5 THEN 'Declined -> InActive'
        WHEN sc.StatusID = 6 THEN 'Closed -> Closed'
        WHEN sc.StatusID = 7 THEN 'Quarantine -> AuditLocked'
        ELSE 'Default -> Active'
    END as Mapping_Logic,
    COUNT(*) as Count
FROM shipping_containers sc
JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
WHERE cp.ConvertedFromShippingContainer = 1
GROUP BY sc.StatusID, cp.StatusID, sc.ShippingID IS NOT NULL
ORDER BY sc.StatusID;

-- =====================================================
-- STEP 11: Validation queries to verify conversion
-- =====================================================
SELECT 'VALIDATION: Container Count Check' as Check_Type,
       (SELECT COUNT(*) FROM shipping_containers) as Original_Containers,
       (SELECT COUNT(*) FROM custompallet WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers)) as Converted_Bins,
       CASE 
           WHEN (SELECT COUNT(*) FROM shipping_containers) = (SELECT COUNT(*) FROM custompallet WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers))
           THEN 'PASS' 
           ELSE 'FAIL' 
       END as Status;

SELECT 'VALIDATION: Serial Count Check' as Check_Type,
       (SELECT COUNT(*) FROM shipping_container_serials) as Original_Serials,
       (SELECT COUNT(*) FROM custompallet_items WHERE CustomPalletID IN (SELECT CustomPalletID FROM custompallet WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers))) as Converted_Items,
       CASE
           WHEN (SELECT COUNT(*) FROM shipping_container_serials WHERE AssetScanID IS NOT NULL OR ServerID IS NOT NULL OR MediaID IS NOT NULL) =
                (SELECT COUNT(*) FROM custompallet_items WHERE CustomPalletID IN (SELECT CustomPalletID FROM custompallet WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers)))
           THEN 'PASS'
           ELSE 'REVIEW_NEEDED'
       END as Status;

SELECT 'VALIDATION: Conversion Flag Check' as Check_Type,
       (SELECT COUNT(*) FROM shipping_containers) as Total_Original_Containers,
       (SELECT COUNT(*) FROM custompallet WHERE ConvertedFromShippingContainer = 1) as Bins_Marked_As_Converted,
       (SELECT COUNT(*) FROM custompallet WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers) AND ConvertedFromShippingContainer = 1) as Converted_Bins_With_Flag,
       CASE
           WHEN (SELECT COUNT(*) FROM shipping_containers) = (SELECT COUNT(*) FROM custompallet WHERE BinName COLLATE utf8mb3_unicode_ci IN (SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers) AND ConvertedFromShippingContainer = 1)
           THEN 'PASS'
           ELSE 'FAIL'
       END as Status;

SELECT 'VALIDATION: Location Update Check' as Check_Type,
       (SELECT COUNT(*) FROM shipping_containers WHERE LocationID IS NOT NULL) as Containers_With_Location,
       (SELECT COUNT(*) FROM location l
        JOIN custompallet cp ON l.LocationID = cp.LocationID
        WHERE cp.ConvertedFromShippingContainer = 1
          AND l.currentItemType = 'BIN'
          AND l.currentItemID = cp.BinName) as Locations_Updated_To_Bin,
       CASE
           WHEN (SELECT COUNT(*) FROM shipping_containers WHERE LocationID IS NOT NULL) =
                (SELECT COUNT(*) FROM location l
                 JOIN custompallet cp ON l.LocationID = cp.LocationID
                 WHERE cp.ConvertedFromShippingContainer = 1
                   AND l.currentItemType = 'BIN'
                   AND l.currentItemID = cp.BinName)
           THEN 'PASS'
           ELSE 'REVIEW_NEEDED'
       END as Status;

-- =====================================================
-- STEP 12: Clean up temporary table
-- =====================================================
DROP TEMPORARY TABLE temp_container_conversion;

-- Commit the transaction
COMMIT;

-- =====================================================
-- NOTES FOR MANUAL REVIEW:
-- =====================================================
/*
1. This script converts all shipping_containers to custompallet (bins)
2. ShippingContainerID becomes the BinName
3. Status mapping is applied according to business logic
4. All shipping_container_serials are migrated to custompallet_items
5. Tracking records are created for audit purposes
6. Validation queries help verify the conversion

IMPORTANT: 
- Review the validation results before considering the conversion complete
- Test this script on a backup/staging environment first
- Some manual adjustments may be needed based on specific business requirements
- Consider backing up the original tables before running this script

POST-CONVERSION STEPS:
1. Update any application code that references ShippingContainerID to use CustomPalletID
2. Update joins from shipping_containers to custompallet
3. Update joins from shipping_container_serials to custompallet_items
4. Consider archiving the original shipping_containers and shipping_container_serials tables
*/
