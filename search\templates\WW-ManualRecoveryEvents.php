<?php
set_time_limit(24000);
ini_set('post_max_size', '2000M');
ini_set('memory_limit', '2000M');
ini_set('max_execution_time', 30000);
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'ManualRecoveryEvents.'.$today.'.csv';

$csv = "entity_id,unique_id,recovery_type,operator_login_id,origin_operator_login_id,recovery_location_id,origin_workstation_id,origin_recovery_datetime,manual_recovery_event_datetime,manual_recovery_action\n";//Column headers
$sql = "Select distinct(A.SerialNumber) as unique_id,RT.Recoverytype as recovery_type,AU.UserName as operator_login_id,PAU.UserName as origin_operator_login_id,
		AF.FacilityName as recovery_location_id,ASS.SiteName as origin_workstation_id,A.DateCreated,A.PendingSaveDate,'Pending Save' as manual_recovery_action

        From asset A
        LEFT JOIN Recoverytype RT on RT.Recoverytypeid = A.RecoveryTypeID
		LEFT JOIN users AU on AU.UserId = A.CreatedBy
		LEFT JOIN pallets P on P.idPallet = A.idPallet
		LEFT JOIN users PAU on PAU.UserId = P.ReceivedBy
		LEFT JOIN facility AF on AF.FacilityID = A.FacilityID
		LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        where
		A.PendingSaveDate  Between '".$ProcessDatefrom."' and '".$ProcessDateto."' 
		AND A.PendingSaveBy >0
		group by A.SerialNumber";
		//AND (P.status=1 or P.status = 7)

$query = mysqli_query($connectionlink1,$sql);
if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1).$sql;
}
while($row = mysqli_fetch_assoc($query))
{
	if($row['DateCreated'] != '')
    {
        $row['DateCreated'] = date("Y-m-d H:i:s", strtotime($row['DateCreated']));
    }
    else
    {
        $row['DateCreated'] = '';
    }
	if($row['PendingSaveDate'] != '')
    {
        $row['PendingSaveDate'] = date("Y-m-d H:i:s", strtotime($row['PendingSaveDate']));
    }
    else
    {
        $row['PendingSaveDate'] = '';
    }
	$date1 = explode(" ",$row['DateCreated']);
	$date2 = explode("-",$date1[0]);
	$date = $date2[1]."/".$date2[2]."/".$date2[0];
	$time = date("g:i:s a", strtotime($row['DateCreated']));
	$date11 = explode(" ",$row['PendingSaveDate']);
	$date21 = explode("-",$date11[0]);
	$date22 = $date21[1]."/".$date21[2]."/".$date21[0];
	$time22 = date("g:i:s a", strtotime($row['PendingSaveDate']));

	$row['unique_id'] = str_replace(","," ",$row['unique_id']);
	$row['recovery_type'] = str_replace(","," ",$row['recovery_type']);
	$row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
	$row['origin_operator_login_id'] = str_replace(","," ",$row['origin_operator_login_id']);
	$row['recovery_location_id'] = str_replace(","," ",$row['recovery_location_id']);
	$row['origin_workstation_id'] = str_replace(","," ",$row['origin_workstation_id']);
    $row['manual_recovery_action'] = str_replace(","," ",$row['manual_recovery_action']);

	if($row['unique_id'] == '')
	{
		$row['unique_id'] = 'n/a';
	}
	if($row['recovery_type'] == '')
	{
		$row['recovery_type'] = 'n/a';
	}
	if($row['operator_login_id'] == '')
	{
		$row['operator_login_id'] = 'n/a';
	}
	if($row['origin_operator_login_id'] == '')
	{
		$row['origin_operator_login_id'] = 'n/a';
	}
	if($date == '')
	{
		$date = 'n/a';
	}
	if($time == '')
	{
		$time = 'n/a';
	}
	if($row['recovery_location_id'] == '')
	{
		$row['recovery_location_id'] = 'n/a';
	}
	if($row['origin_workstation_id'] == '')
	{
		$row['origin_workstation_id'] = 'n/a';
	}
	if($row['manual_recovery_action'] == '')
	{
		$row['manual_recovery_action'] = 'n/a';
	}
    if($date22 == '')
	{
		$date22 = 'n/a';
	}
	if($time22 == '')
	{
		$time22 = 'n/a';
	}
    $row2  = array('eV-Disposition-1',$row['unique_id'],$row['recovery_type'],$row['operator_login_id'],$row['origin_operator_login_id'],$row['recovery_location_id'],$row['origin_workstation_id'],$row['DateCreated'],$row['PendingSaveDate'],$row['manual_recovery_action']);
    $rows[] = $row2;
}

$sql1 = "Select distinct(A.MediaSerialNumber) as unique_id,'Assembly' as recovery_type,AU.UserName as operator_login_id,PAU.UserName as origin_operator_login_id,
		AF.FacilityName as recovery_location_id,ASS.SiteName as origin_workstation_id,A.CreatedDate,A.PendingSaveDate,'Pending Save' as manual_recovery_action

        From speed_media_recovery A
		LEFT JOIN users AU on AU.UserId = A.CreatedBy
		LEFT JOIN pallets P on P.idPallet = A.idPallet
		LEFT JOIN users PAU on PAU.UserId = P.ReceivedBy
		LEFT JOIN facility AF on AF.FacilityID = A.FacilityID
		LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        where
		A.PendingSaveDate  Between '".$ProcessDatefrom."' and '".$ProcessDateto."' 
		AND A.PendingSaveBy >0
		group by A.MediaSerialNumber";
		//AND (P.status=1 or P.status = 7)

$query1 = mysqli_query($connectionlink1,$sql1);
while($row1 = mysqli_fetch_assoc($query1))
{
	if($row1['DateCreated'] != '')
    {
        $row1['DateCreated'] = date("Y-m-d H:i:s", strtotime($row1['DateCreated']));
    }
    else
    {
        $row1['DateCreated'] = '';
    }
	if($row1['PendingSaveDate'] != '')
    {
        $row1['PendingSaveDate'] = date("Y-m-d H:i:s", strtotime($row1['PendingSaveDate']));
    }
    else
    {
        $row1['PendingSaveDate'] = '';
    }
	$date1 = explode(" ",$row1['DateCreated']);
	$date2 = explode("-",$date1[0]);
	$date = $date2[1]."/".$date2[2]."/".$date2[0];
	$time = date("g:i:s a", strtotime($row1['DateCreated']));
	$date11 = explode(" ",$row1['PendingSaveDate']);
	$date21 = explode("-",$date11[0]);
	$date22 = $date21[1]."/".$date21[2]."/".$date21[0];
	$time22 = date("g:i:s a", strtotime($row1['PendingSaveDate']));

	$row1['unique_id'] = str_replace(","," ",$row1['unique_id']);
	$row1['recovery_type'] = str_replace(","," ",$row1['recovery_type']);
	$row1['operator_login_id'] = str_replace(","," ",$row1['operator_login_id']);
	$row1['origin_operator_login_id'] = str_replace(","," ",$row1['origin_operator_login_id']);
	$row1['recovery_location_id'] = str_replace(","," ",$row1['recovery_location_id']);
	$row1['origin_workstation_id'] = str_replace(","," ",$row1['origin_workstation_id']);
    $row1['manual_recovery_action'] = str_replace(","," ",$row1['manual_recovery_action']);

	if($row1['unique_id'] == '')
	{
		$row1['unique_id'] = 'n/a';
	}
	if($row1['recovery_type'] == '')
	{
		$row1['recovery_type'] = 'n/a';
	}
	if($row1['operator_login_id'] == '')
	{
		$row1['operator_login_id'] = 'n/a';
	}
	if($row1['origin_operator_login_id'] == '')
	{
		$row1['origin_operator_login_id'] = 'n/a';
	}
	if($date == '')
	{
		$date = 'n/a';
	}
	if($time == '')
	{
		$time = 'n/a';
	}
	if($row1['recovery_location_id'] == '')
	{
		$row1['recovery_location_id'] = 'n/a';
	}
	if($row1['origin_workstation_id'] == '')
	{
		$row1['origin_workstation_id'] = 'n/a';
	}
	if($row1['manual_recovery_action'] == '')
	{
		$row1['manual_recovery_action'] = 'n/a';
	}
    if($date22 == '')
	{
		$date22 = 'n/a';
	}
	if($time22 == '')
	{
		$time22 = 'n/a';
	}
    $row12  = array('eV-Disposition-1',$row1['unique_id'],$row1['recovery_type'],$row1['operator_login_id'],$row1['origin_operator_login_id'],$row1['recovery_location_id'],$row1['origin_workstation_id'],$row1['DateCreated'],$row1['PendingSaveDate'],$row1['manual_recovery_action']);
    $rows[] = $row12;
}

$sql2 = "Select distinct(A.ServerSerialNumber) as unique_id,'Assembly' as recovery_type,AU.UserName as operator_login_id,PAU.UserName as origin_operator_login_id,
		AF.FacilityName as recovery_location_id,ASS.SiteName as origin_workstation_id,A.CreatedDate,A.PendingSaveDate,'Pending Save' as manual_recovery_action

        From speed_server_recovery A
		LEFT JOIN users AU on AU.UserId = A.CreatedBy
		LEFT JOIN pallets P on P.idPallet = A.idPallet
		LEFT JOIN users PAU on PAU.UserId = P.ReceivedBy
		LEFT JOIN facility AF on AF.FacilityID = P.PalletFacilityID
		LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
        where
		A.PendingSaveDate  Between '".$ProcessDatefrom."' and '".$ProcessDateto."' 
		AND A.PendingSaveBy >0
		group by A.ServerSerialNumber";
		//AND (P.status=1 or P.status = 7)

$query2 = mysqli_query($connectionlink1,$sql2);
while($row2 = mysqli_fetch_assoc($query2))
{
	if($row2['DateCreated'] != '')
    {
        $row2['DateCreated'] = date("Y-m-d H:i:s", strtotime($row2['DateCreated']));
    }
    else
    {
        $row2['DateCreated'] = '';
    }
	if($row2['PendingSaveDate'] != '')
    {
        $row2['PendingSaveDate'] = date("Y-m-d H:i:s", strtotime($row2['PendingSaveDate']));
    }
    else
    {
        $row2['PendingSaveDate'] = '';
    }
	$date1 = explode(" ",$row2['DateCreated']);
	$date2 = explode("-",$date1[0]);
	$date = $date2[1]."/".$date2[2]."/".$date2[0];
	$time = date("g:i:s a", strtotime($row2['DateCreated']));
	$date11 = explode(" ",$row2['PendingSaveDate']);
	$date21 = explode("-",$date11[0]);
	$date22 = $date21[1]."/".$date21[2]."/".$date21[0];
	$time22 = date("g:i:s a", strtotime($row2['PendingSaveDate']));

	$row2['unique_id'] = str_replace(","," ",$row2['unique_id']);
	$row2['recovery_type'] = str_replace(","," ",$row2['recovery_type']);
	$row2['operator_login_id'] = str_replace(","," ",$row2['operator_login_id']);
	$row2['origin_operator_login_id'] = str_replace(","," ",$row2['origin_operator_login_id']);
	$row2['recovery_location_id'] = str_replace(","," ",$row2['recovery_location_id']);
	$row2['origin_workstation_id'] = str_replace(","," ",$row2['origin_workstation_id']);
    $row2['manual_recovery_action'] = str_replace(","," ",$row2['manual_recovery_action']);

	if($row2['unique_id'] == '')
	{
		$row2['unique_id'] = 'n/a';
	}
	if($row2['recovery_type'] == '')
	{
		$row2['recovery_type'] = 'n/a';
	}
	if($row2['operator_login_id'] == '')
	{
		$row2['operator_login_id'] = 'n/a';
	}
	if($row2['origin_operator_login_id'] == '')
	{
		$row2['origin_operator_login_id'] = 'n/a';
	}
	if($date == '')
	{
		$date = 'n/a';
	}
	if($time == '')
	{
		$time = 'n/a';
	}
	if($row2['recovery_location_id'] == '')
	{
		$row2['recovery_location_id'] = 'n/a';
	}
	if($row2['origin_workstation_id'] == '')
	{
		$row2['origin_workstation_id'] = 'n/a';
	}
	if($row2['manual_recovery_action'] == '')
	{
		$row2['manual_recovery_action'] = 'n/a';
	}
    if($date22 == '')
	{
		$date22 = 'n/a';
	}
	if($time22 == '')
	{
		$time22 = 'n/a';
	}
    $row22  = array('eV-Disposition-1',$row2['unique_id'],$row2['recovery_type'],$row2['operator_login_id'],$row2['origin_operator_login_id'],$row2['recovery_location_id'],$row2['origin_workstation_id'],$row2['DateCreated'],$row2['PendingSaveDate'],$row2['manual_recovery_action']);
    $rows[] = $row22;
}

foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>