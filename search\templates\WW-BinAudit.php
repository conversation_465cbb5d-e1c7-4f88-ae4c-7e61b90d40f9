<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
//$ProcessDatefrom = '2024-04-12 00:00:00';
//$ProcessDateto = '2025-07-07 00:00:00';
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'BinAudit.'.$today.'.csv';
//$filname = 'BA.'.$today.'.csv';

$csv = "entity_id,assigner_login_id,operator_login_id,last_touch_login_id,audit_location_id,audit_type,audit_action,origin_bin_id,origin_bin_scan_time,origin_next_step_action,serial_id,serial_scan_time,origin_mpn_id,mpn_id,mpn_scan_time,part_type,audit_notes,audit_notes_scan_time,audit_result,audit_result_scan_time,valid_serial_flag,valid_mpn_flag,valid_next_step_action_flag,clean_audit_flag,bin_id,bin_scan_time,next_step_action,audit_date,audit_time,origin_coo_id,coo_id,coo_scan_time,valid_coo_flag,classification_type,classification_code_id,event_id,event_s_duration_value,customer_id\n";//Column headers

$sql = "Select distinct(BAR.SerialNumber) as serial_id,BARU.UserName as assigner_login_id,BARU.UserName as operator_login_id,BARCU.UserName as last_touch_login_id,
        BARUF.FacilityName as audit_location_id,BARAB.Type as audit_type,BAR.Damaged as audit_action,BAR.SourceBinName as origin_bin_id,
        BAR.origin_bin_scan_time as origin_bin_scan_time,BARD.disposition as origin_next_step_action,
        BAR.serial_scan_time as serial_scan_time,BAR.origin_mpn_id as origin_mpn_id,BAR.MPN as mpn_id,BAR.mpn_scan_time as mpn_scan_time,
        BAR.part_type as part_type,BAR.AuditNotes as audit_notes,BAR.audit_notes_scan_time as audit_notes_scan_time,BAR.EvaluationResult as audit_result,
        BAR.EvaluationResult_scan_time as audit_result_scan_time,BAR.valid_serial_flag as valid_serial_flag,
        BAR.valid_mpn_flag as valid_mpn_flag,NULL as valid_next_step_action_flag,BAR.clean_audit_flag as clean_audit_flag,BAR.NewBinName as bin_id,
        BAR.assigned_bin_scan_time as bin_scan_time,BAR.NewDispositionName as next_step_action,BARAB.AuditedDate,'' as origin_coo_id,'' as coo_id,
        '' as coo_scan_time,'' as valid_coo_flag,BARU.FacilityID,BAR.SourceDisposition,BAR.event_id as event_id,BAR.CreatedDate,AWSSC.Customer as customer_id
        FROM bin_audit_records BAR 
        LEFT JOIN users BARU ON BARU.UserId = BAR.CreatedBy
        LEFT JOIN facility BARUF ON BARUF.FacilityID = BARU.FacilityID
        LEFT JOIN bin_audit_controls BARC ON BARC.ControlID = BAR.ControlID
        LEFT JOIN users BARCU ON BARCU.UserId = BAR.last_touched_by
        LEFT JOIN custompallet BARCP ON BARCP.FacilityID = BAR.SourceBinID
        LEFT JOIN facility BARCPF ON BARCPF.FacilityID = BARCP.FacilityID
        LEFT JOIN bin_audit_bins BARAB ON BARAB.ID = BAR.BinAuditID
        LEFT JOIN disposition BARD ON BARD.disposition_id = BAR.SourceDisposition
        LEFT JOIN asset BARA ON BARA.AssetScanID = BAR.AssetScanID
        LEFT JOIN pallets P on P.idPallet = BARA.idPallet
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE BAR.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        Group by BAR.SerialNumber";

$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    $date1 = explode(" ",$row['AuditedDate']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    /*if($row['serial_scan_time'] != '')
    {
        $timeFirst  = strtotime($row['serial_scan_time']);
        $timeSecond = strtotime($row['CreatedDate']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    else
    {
        $differenceInSeconds = 0;
    }*/
    $timeFirst  = strtotime($row['origin_bin_scan_time']);
    $timeSecond = strtotime($row['AuditedDate']);
    $differenceInSeconds = $timeSecond - $timeFirst;
    $time = date("g:i:s A", strtotime($row['AuditedDate']));
    $row['assigner_login_id'] = strtolower($row['assigner_login_id']);
    $row['operator_login_id'] = strtolower($row['operator_login_id']);
    $row['last_touch_login_id'] = strtolower($row['last_touch_login_id']);
    $row['entity_id'] = str_replace(","," ",$row['entity_id']);
	$row['assigner_login_id'] = str_replace(","," ",$row['assigner_login_id']);
	$row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
	$row['last_touch_login_id'] = str_replace(","," ",$row['last_touch_login_id']);
    $row['audit_location_id'] = str_replace(","," ",$row['audit_location_id']);
    $row['audit_type'] = str_replace(","," ",$row['audit_type']);
    $row['audit_action'] = str_replace(","," ",$row['audit_action']);
    $row['origin_bin_id'] = str_replace(","," ",$row['origin_bin_id']);
    $row['origin_next_step_action'] = str_replace(","," ",$row['origin_next_step_action']);
    $row['serial_id'] = str_replace(","," ",$row['serial_id']);
    $row['origin_mpn_id'] = str_replace(","," ",$row['origin_mpn_id']);
    $row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
    $row['part_type'] = str_replace(","," ",$row['part_type']);
    $row['audit_notes'] = str_replace(","," ",$row['audit_notes']);
    $row['audit_result'] = str_replace(","," ",$row['audit_result']);
    $row['valid_serial_flag'] = str_replace(","," ",$row['valid_serial_flag']);
    $row['valid_mpn_flag'] = str_replace(","," ",$row['valid_mpn_flag']);
    $row['valid_next_step_action_flag'] = str_replace(","," ",$row['valid_next_step_action_flag']);
    $row['clean_audit_flag'] = str_replace(","," ",$row['clean_audit_flag']);
    $row['bin_id'] = str_replace(","," ",$row['bin_id']);
    $row['next_step_action'] = str_replace(","," ",$row['next_step_action']);
    $row['valid_next_step_action_flag'] = $row['valid_serial_flag'];
    if($row['audit_type'] == 'Schedule')
    {
        $row['assigner_login_id'] = 'admin';
    }
    if($row['entity_id'] == '')
    {
        $row['entity_id'] = 'n/a';
    }
    if($row['assigner_login_id'] == '')
    {
        $row['assigner_login_id'] = 'n/a';
    }
    if($row['operator_login_id'] == '')
    {
        $row['operator_login_id'] = 'n/a';
    }
    if($row['last_touch_login_id'] == '')
    {
        $row['last_touch_login_id'] = 'n/a';
    }
    if($row['audit_location_id'] == '')
    {
        $row['audit_location_id'] = 'n/a';
    }
    if($row['audit_type'] == '')
    {
        $row['audit_type'] = 'n/a';
    }
    if($row['audit_action'] == '')
    {
        $row['audit_action'] = 'n/a';
    }
    if($row['origin_bin_id'] == '')
    {
        $row['origin_bin_id'] = 'n/a';
    }
    if($row['origin_next_step_action'] == '')
    {
        $row['origin_next_step_action'] = 'n/a';
    }
    if($row['serial_id'] == '')
    {
        $row['serial_id'] = 'n/a';
    }
    if($row['origin_mpn_id'] == '')
    {
        $row['origin_mpn_id'] = 'n/a';
    }
    if($row['mpn_id'] == '')
    {
        $row['mpn_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = '';
    }
    if($time == '')
    {
        $time = '';
    }
    if($row['part_type'] == '')
    {
        $row['part_type'] = 'n/a';
    }
    if($row['audit_notes'] == '')
    {
        $row['audit_notes'] = 'n/a';
    }
    if($row['audit_result'] == '')
    {
        $row['audit_result'] = 'n/a';
    }
    if($row['valid_serial_flag'] == '')
    {
        $row['valid_serial_flag'] = '0';
    }
    if($row['valid_mpn_flag'] == '')
    {
        $row['valid_mpn_flag'] = '0';
    }
    if($row['valid_next_step_action_flag'] == '')
    {
        $row['valid_next_step_action_flag'] = '0';
    }
    if($row['clean_audit_flag'] == '')
    {
        $row['clean_audit_flag'] = 'n/a';
    }
    if($row['bin_id'] == '')
    {
        $row['bin_id'] = 'n/a';
    }
    if($row['next_step_action'] == '')
    {
        $row['next_step_action'] = 'n/a';
    }
    if($row['origin_coo_id'] == '')
    {
        $row['origin_coo_id'] = 'n/a';
    }
    if($row['coo_id'] == '')
    {
        $row['coo_id'] = 'n/a';
    }
    if($row['valid_coo_flag'] == '')
    {
        $row['valid_coo_flag'] = 'N';
    }
    if($row['event_id'] == '')
    {
        $row['event_id'] = 'n/a';
    }
    if($row['customer_id'] == '')
    {
        $row['customer_id'] = 'n/a';
    }
    
    if($row['bin_scan_time'] !='')
    {
        $row['bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row['bin_scan_time']));
    }
    else
    {
        $row['bin_scan_time'] = '';
    }
    if($row['audit_result_scan_time'] !='')
    {
        $row['audit_result_scan_time'] = date("Y-m-d H:i:s", strtotime($row['audit_result_scan_time']));
    }
    else
    {
        $row['audit_result_scan_time'] = '';
    }
    if($row['audit_notes_scan_time'] !='')
    {
        $row['audit_notes_scan_time'] = date("Y-m-d H:i:s", strtotime($row['audit_notes_scan_time']));
    }
    else
    {
        $row['audit_notes_scan_time'] = '';
    }
    if($row['mpn_scan_time'] !='')
    {
        $row['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row['mpn_scan_time']));
    }
    else
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['serial_scan_time'] !='')
    {
        $row['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row['serial_scan_time']));
    }
    else
    {
        $row['serial_scan_time'] = '';
    }
    if($row['origin_bin_scan_time'] !='')
    {
        $row['origin_bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row['origin_bin_scan_time']));
    }
    else
    {
        $row['origin_bin_scan_time'] = '';
    }
    if($row['coo_scan_time'] !='')
    {
        $row['coo_scan_time'] = date("Y-m-d H:i:s", strtotime($row['coo_scan_time']));
    }
    else
    {
        $row['coo_scan_time'] = '';
    }
    if($row['valid_serial_flag'] == '1' && $row['valid_mpn_flag'] == '1' && $row['valid_next_step_action_flag'] == '1')
    {
        $row['clean_audit_flag'] = 'Y';
    }
    else
    {
        $row['clean_audit_flag'] = 'N';
    }
    if($row['valid_serial_flag'] == '1')
    {
        $row['valid_serial_flag'] = 'Y';
    }
    else
    {
        $row['valid_serial_flag'] = 'N';
    }
    if($row['valid_mpn_flag'] == '1')
    {
        $row['valid_mpn_flag'] = 'Y';
    }
    else
    {
        $row['valid_mpn_flag'] = 'N';
    }
    if($row['valid_next_step_action_flag'] == '1')
    {
        $row['valid_next_step_action_flag'] = 'Y';
    }
    else
    {
        $row['valid_next_step_action_flag'] = 'N';
    }
    $sqlwastecode = "Select WasteCode,WasteClassificationType from waste_codes where FacilityID = '".$row['FacilityID']."' and disposition_id = '".$row['SourceDisposition']."' and part_type = '".$row['part_type']."'";
    $querywastecode = mysqli_query($connectionlink1,$sqlwastecode);
    $rowwastecode = mysqli_fetch_assoc($querywastecode);
    if($rowwastecode['WasteCode'] == '')
    {
        $rowwastecode['WasteCode'] = 'n/a';
    }
    if($rowwastecode['WasteClassificationType'] == '')
    {
        $rowwastecode['WasteClassificationType'] = 'n/a';
    }
    
    $row2  = array('eV-Disposition-1',$row['assigner_login_id'],$row['operator_login_id'],$row['last_touch_login_id'],$row['audit_location_id'],$row['audit_type'],$row['audit_action'],$row['origin_bin_id'],$row['origin_bin_scan_time'],$row['origin_next_step_action'],$row['serial_id'],$row['serial_scan_time'],$row['origin_mpn_id'],$row['mpn_id'],$row['mpn_scan_time'],$row['part_type'],$row['audit_notes'],$row['audit_notes_scan_time'],$row['audit_result'],$row['audit_result_scan_time'],$row['valid_serial_flag'],$row['valid_mpn_flag'],$row['valid_next_step_action_flag'],$row['clean_audit_flag'],$row['bin_id'],$row['bin_scan_time'],$row['next_step_action'],$date,$time,$row['origin_coo_id'],$row['coo_id'],$row['coo_scan_time'],$row['valid_coo_flag'],$rowwastecode['WasteClassificationType'],$rowwastecode['WasteCode'],$row['event_id'],$differenceInSeconds,$row['customer_id']);
    $rows[] = $row2;
}

foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34].','.$record[35].','.$record[36].','.$record[37]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink1,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>