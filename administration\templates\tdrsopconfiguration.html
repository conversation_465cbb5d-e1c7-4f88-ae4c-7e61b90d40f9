<div class="row page" data-ng-controller="TDRSOPConfiguration">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>TDR SOP Configuration</span>
                        <div flex></div>
                    </div>
                </md-toolbar>

                <div class="row">
                    <div class="col-md-12">
                        <form name="TDRSOP_form" class="form-validation" data-ng-submit="submitForm()">
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>TDR Type</label>
                                    <md-select name="Status" ng-model="TDRSOP.TdrType" required aria-label="select">
                                        <md-option value="Trailer Docking">Trailer Docking</md-option>
                                        <md-option value="Trailer Release">Trailer Release</md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="TDRSOP_form.TdrType.$error" multiple ng-if='TDRSOP_form.TdrType.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                             <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Vehicle Type</label>
                                    <md-select name="VehicleType" ng-model="TDRSOP.VehicleType" required aria-label="select">
                                        <md-option value="Non-AZNG Pup Trailer < 29'">Non-AZNG Pup Trailer <29</md-option>
                                        <md-option value="29'-53' Standard Trailer & AZNG Pup Trailer"> 29' -53' Standard Trailer & AZNG Pup Trailer </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="TDRSOP_form.VehicleType.$error" multiple ng-if='TDRSOP_form.VehicleType.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Step No</label>
                                    <md-select name="Status" ng-model="TDRSOP.StepNo" required aria-label="select">
                                        <md-option value="1"> 1 </md-option>
                                        <md-option value="2"> 2 </md-option>
                                        <md-option value="3"> 3 </md-option>
                                        <md-option value="4"> 4 </md-option>
                                        <md-option value="5"> 5 </md-option>
                                        <md-option value="6"> 6 </md-option>
                                        <md-option value="7"> 7 </md-option>
                                        <md-option value="8"> 8 </md-option>
                                        <md-option value="9"> 9 </md-option>
                                        <md-option value="10"> 10 </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="TDRSOP_form.StepNo.$error" multiple ng-if='TDRSOP_form.StepNo.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-6">
                                    <md-input-container class="md-block">
                                        <label>Description</label>
                                        <input name="Description" ng-model="TDRSOP.Description" required ng-maxlength="250">  
                                        <div class="error-sapce">
                                            <div ng-messages="TDRSOP_form.Description.$error" multiple ng-if='TDRSOP_form.Description.$dirty'> 
                                                <div ng-message="required">This is required.</div>
                                                <div ng-message="maxlength">Max length 250.</div>
                                            </div>
                                        </div>
                                    </md-input-container>
                                </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Status</label>
                                    <md-select name="Status" ng-model="TDRSOP.Status" required aria-label="select">
                                        <md-option value="1"> Active </md-option>
                                        <md-option value="2"> In active </md-option>
                                    </md-select>
                                    <div class="error-space">
                                    <div ng-messages="TDRSOP_form.Status.$error" multiple ng-if='TDRSOP_form.Status.$dirty'>
                                        <div ng-message="required">This is required.</div>
                                    </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-12 btns-row">
                                <a href="#!/tdrsopconfiguration" style="text-decoration: none;">
                                    <md-button class="md-button md-raised btn-w-md  md-default">
                                        Cancel
                                    </md-button>
                                </a>

                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="TDRSOP_form.$invalid || TDRSOP.busy" ng-click="SaveTDRSOPConfiguration()">
                                <span ng-show="! TDRSOP.busy">Save</span>
                                <span ng-show="TDRSOP.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                            </div>
                        </form>
                    </div>
                </div>
            </md-card>

            <md-card class="no-margin-h pt-0">
                <md-toolbar class="md-table-toolbar md-default" ng-init="TDRSOPConfigurationList = true;">
                    <div class="md-toolbar-tools" style="cursor: pointer;">

                        <i ng-click="TDRSOPConfigurationList = !TDRSOPConfigurationList" class="material-icons md-primary" ng-show="TDRSOPConfigurationList">keyboard_arrow_up</i>
                        <i ng-click="TDRSOPConfigurationList = !TDRSOPConfigurationList" class="material-icons md-primary" ng-show="! TDRSOPConfigurationList">keyboard_arrow_down</i>
                        <span ng-click="TDRSOPConfigurationList = !TDRSOPConfigurationList">TDR SOP List</span>
                        <div flex></div>
                         <a href="#!/tdrsopconfiguration" ng-click="TDRSOPConfigurationxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                            <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                        </a>
                    </div>
                </md-toolbar>

                <div class="callout callout-info" ng-show="!busy && pagedItems.length == 0">
                    <p>No TDR SOP Configuration Available </p>
                </div>

                <div class="row"  ng-show="TDRSOPConfigurationList">
                    <div class="col-md-12">
                        <div class="col-md-12">
                            <div ng-show="pagedItems" class="pull-right mt-10">
                                    <small>
                                    Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                    to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                        <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                    of <span style="font-weight:bold;">{{total}}</span>
                                    </small>
                            </div>
                            <div style="clear:both;"></div>
                            <div class="table-responsive" style="overflow: auto;">
                                <table class="table table-striped mb-0">
                                    <thead>

                                        <tr class="th_sorting">
                                            <th style="min-width: 40px;">Edit</th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('TdrType')" ng-class="{'orderby' : OrderBy == 'TdrType'}">
                                                <div style="min-width: 80px;">
                                                    TDR Type<i class="fa fa-sort pull-right" ng-show="OrderBy != 'TdrType'"></i>
                                                    <span ng-show="OrderBy == 'TdrType'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                             <th style="cursor:pointer;" ng-click="MakeOrderBy('VehicleType')" ng-class="{'orderby' : OrderBy == 'VehicleType'}">
                                                <div style="min-width: 220px;">
                                                    Vehicle Type<i class="fa fa-sort pull-right" ng-show="OrderBy != 'VehicleType'"></i>
                                                    <span ng-show="OrderBy == 'VehicleType'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('StepNo')" ng-class="{'orderby' : OrderBy == 'StepNo'}">
                                                <div style="min-width: 80px;">
                                                    Step No<i class="fa fa-sort pull-right" ng-show="OrderBy != 'StepNo'"></i>
                                                    <span ng-show="OrderBy == 'StepNo'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>

                                             <th style="cursor:pointer;" ng-click="MakeOrderBy('Description')" ng-class="{'orderby' : OrderBy == 'Description'}">
                                                <div style="min-width: 130px;">
                                                    Description<i class="fa fa-sort pull-right" ng-show="OrderBy != 'Description'"></i>
                                                    <span ng-show="OrderBy == 'Description'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                            <th style="cursor:pointer;" ng-click="MakeOrderBy('StatusName')" ng-class="{'orderby' : OrderBy == 'StatusName'}">
                                                <div style="min-width: 100px;">
                                                    Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'StatusName'"></i>
                                                    <span ng-show="OrderBy == 'StatusName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span>
                                                </div>
                                            </th>
                                        </tr>

                                        <tr class="errornone">
                                            <td>&nbsp;</td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="TdrType" ng-model="filter_text[0].TdrType" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="VehicleType" ng-model="filter_text[0].VehicleType" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="StepNo" ng-model="filter_text[0].StepNo" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="Description" ng-model="filter_text[0].Description" ng-change="MakeFilter()"  aria-label="text" />
                                                </md-input-container>
                                            </td>
                                            <td>
                                                <md-input-container class="md-block mt-0">
                                                    <input type="text" name="StatusName" ng-model="filter_text[0].StatusName" ng-change="MakeFilter()" aria-label="text" />
                                                </md-input-container>
                                            </td>
                                        </tr>
                                    </thead>

                                    <tbody ng-show="pagedItems.length > 0">
                                        <tr ng-repeat="product in pagedItems">
                                            <td class="action-icons" style="min-width: 40px;">
                                                <span ng-click="EditTDRSOPConfiguration(product,$event)"><i class="material-icons text-danger edit">edit</i></span>
                                            </td>
                                            <td>
                                                {{product.TdrType}}
                                            </td>
                                            <td>
                                                {{product.VehicleType}}
                                            </td>
                                            <td>
                                                {{product.StepNo}}
                                            </td>
                                            <td>
                                                {{product.Description}}
                                            </td>
                                            <td>
                                                {{product.StatusName}}
                                            </td>
                                        </tr>
                                    </tbody>

                                    <tfoot>
                                        <tr>
                                            <td colspan="5">
                                                <div>
                                                    <ul class="pagination">
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="firstPage()"><< First</a>
                                                        </li>
                                                        <li ng-class="prevPageDisabled()">
                                                            <a href ng-click="prevPage()"><< Prev</a>
                                                        </li>
                                                        <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                            <a style="cursor:pointer;">{{n+1}}</a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="nextPage()">Next >></a>
                                                        </li>
                                                        <li ng-class="nextPageDisabled()">
                                                            <a href ng-click="lastPage()">Last >></a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    </tfoot>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </md-card>
        </article>
    </div>
</div>
