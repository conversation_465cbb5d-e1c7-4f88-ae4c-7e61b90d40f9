<?php
/**
 * Convert Single Shipment Script
 * Converts shipping containers to bins for a specific shipment
 * Usage: convert_single_shipment.php?shippingID=12345
 */

// Database connection
session_start();
include_once("../connection.php");
$obj1 = new Connection();
$connectionlink = Connection::DBConnect();

// Configuration
$systemUserId = 712; // System user ID for tracking

// Get ShippingID from URL
$shippingID = isset($_GET['shippingID']) ? trim($_GET['shippingID']) : null;

if (!$shippingID || empty($shippingID)) {
    die("Error: Please provide a ShippingID in the URL. Example: convert_single_shipment.php?shippingID=BinTest19\n");
}

// Escape ShippingID for SQL safety
$shippingID = mysqli_real_escape_string($connectionlink, $shippingID);

echo "=== CONVERT SINGLE SHIPMENT SCRIPT ===\n";
echo "Processing ShippingID: $shippingID\n";
echo "System User ID: $systemUserId\n\n";

// Step 1: Validate the shipment
function validateShipment($connectionlink, $shippingID) {
    echo "=== STEP 1: Validating Shipment ===\n";
    
    $shipmentQuery = "SELECT ShippingID, ShipmentStatusID, CreatedDate, CreatedBy 
                      FROM shipping 
                      WHERE ShippingID = '$shippingID'";
    $result = mysqli_query($connectionlink, $shipmentQuery);
    
    if (!$result || mysqli_num_rows($result) == 0) {
        die("✗ Error: Shipment ID $shippingID not found\n");
    }
    
    $shipment = mysqli_fetch_assoc($result);
    echo "✓ Shipment found: ID $shippingID, Status: {$shipment['ShipmentStatusID']}\n";
    
    // Get container count
    $containerCountQuery = "SELECT COUNT(*) as container_count 
                           FROM shipping_containers 
                           WHERE ShippingID = '$shippingID'";
    $countResult = mysqli_query($connectionlink, $containerCountQuery);
    $countRow = mysqli_fetch_assoc($countResult);
    
    echo "✓ Containers in shipment: {$countRow['container_count']}\n\n";

    $query = "UPDATE shipping_container_serials se
    LEFT JOIN shipping_containers sc 
        ON se.ShippingContainerID = sc.ShippingContainerID
    LEFT JOIN shipping s 
        ON sc.ShippingID = s.ShippingID
    LEFT JOIN custompallet c 
        ON CONVERT(se.BinName_bkp USING utf8mb3) COLLATE utf8mb3_unicode_ci 
        = CONVERT(c.BinName USING utf8mb3) COLLATE utf8mb3_unicode_ci
    SET 
        se.CustomPalletID = c.CustomPalletID,
        se.BinName = c.BinName 
    WHERE 
        s.ShipmentStatusID = 1
        AND BinName_bkp != '' and c.CustomPalletID IN (
            SELECT CustomPalletID FROM custompallet WHERE ShippingID = '$shippingID'
        )";
    $q = mysqli_query($connectionlink, $query);
    
    return $shipment;
}

// Step 2: Convert containers to bins for this shipment
function convertShipmentContainers($connectionlink, $shippingID, $systemUserId) {
    echo "=== STEP 2: Converting Containers to Bins ===\n";
    
    // Check for containers already converted
    $alreadyConvertedQuery = "SELECT COUNT(*) as converted_count
                             FROM shipping_containers sc
                             JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
                             WHERE sc.ShippingID = '$shippingID'
                               AND cp.ConvertedFromShippingContainer = 1";
    $convertedResult = mysqli_query($connectionlink, $alreadyConvertedQuery);
    $convertedRow = mysqli_fetch_assoc($convertedResult);

    if ($convertedRow['converted_count'] > 0) {
        echo "✓ Found {$convertedRow['converted_count']} containers already converted (will update if needed)\n";
    }
    
    // Get containers to convert (use INSERT IGNORE to handle existing records)
    $containersQuery = "SELECT sc.*,
                               CASE
                                   WHEN sc.FacilityID IS NOT NULL AND sc.FacilityID != 0
                                        AND EXISTS (SELECT 1 FROM facility f WHERE f.FacilityID = sc.FacilityID)
                                   THEN sc.FacilityID
                                   ELSE (SELECT MIN(FacilityID) FROM facility WHERE FacilityID > 0 LIMIT 1)
                               END as SafeFacilityID,
                               CASE
                                   WHEN sc.LocationID IS NOT NULL AND sc.LocationID != 0
                                        AND EXISTS (SELECT 1 FROM location l WHERE l.LocationID = sc.LocationID)
                                   THEN sc.LocationID
                                   ELSE NULL
                               END as SafeLocationID
                        FROM shipping_containers sc
                        WHERE sc.ShippingID = '$shippingID'";
    
    $containersResult = mysqli_query($connectionlink, $containersQuery);
    
    if (!$containersResult) {
        die("✗ Error getting containers: " . mysqli_error($connectionlink) . "\n");
    }
    
    $totalContainers = mysqli_num_rows($containersResult);
    echo "Containers to process: $totalContainers\n";

    if ($totalContainers == 0) {
        echo "✓ No containers found in this shipment\n\n";
        return 0;
    }
    
    $converted = 0;
    
    // Convert each container
    while ($container = mysqli_fetch_assoc($containersResult)) {
        $containerID = $container['ShippingContainerID'];
        echo "Converting: $containerID... ";
        
        // Insert into custompallet (use INSERT IGNORE to handle existing records)
        $insertSQL = "INSERT IGNORE INTO custompallet (
            FacilityID, LocationID, Description, MaximumAssets, AssetsCount,
            CreatedDate, CreatedBy, LastModifiedDate, LastModifiedBy,
            StatusID, StatusModifiedDate, StatusModifiedBy,
            disposition_id, BinName, SealID, ShippingControllerLoginID,
            ContainerWeight, RecentSealDate, RecentSealBy,
            ReferenceID, ReferenceTypeID, ReferenceType, ReferenceIDRequired,
            idPackage, ShippingID, bin_added_to_shipment_time,
            ASNContainer, idPallet, BatchRecovery, ConvertedFromShippingContainer
        ) VALUES (
            '{$container['SafeFacilityID']}',
            " . ($container['SafeLocationID'] ? "'{$container['SafeLocationID']}'" : 'NULL') . ",
            " . ($container['Description'] ? "'" . mysqli_real_escape_string($connectionlink, $container['Description']) . "'" : 'NULL') . ",
            " . ($container['MaximumAssets'] ? "'{$container['MaximumAssets']}'" : 'NULL') . ",
            '0',
            NOW(),
            '$systemUserId',
            NOW(),
            '$systemUserId',
            " . ($container['StatusID'] ? "'{$container['StatusID']}'" : "'6'") . ",
            NOW(),
            '$systemUserId',
            " . ($container['disposition_id'] ? "'{$container['disposition_id']}'" : 'NULL') . ",
            '" . mysqli_real_escape_string($connectionlink, $container['ShippingContainerID']) . "',
            " . ($container['SealID'] ? "'" . mysqli_real_escape_string($connectionlink, $container['SealID']) . "'" : 'NULL') . ",
            " . ($container['ShippingControllerLoginID'] ? "'" . mysqli_real_escape_string($connectionlink, $container['ShippingControllerLoginID']) . "'" : 'NULL') . ",
            " . ($container['ContainerWeight'] ? "'{$container['ContainerWeight']}'" : 'NULL') . ",
            " . ($container['RecentSealDate'] ? "'{$container['RecentSealDate']}'" : 'NULL') . ",
            " . ($container['RecentSealBy'] ? "'{$container['RecentSealBy']}'" : 'NULL') . ",
            " . ($container['ReferenceID'] ? "'" . mysqli_real_escape_string($connectionlink, $container['ReferenceID']) . "'" : 'NULL') . ",
            " . ($container['ReferenceTypeID'] ? "'{$container['ReferenceTypeID']}'" : 'NULL') . ",
            " . ($container['ReferenceType'] ? "'" . mysqli_real_escape_string($connectionlink, $container['ReferenceType']) . "'" : 'NULL') . ",
            " . ($container['ReferenceIDRequired'] ? "'{$container['ReferenceIDRequired']}'" : "'0'") . ",
            " . ($container['idPackage'] ? "'{$container['idPackage']}'" : 'NULL') . ",
            '$shippingID',
            " . ($container['bin_added_to_shipment_time'] ? "'{$container['bin_added_to_shipment_time']}'" : 'NOW()') . ",
            " . ($container['ASNContainer'] ? "'{$container['ASNContainer']}'" : "'0'") . ",
            " . ($container['idPallet'] ? "'{$container['idPallet']}'" : 'NULL') . ",
            " . ($container['BatchRecovery'] ? "'{$container['BatchRecovery']}'" : "'0'") . ",
            '1'
        )";
        
        if (mysqli_query($connectionlink, $insertSQL)) {
            $affectedRows = mysqli_affected_rows($connectionlink);
            if ($affectedRows > 0) {
                $converted++;
                echo "✓ (created)\n";
            } else {
                echo "✓ (already exists)\n";
            }
        } else {
            echo "✗ Error: " . mysqli_error($connectionlink) . "\n";
        }
    }
    
    echo "✓ Total containers processed: $totalContainers (New: $converted, Existing: " . ($totalContainers - $converted) . ")\n\n";
    return $converted;
}

// Step 3: Update shipping_container_serials
function updateShipmentSerials($connectionlink, $shippingID, $systemUserId) {
    echo "=== STEP 3: Updating Shipping Container Serials ===\n";
    
    $updateSQL = "UPDATE shipping_container_serials scs
                  JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
                  JOIN shipping_containers sc ON sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
                  SET
                      scs.CustomPalletID = cp.CustomPalletID,
                      scs.BinName = cp.BinName,
                      scs.UpdatedDate = NOW(),
                      scs.UpdatedBy = '$systemUserId'
                  WHERE sc.ShippingID = '$shippingID'
                    AND scs.ShippingContainerID IS NOT NULL
                    AND (scs.CustomPalletID IS NULL OR scs.BinName IS NULL)
                    AND cp.ConvertedFromShippingContainer = 1";
    
    if (mysqli_query($connectionlink, $updateSQL)) {
        $updated = mysqli_affected_rows($connectionlink);
        echo "✓ Updated $updated shipping container serials\n\n";
        return $updated;
    } else {
        echo "✗ Error updating serials: " . mysqli_error($connectionlink) . "\n\n";
        return 0;
    }
}

// Step 4: Create custompallet_items from shipping_container_serials
function createCustomPalletItems($connectionlink, $shippingID, $systemUserId) {
    echo "=== STEP 4: Creating CustomPallet Items ===\n";

    // Get total items to create
    // $totalResult = mysqli_query($connectionlink, "
    //     SELECT COUNT(DISTINCT scs.SerialID) as total
    //     FROM shipping_container_serials scs
    //     JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
    //     JOIN shipping_containers sc ON sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
    //     WHERE sc.ShippingID = '$shippingID'
    //       AND cp.ConvertedFromShippingContainer = 1
    //       AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)
    // ");

    // if (!$totalResult) {
    //     echo "✗ Error getting total items: " . mysqli_error($connectionlink) . "\n\n";
    //     return 0;
    // }

    // $totalRow = mysqli_fetch_assoc($totalResult);
    // $totalItems = $totalRow['total'];

    // echo "Total items to create: $totalItems\n";

    // if ($totalItems == 0) {
    //     echo "✓ No items to create\n\n";
    //     return 0;
    // }

    // Create custompallet_items in bulk
    $sql = "INSERT IGNORE INTO custompallet_items (
        CustomPalletID, AssetScanID, DateCreated, CreatedBy, status, ServerID, MediaID,byproduct_id, Quantity
    )
    SELECT DISTINCT
        cp.CustomPalletID,
        scs.AssetScanID,
        COALESCE(scs.CreatedDate, NOW()) as DateCreated,
        COALESCE(scs.CreatedBy, $systemUserId) as CreatedBy,
        COALESCE(scs.StatusID, 8) as status,
        scs.ServerID,
        scs.MediaID,
        scs.byproduct_id,
        COALESCE(scs.Quantity, 1) as Quantity
    FROM shipping_container_serials scs
    JOIN custompallet cp ON cp.CustomPalletID = scs.CustomPalletID 
    JOIN shipping_containers sc ON sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
    WHERE sc.ShippingID = '$shippingID'
      AND cp.ConvertedFromShippingContainer = 1
      AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL OR scs.byproduct_id IS NOT NULL)";

    if (mysqli_query($connectionlink, $sql)) {
        $created = mysqli_affected_rows($connectionlink);
        echo "✓ Created $created custompallet_items records\n\n";
        return $created;
    } else {
        echo "✗ Error creating custompallet_items: " . mysqli_error($connectionlink) . "\n\n";
        return 0;
    }
}

// Step 5: Update AssetsCount for converted bins
function updateAssetsCount($connectionlink, $shippingID, $systemUserId) {
    echo "=== STEP 5: Updating Assets Count ===\n";

    $updateSQL = "UPDATE custompallet cp
                  JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
                  SET cp.AssetsCount = (
                      SELECT COUNT(*)
                      FROM custompallet_items cpi
                      WHERE cpi.CustomPalletID = cp.CustomPalletID
                  ),
                  cp.LastModifiedDate = NOW(),
                  cp.LastModifiedBy = '$systemUserId'
                  WHERE sc.ShippingID = '$shippingID'
                    AND cp.ConvertedFromShippingContainer = 1";

    if (mysqli_query($connectionlink, $updateSQL)) {
        $updated = mysqli_affected_rows($connectionlink);
        echo "✓ Updated AssetsCount for $updated bins based on custompallet_items\n\n";
        return $updated;
    } else {
        echo "✗ Error updating AssetsCount: " . mysqli_error($connectionlink) . "\n\n";
        return 0;
    }
}

// Step 6: Generate report
function generateShipmentReport($connectionlink, $shippingID) {
    echo "=== STEP 6: Shipment Conversion Report ===\n";
    
    $reportSQL = "SELECT 
                      COUNT(*) as Total_Bins,
                      SUM(cp.AssetsCount) as Total_Assets,
                      AVG(cp.AssetsCount) as Avg_Assets_Per_Bin,
                      MAX(cp.AssetsCount) as Max_Assets_In_Bin
                  FROM custompallet cp
                  JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
                  WHERE sc.ShippingID = '$shippingID'
                    AND cp.ConvertedFromShippingContainer = 1";
    
    $result = mysqli_query($connectionlink, $reportSQL);
    if ($row = mysqli_fetch_assoc($result)) {
        echo "CONVERSION SUMMARY FOR SHIPMENT $shippingID:\n";
        echo "- Total Bins Created: " . $row['Total_Bins'] . "\n";
        echo "- Total Assets: " . $row['Total_Assets'] . "\n";
        echo "- Average Assets per Bin: " . round($row['Avg_Assets_Per_Bin'], 2) . "\n";
        echo "- Maximum Assets in a Bin: " . $row['Max_Assets_In_Bin'] . "\n";
    }
}

// Main execution
try {
    $startTime = time();
    
    // Step 1: Validate shipment
    $shipment = validateShipment($connectionlink, $shippingID);
    
    // Step 2: Convert containers to bins
    //$convertedContainers = convertShipmentContainers($connectionlink, $shippingID, $systemUserId);
    
    // Step 3: Update shipping_container_serials
    //$updatedSerials = updateShipmentSerials($connectionlink, $shippingID, $systemUserId);

    // Step 4: Create custompallet_items
    $createdItems = createCustomPalletItems($connectionlink, $shippingID, $systemUserId);

    // Step 5: Update AssetsCount
    $updatedAssets = updateAssetsCount($connectionlink, $shippingID, $systemUserId);

    // Step 6: Generate report
    generateShipmentReport($connectionlink, $shippingID);
    
    $totalTime = time() - $startTime;
    echo "\n✓ Shipment $shippingID conversion completed in $totalTime seconds!\n";
    
} catch (Exception $e) {
    echo "\n✗ Error during conversion: " . $e->getMessage() . "\n";
    exit(1);
}

?>
