<?php
/**
 * Convert Single Shipment serial
 * Loops through each serial
 */

// Database connection
session_start();
include_once("../connection.php");
$obj1 = new Connection();
$connectionlink = Connection::DBConnect();

// Configuration
$systemUserId = 712; // System user ID for tracking

// $query = "SELECT se.*,c.CustomPalletID,c.BinName FROM shipping_container_serials se 
// left join shipping_containers sc on se.ShippingContainerID = sc.ShippingContainerID 
// left join custompallet c on sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = c.BinName COLLATE utf8mb3_unicode_ci
// where isnull(sc.ShippingID) and se.Completed = 0 limit 10000";


$query = "SELECT a.AssetScanID,a.CustomPalletID,c.CustomPalletID FROM asset a,custompallet c 
where a.ShippingContainerID = c.BinName and  (isnull(a.CustomPalletID) or a.CustomPalletID = '') and a.StatusID = '8' and a.FacilityID = 11  limit 10000";

$q = mysqli_query($connectionlink, $query);
if(mysqli_affected_rows($connectionlink) > 0) {
    while($row = mysqli_fetch_assoc($q)) {             
        $query1 = "update asset set CustomPalletID = '".$row['CustomPalletID']."' where AssetScanID = '".$row['AssetScanID']."'";
        $q1 = mysqli_query($connectionlink, $query1);
        if(mysqli_error($connectionlink)) {			
            echo mysqli_error($connectionlink).$query1."<br>";
            continue;		
        }
    }

    echo "Loop Completed";
} else {
    echo "No Records";
}
?>
