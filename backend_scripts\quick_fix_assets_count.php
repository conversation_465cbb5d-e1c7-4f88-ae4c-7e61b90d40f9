<?php
/**
 * Quick Fix Assets Count Script
 * This script quickly updates AssetsCount for converted bins based on existing data
 * Much faster than recreating custompallet_items
 */

// Database connection
session_start();
include_once("../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();

$systemUserId = 712; // System user ID for tracking

echo "=== QUICK FIX ASSETS COUNT SCRIPT ===\n";
echo "This script will update AssetsCount for converted bins based on shipping_container_serials\n\n";

// Method 1: Update AssetsCount based on shipping_container_serials (faster)
function quickUpdateAssetsCount($connectionlink, $systemUserId) {
    echo "=== Updating AssetsCount Based on Shipping Container Serials ===\n";
    
    // Update AssetsCount for converted bins based on shipping_container_serials count
    $sql = "UPDATE custompallet cp
            JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
            JOIN shipping s ON sc.ShippingID = s.ShippingID
            SET cp.AssetsCount = (
                SELECT COUNT(*)
                FROM shipping_container_serials scs
                WHERE scs.ShippingContainerID COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
                  AND scs.CustomPalletID = cp.CustomPalletID
                  AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)
            ),
            cp.LastModifiedDate = NOW(),
            cp.LastModifiedBy = $systemUserId
            WHERE s.ShipmentStatusID = 1
              AND cp.ConvertedFromShippingContainer = 1";
    
    if (mysqli_query($connectionlink, $sql)) {
        $updated = mysqli_affected_rows($connectionlink);
        echo "✓ Updated AssetsCount for $updated converted bins\n";
        return $updated;
    } else {
        die("✗ Error updating AssetsCount: " . mysqli_error($connectionlink) . "\n");
    }
}

// Method 2: Create missing custompallet_items in bulk (if needed)
function bulkCreateMissingItems($connectionlink, $systemUserId) {
    echo "\n=== Creating Missing CustomPallet Items (Bulk) ===\n";
    
    $sql = "INSERT IGNORE INTO custompallet_items (
        CustomPalletID, AssetScanID, DateCreated, CreatedBy, status, ServerID, MediaID, Quantity
    )
    SELECT DISTINCT
        cp.CustomPalletID,
        scs.AssetScanID,
        COALESCE(scs.CreatedDate, NOW()) as DateCreated,
        COALESCE(scs.CreatedBy, $systemUserId) as CreatedBy,
        COALESCE(scs.StatusID, 8) as status,
        scs.ServerID,
        scs.MediaID,
        COALESCE(scs.Quantity, 1) as Quantity
    FROM shipping_container_serials scs
    JOIN custompallet cp ON cp.BinName COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci AND cp.CustomPalletID = scs.CustomPalletID
    JOIN shipping_containers sc ON sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = scs.ShippingContainerID COLLATE utf8mb3_unicode_ci
    JOIN shipping s ON sc.ShippingID = s.ShippingID
    WHERE s.ShipmentStatusID = 1
      AND cp.ConvertedFromShippingContainer = 1
      AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)";
    
    if (mysqli_query($connectionlink, $sql)) {
        $created = mysqli_affected_rows($connectionlink);
        echo "✓ Created $created custompallet_items records\n";
        return $created;
    } else {
        die("✗ Error creating custompallet_items: " . mysqli_error($connectionlink) . "\n");
    }
}

// Method 3: Update AssetsCount based on actual custompallet_items
function updateAssetsCountFromItems($connectionlink, $systemUserId) {
    echo "\n=== Updating AssetsCount Based on Actual Items ===\n";
    
    $sql = "UPDATE custompallet cp
            JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
            JOIN shipping s ON sc.ShippingID = s.ShippingID
            SET cp.AssetsCount = (
                SELECT COUNT(*)
                FROM custompallet_items cpi
                WHERE cpi.CustomPalletID = cp.CustomPalletID
            ),
            cp.LastModifiedDate = NOW(),
            cp.LastModifiedBy = $systemUserId
            WHERE s.ShipmentStatusID = 1
              AND cp.ConvertedFromShippingContainer = 1";
    
    if (mysqli_query($connectionlink, $sql)) {
        $updated = mysqli_affected_rows($connectionlink);
        echo "✓ Updated AssetsCount for $updated bins based on actual items\n";
        return $updated;
    } else {
        die("✗ Error updating AssetsCount from items: " . mysqli_error($connectionlink) . "\n");
    }
}

// Validation function
function validateData($connectionlink) {
    echo "\n=== VALIDATION REPORT ===\n";
    
    // Check total converted bins
    $totalResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as total
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
    ");
    $totalRow = mysqli_fetch_assoc($totalResult);
    echo "Total converted bins (active shipments): " . $totalRow['total'] . "\n";
    
    // Check bins with zero assets
    $zeroAssetsResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as zero_count
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
          AND cp.AssetsCount = 0
    ");
    $zeroAssetsRow = mysqli_fetch_assoc($zeroAssetsResult);
    echo "Bins with zero assets: " . $zeroAssetsRow['zero_count'] . "\n";
    
    // Check total assets
    $assetsResult = mysqli_query($connectionlink, "
        SELECT SUM(cp.AssetsCount) as total_assets
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
    ");
    $assetsRow = mysqli_fetch_assoc($assetsResult);
    echo "Total assets in converted bins: " . $assetsRow['total_assets'] . "\n";
    
    // Check custompallet_items count
    $itemsResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as total_items
        FROM custompallet_items cpi
        JOIN custompallet cp ON cpi.CustomPalletID = cp.CustomPalletID
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
    ");
    $itemsRow = mysqli_fetch_assoc($itemsResult);
    echo "Total custompallet_items records: " . $itemsRow['total_items'] . "\n";
    
    // Check for mismatches
    $mismatchResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as mismatch_count
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
          AND cp.AssetsCount != (
              SELECT COUNT(*)
              FROM custompallet_items cpi
              WHERE cpi.CustomPalletID = cp.CustomPalletID
          )
    ");
    $mismatchRow = mysqli_fetch_assoc($mismatchResult);
    echo "Bins with AssetsCount mismatch: " . $mismatchRow['mismatch_count'] . "\n";
    
    if ($mismatchRow['mismatch_count'] == 0) {
        echo "✓ All bins have correct AssetsCount!\n";
    } else {
        echo "⚠ Some bins have AssetsCount mismatches\n";
    }
}

// Main execution
echo "Choose your approach:\n";
echo "1. Quick fix - Update AssetsCount based on shipping_container_serials (FASTEST)\n";
echo "2. Complete fix - Create missing items + update AssetsCount (SLOWER)\n";
echo "3. Validation only - Check current status\n";
echo "\nRecommended: Start with option 1, then run option 3 to validate\n\n";

// For automated execution, use option 1 (fastest)
echo "Running QUICK FIX (Option 1)...\n\n";

try {
    // Step 1: Quick update AssetsCount
    $updated = quickUpdateAssetsCount($connectionlink, $systemUserId);
    
    // Step 2: Validate
    validateData($connectionlink);
    
    echo "\n✓ Quick fix completed!\n";
    echo "If you need custompallet_items records, run the complete fix script.\n";
    
} catch (Exception $e) {
    echo "\n✗ Error during quick fix: " . $e->getMessage() . "\n";
    exit(1);
}

?>
