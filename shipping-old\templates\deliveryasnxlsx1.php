<?php
session_start();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$ShippingID = 'WW-RZRR-3111';
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "Delivery ASN"." - ".$ShippingID.".xlsx" ;
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Delivery ASN ');

$sqlshipticket = "Select SF.FacilityName,SD.disposition From shipping S
                    LEFT JOIN facility SF ON SF.FacilityID = S.FacilityID 
                    LEFT JOIN disposition SD ON SD.disposition_id = S.disposition_id
                    where S.ShippingID = '".$ShippingID."'";
$queryshipticket = mysqli_query($connectionlink1,$sqlshipticket);
$rowshipticket = mysqli_fetch_assoc($queryshipticket);
if($rowshipticket['FacilityName'] == 'CVG110')
{
    $rowshipticket['Facilityshotcode'] = 'AWSCV';
}
if($rowshipticket['FacilityName'] == 'DUB110')
{
    $rowshipticket['Facilityshotcode'] = 'AWSDU';
}
if($rowshipticket['FacilityName'] == 'SIN100')
{
    $rowshipticket['Facilityshotcode'] = 'AWSSI';
}
$rowshipticketdetails  = array('',$rowshipticket['Facilityshotcode'],'N',$ShippingID,'T','1','110-100','Variable','');
$rowsshipticketdetails[] = $rowshipticketdetails;

$sqlshipcontainer = "Select ShippingContainerID from shipping_containers where ShippingID = '".$ShippingID."'";
$queryshipcontainer = mysqli_query($connectionlink1,$sqlshipcontainer);
$i=1;
if(mysqli_num_rows($queryshipcontainer) > 0)
{
    while($rowshipcontainer = mysqli_fetch_assoc($queryshipcontainer))
    {
        $sqlserialcount = "Select count(*) as serialcount from shipping_container_serials where ShippingContainerID = '".$rowshipcontainer['ShippingContainerID']."'";
        $queryserialcount = mysqli_query($connectionlink1,$sqlserialcount);
        $rowserialcount = mysqli_fetch_assoc($queryserialcount);
        $sqlshipcontainerserial = "Select SerialNumber,UniversalModelNumber from shipping_container_serials where ShippingContainerID = '".$rowshipcontainer['ShippingContainerID']."' order by UniversalModelNumber DESC";
        $queryshipcontainerserial = mysqli_query($connectionlink1,$sqlshipcontainerserial);
        $j=1;
        if($rowserialcount['serialcount'] > 0)
        {
            while($rowshipcontainerserial = mysqli_fetch_assoc($queryshipcontainerserial))
            {
                if($mpn != $rowshipcontainerserial['UniversalModelNumber'])
                {
                    $sqlextmpn = "Select ext_mpn_id,coo_id from catlog_creation where mpn_id = '".$rowshipcontainerserial['UniversalModelNumber']."'";
                    $queryextmpn = mysqli_query($connectionlink1,$sqlextmpn);
                    $rowextmpn = mysqli_fetch_assoc($queryextmpn);
                    $extmpnval = $rowextmpn['ext_mpn_id'];
                    $cooid = $rowextmpn['coo_id'];
                }
                
                $mpn = $rowshipcontainerserial['UniversalModelNumber'];
                if($i == 1)
                {
                    $sqlextmpn = "Select ext_mpn_id,coo_id from catlog_creation where mpn_id = '".$rowshipcontainerserial['UniversalModelNumber']."'";
                    $queryextmpn = mysqli_query($connectionlink1,$sqlextmpn);
                    $rowextmpn = mysqli_fetch_assoc($queryextmpn);
                    $extmpnval = $rowextmpn['ext_mpn_id'];
                    $cooid = $rowextmpn['coo_id'];
                }
                if($i==1 && $k!=1)
                {
                    $rowshipcontainerdetails  = array('',$ShippingID,$i,$rowshipcontainer['ShippingContainerID'],$extmpnval,$rowserialcount['serialcount'],$cooid,'1','');
                    $rowsshipcontainerdetails[] = $rowshipcontainerdetails;
                    $k=1;
                }
                if($i>1)
                {
                    if($j==1)
                    {
                        $rowshipcontainerdetails  = array('',$ShippingID,$i,$rowshipcontainer['ShippingContainerID'],$extmpnval,$rowserialcount['serialcount'],$cooid,'1','');
                        $rowsshipcontainerdetails[] = $rowshipcontainerdetails;
                    }
                }
                $j=$j+1;
            }
            $i= $i+1;
        }
    }
}

$sqlshipcontainer = "SELECT SS.SerialNumber,SS.ShippingContainerID,SS.InventorySerialNumber FROM shipping_container_serials SS,shipping_containers SC
WHERE 
SS.ShippingContainerID = SC.ShippingContainerID
AND SC.ShippingID = '".$ShippingID."'";
$queryshipcontainer = mysqli_query($connectionlink1,$sqlshipcontainer);
$ii=0;
if(mysqli_num_rows($queryshipcontainer) > 0)
{
    while($rowshipcontainer = mysqli_fetch_assoc($queryshipcontainer))
    {
        if($ii == 0)
        {
            $containerid = $rowshipcontainer['ShippingContainerID'];
            $ii = $ii+1;
        }
        if($containerid != $rowshipcontainer['ShippingContainerID'])
        {
            $ii = $ii+1;
            $containerid = $rowshipcontainer['ShippingContainerID'];
        }
        else
        {
            $containerid = $rowshipcontainer['ShippingContainerID'];
        }
        if($rowshipcontainer['SerialNumber'] == '')
        {
            $rowshipcontainer['SerialNumber'] = $rowshipcontainer['InventorySerialNumber'];
        }
        //$containerid = $rowshipcontainer['ShippingContainerID'];
        $rowshipserialdetails  = array('',$ShippingID,$ii,$rowshipcontainer['ShippingContainerID'],$rowshipcontainer['SerialNumber']);
        $rowsshipserialdetails[] = $rowshipserialdetails;
    }
}
$iii = 1;
$m = 0;
foreach($rowsshipserialdetails as $rowsshipserialdetails11)
{
    foreach($rowsshipticketdetails as $rowsshipticketdetails11)
    {
        if($ticketid == $rowsshipticketdetails11[3])
        {
            if($m > 0)
            {
                $rowfinal  = array('','','','','','','','','',$rowsshipcontainerdetails[$m][0],$rowsshipcontainerdetails[$m][1],$rowsshipcontainerdetails[$m][2],$rowsshipcontainerdetails[$m][3],$rowsshipcontainerdetails[$m][4],$rowsshipcontainerdetails[$m][5],$rowsshipcontainerdetails[$m][6],$rowsshipcontainerdetails[$m][7],$rowsshipcontainerdetails[$m][8],$rowsshipserialdetails11[0],$rowsshipserialdetails11[1],$rowsshipserialdetails11[2],$rowsshipserialdetails11[3],$rowsshipserialdetails11[4]);
                $rowfinals[] = $rowfinal;
            }
        }
        else
        {
            foreach($rowsshipcontainerdetails as $rowsshipcontainerdetails111)
            {
                if($iii == 1)
                {
                    $rowfinal  = array($rowsshipticketdetails11[0],$rowsshipticketdetails11[1],$rowsshipticketdetails11[2],$rowsshipticketdetails11[3],$rowsshipticketdetails11[4],$rowsshipticketdetails11[5],$rowsshipticketdetails11[6],$rowsshipticketdetails11[7],$rowsshipticketdetails11[8],$rowsshipcontainerdetails111[0],$rowsshipcontainerdetails111[1],$rowsshipcontainerdetails111[2],$rowsshipcontainerdetails111[3],$rowsshipcontainerdetails111[4],$rowsshipcontainerdetails111[5],$rowsshipcontainerdetails111[6],$rowsshipcontainerdetails111[7],$rowsshipcontainerdetails111[8],$rowsshipserialdetails11[0],$rowsshipserialdetails11[1],$rowsshipserialdetails11[2],$rowsshipserialdetails11[3],$rowsshipserialdetails11[4]);
                    $rowfinals[] = $rowfinal;
                    $ticketid = $rowsshipticketdetails11[3];
                    $iii = $iii+1;
                }
            }
        }
    }
    $m = $m+1;
}
/*foreach($rowsshipserialdetails as $rowsshipserialdetails11)
{
    foreach($rowsshipticketdetails as $rowsshipticketdetails11)
    {
        foreach($rowsshipcontainerdetails as $rowsshipcontainerdetails11)
        {
            if($ticketid == $rowsshipticketdetails11[3])
            {
                if($containerid == $rowsshipcontainerdetails11[3])
                {
                    $rowfinal  = array('','','','','','','','','','','','','','','','','','',$rowsshipserialdetails11[0],$rowsshipserialdetails11[1],$rowsshipserialdetails11[2],$rowsshipserialdetails11[3],$rowsshipserialdetails11[4]);
                    $rowfinals[] = $rowfinal;
                    $containerid = $rowsshipcontainerdetails11[3];
                }
                else
                {
                    $rowfinal  = array('','','','','','','','','',$rowsshipcontainerdetails11[0],$rowsshipcontainerdetails11[1],$rowsshipcontainerdetails11[2],$rowsshipcontainerdetails11[3],$rowsshipcontainerdetails11[4],$rowsshipcontainerdetails11[5],$rowsshipcontainerdetails11[6],$rowsshipcontainerdetails11[7],$rowsshipcontainerdetails11[8],$rowsshipserialdetails11[0],$rowsshipserialdetails11[1],$rowsshipserialdetails11[2],$rowsshipserialdetails11[3],$rowsshipserialdetails11[4]);
                    $rowfinals[] = $rowfinal;
                    $containerid = $rowsshipcontainerdetails11[3];
                }
            }
            else{
                $rowfinal  = array($rowsshipticketdetails11[0],$rowsshipticketdetails11[1],$rowsshipticketdetails11[2],$rowsshipticketdetails11[3],$rowsshipticketdetails11[4],$rowsshipticketdetails11[5],$rowsshipticketdetails11[6],$rowsshipticketdetails11[7],$rowsshipticketdetails11[8],$rowsshipcontainerdetails11[0],$rowsshipcontainerdetails11[1],$rowsshipcontainerdetails11[2],$rowsshipcontainerdetails11[3],$rowsshipcontainerdetails11[4],$rowsshipcontainerdetails11[5],$rowsshipcontainerdetails11[6],$rowsshipcontainerdetails11[7],$rowsshipcontainerdetails11[8],$rowsshipserialdetails11[0],$rowsshipserialdetails11[1],$rowsshipserialdetails11[2],$rowsshipserialdetails11[3],$rowsshipserialdetails11[4]);
                $rowfinals[] = $rowfinal;
                $ticketid = $rowsshipticketdetails11[3];
                $containerid = $rowsshipcontainerdetails11[3];
            }
        }
    }
}*/
$finalarr = $rowsshipticketdetails+$rowsshipcontainerdetails+$rowsshipserialdetails;
$header = array('PVMI ASN Header','Warehouse ID','Receipt Type','ASN Reference Number','Mode','Supplier Code','Tracking Number','Carrier','','PVMI ASN Detail','ASN Reference Number','ASN Line Number','container_id','Supplier Part Number','Qty Shipped','COO','Serial Indicator','','PVMI ASN Sdetail','ASN Reference Number','ASN Line Number','container_id','serial_id');
$sheet_name = 'Delivery ASN';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
/*$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);*/
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
//foreach($rows as $row11)
//    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);

foreach($rowfinals as $finalarr11)
    $writer->writeSheetRow($sheet_name, $finalarr11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
//$writer->writeToStdOut();
$writer->writeToFile('DeliveryASN.xlsx');
exit(0);
?> 