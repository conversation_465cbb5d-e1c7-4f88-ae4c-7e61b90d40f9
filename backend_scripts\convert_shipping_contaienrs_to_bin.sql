CREATE TABLE `custompallet` (
  `CustomPalletID` int NOT NULL AUTO_INCREMENT,
  `FacilityID` int DEFAULT NULL,
  `LocationID` int DEFAULT NULL,
  `Description` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `MaximumAssets` int DEFAULT NULL,
  `AssetsCount` int DEFAULT '0',
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `LastModifiedDate` datetime DEFAULT NULL,
  `LastModifiedBy` int DEFAULT NULL,
  `StatusID` int DEFAULT NULL,
  `StatusModifiedDate` datetime DEFAULT NULL,
  `StatusModifiedBy` int DEFAULT NULL,
  `AccountID` int DEFAULT NULL,
  `disposition_id` int DEFAULT NULL,
  `workflow_id` int DEFAULT NULL,
  `LocationType` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `Room` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `Serialization` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `BinType` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `BinName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `SiteID` int DEFAULT NULL,
  `InventoryBased` tinyint DEFAULT '0',
  `disposition_sequence_number` int DEFAULT '0',
  `AcceptAllDisposition` tinyint DEFAULT '0',
  `LockedForBulkMediaDestruction` tinyint DEFAULT '0',
  `LockedTime` datetime DEFAULT NULL,
  `LockedBy` int DEFAULT NULL,
  `MobilityName` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `MobilityNameCreatedBy` int DEFAULT NULL,
  `MobilityNameCreatedDate` datetime DEFAULT NULL,
  `ControlID` int DEFAULT NULL COMMENT 'bin_audit_control id',
  `DateAddedToBinAudit` datetime DEFAULT NULL,
  `AuditDate` datetime DEFAULT NULL,
  `AuditLocked` tinyint DEFAULT '0',
  `BinTypeID` int DEFAULT NULL,
  `PartTypeSummary` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `PartTypeSummaryUpdatedDate` datetime DEFAULT NULL,
  `PartTypeSummaryUpdatedBy` int DEFAULT NULL,
  `ParentCustomPalletID` int DEFAULT NULL,
  `ParentBinName` text,
  `SealID` varchar(50) DEFAULT NULL,
  `ShippingControllerLoginID` varchar(100) DEFAULT NULL,
  `ContainerWeight` int DEFAULT NULL,
  `RecentSealDate` datetime DEFAULT NULL,
  `RecentSealBy` int DEFAULT NULL,
  `ReferenceID` varchar(500) DEFAULT NULL,
  `ReferenceTypeID` int DEFAULT NULL,
  `ReferenceType` varchar(100) DEFAULT NULL,
  `ReferenceIDRequired` tinyint DEFAULT '0',
  `CustomerLock` tinyint DEFAULT '0',
  `AWSCustomerID` int DEFAULT NULL,
  `GroupID` int DEFAULT NULL,
  `idPackage` int DEFAULT NULL,
  `MaxLimitRequired` tinyint DEFAULT '0',
  `ShippingID` varchar(50) DEFAULT NULL,
  `bin_added_to_shipment_time` datetime DEFAULT NULL,
  `bin_added_to_shipment_by` int DEFAULT NULL,
  `ASNBin` tinyint DEFAULT '0',
  `byproduct_id` int DEFAULT NULL,
  `OriginalPalletID` varchar(50) DEFAULT NULL,
  `ASNContainer` tinyint DEFAULT '0',
  `idPallet` varchar(50) DEFAULT NULL,
  `BatchRecovery` tinyint DEFAULT '0',
  `DeletedDate` datetime DEFAULT NULL,
  `DeletedBy` int DEFAULT NULL,
  `RemovalCode` text,
  PRIMARY KEY (`CustomPalletID`),
  KEY `custompallettofacility_idx` (`FacilityID`),
  KEY `custompallettolocation_idx` (`LocationID`),
  KEY `custompallettocustompalletstatus_idx` (`StatusID`),
  KEY `custompallettodisposition_idx` (`disposition_id`),
  CONSTRAINT `custompallettocustompalletstatus` FOREIGN KEY (`StatusID`) REFERENCES `custompallet_status` (`StatusID`),
  CONSTRAINT `custompallettodisposition` FOREIGN KEY (`disposition_id`) REFERENCES `disposition` (`disposition_id`),
  CONSTRAINT `custompallettofacility` FOREIGN KEY (`FacilityID`) REFERENCES `facility` (`FacilityID`)
);


CREATE TABLE `custompallet_items` (
  `CustomPalletItemID` int NOT NULL AUTO_INCREMENT,
  `CustomPalletID` int DEFAULT NULL,
  `AssetScanID` bigint DEFAULT NULL,
  `DateCreated` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `InventoryID` int DEFAULT NULL,
  `status` int DEFAULT NULL,
  `ServerID` int DEFAULT NULL,
  `MediaID` int DEFAULT NULL,
  `RecoveryTypeID` int DEFAULT NULL,
  `UnserializedRecoveryRecordID` int DEFAULT NULL,
  `Quantity` int DEFAULT NULL,
  `byproduct_id` int DEFAULT NULL,
  PRIMARY KEY (`CustomPalletItemID`),
  KEY `cpitemstocp_idx` (`CustomPalletID`),
  KEY `cpitemstoasset_idx` (`AssetScanID`),
  KEY `custompallet_itemstoinventory_idx` (`InventoryID`),
  CONSTRAINT `custompallet_itemstocustompallet` FOREIGN KEY (`CustomPalletID`) REFERENCES `custompallet` (`CustomPalletID`),
  CONSTRAINT `custompallet_itemstoinventory` FOREIGN KEY (`InventoryID`) REFERENCES `inventory` (`InventoryID`)
);


CREATE TABLE `shipping_containers` (
  `ShippingContainerID` varchar(50) NOT NULL,
  `ShippingID` varchar(50) DEFAULT NULL,
  `idPackage` int DEFAULT NULL,
  `CustomID` varchar(50) DEFAULT NULL,
  `ContainerNotes` text,
  `SealID` varchar(100) DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `StatusID` int DEFAULT NULL,
  `ContainerWeight` int DEFAULT '0',
  `ShippingControllerLoginID` varchar(100) DEFAULT NULL,
  `PalletID` varchar(100) DEFAULT NULL,
  `FacilityID` int DEFAULT NULL,
  `LocationID` int DEFAULT NULL,
  `InspectionResult` varchar(100) DEFAULT NULL,
  `InspectionAuditDateTime` datetime DEFAULT NULL,
  `InspectionAuditBy` int DEFAULT NULL,
  `AuditControllerLoginID` varchar(100) DEFAULT NULL,
  `disposition_id` int DEFAULT NULL,
  `RecentSealDate` datetime DEFAULT NULL,
  `RecentSealBy` int DEFAULT NULL,
  `ShippingNotes` varchar(200) DEFAULT NULL,
  `PartTypeSummary` text,
  `RemovalCode` text,
  `QuarantineToActiveAuditController` varchar(100) DEFAULT NULL,
  `ExpectedServersCount` int DEFAULT NULL,
  `ScannedServersCount` int DEFAULT NULL,
  `container_added_to_shipment_time` datetime DEFAULT NULL,
  `ReferenceIDRequired` tinyint DEFAULT '0',
  `ReferenceID` varchar(500) DEFAULT NULL,
  `ReferenceTypeID` int DEFAULT NULL,
  `ReferenceType` varchar(100) DEFAULT NULL,
  `ASNContainer` tinyint DEFAULT '0',
  `idPallet` varchar(100) DEFAULT NULL,
  `BatchRecovery` tinyint DEFAULT '0',
  PRIMARY KEY (`ShippingContainerID`),
  KEY `shippingcontainerstoshipping_idx` (`ShippingID`),
  KEY `shippingcontainerstopackage_idx` (`idPackage`),
  CONSTRAINT `shippingcontainerstopackage` FOREIGN KEY (`idPackage`) REFERENCES `package` (`idPackage`),
  CONSTRAINT `shippingcontainerstoshipping` FOREIGN KEY (`ShippingID`) REFERENCES `shipping` (`ShippingID`)
);


CREATE TABLE `shipping_container_serials` (
  `SerialID` int NOT NULL AUTO_INCREMENT,
  `SerialNumber` varchar(100) DEFAULT NULL,
  `AssetScanID` bigint DEFAULT NULL,
  `Notes` text,
  `StatusID` int DEFAULT NULL,
  `CreatedDate` datetime DEFAULT NULL,
  `CreatedBy` int DEFAULT NULL,
  `UpdatedDate` datetime DEFAULT NULL,
  `UpdatedBy` int DEFAULT NULL,
  `ShippingContainerID` varchar(50) DEFAULT NULL,
  `UniversalModelNumber` varchar(100) DEFAULT NULL,
  `part_type` varchar(100) DEFAULT NULL,
  `InventoryID` int DEFAULT NULL,
  `InventorySerialNumber` varchar(100) DEFAULT NULL,
  `InventoryUniversalModelNumber` varchar(100) DEFAULT NULL,
  `SanitizationVerificationID` varchar(100) DEFAULT NULL,
  `ControllerLoginID` varchar(100) DEFAULT NULL,
  `ServerID` int DEFAULT NULL,
  `ServerSerialNumber` varchar(50) DEFAULT NULL,
  `byproduct_id` int DEFAULT NULL,
  `MediaID` int DEFAULT NULL,
  `MediaSerialNumber` varchar(50) DEFAULT NULL,
  `UnserializedRecoveryRecordID` int DEFAULT NULL,
  `Quantity` int DEFAULT '1',
  `FromCustomPalletID` int DEFAULT NULL,
  `FromBinName` varchar(99) DEFAULT NULL,
  `FromDispositionID` int DEFAULT NULL,
  `serial_scan_time` datetime DEFAULT NULL,
  `mpn_scan_time` datetime DEFAULT NULL,
  `part_type_scan_time` datetime DEFAULT NULL,
  `coo_scan_time` datetime DEFAULT NULL,
  `tpvr_request_scan_time` datetime DEFAULT NULL,
  `controller_scan_time` datetime DEFAULT NULL,
  `COO` varchar(100) DEFAULT NULL,
  `COOID` int DEFAULT NULL,
  `CustomPalletID` int DEFAULT NULL,
  `BinName` text,
  PRIMARY KEY (`SerialID`),
  KEY `shippingserialtoshippingcontainers_idx` (`ShippingContainerID`),
  KEY `shippingserialtoinventory_idx` (`InventoryID`),
  KEY `shippingserverserialindex` (`ServerSerialNumber`),
  CONSTRAINT `shippingserialtoinventory` FOREIGN KEY (`InventoryID`) REFERENCES `inventory` (`InventoryID`),
  CONSTRAINT `shippingserialtoshippingcontainers` FOREIGN KEY (`ShippingContainerID`) REFERENCES `shipping_containers` (`ShippingContainerID`)
);


//Status of Shipping Containers
1	Active
2	Approved
3	Shipped
4	Received
5	Declined
6	Closed
7	Quarantine

//Status of Custom Pallet
1	Active
2	InActive
3	Closed
4	AuditLocked
5	Deleted
6	Added to Shipment
7	Shipped