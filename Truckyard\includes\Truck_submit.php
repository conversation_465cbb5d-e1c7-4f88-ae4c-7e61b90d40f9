<?php
session_start();
include_once("../database/Truck.class.php");
$obj = new TruckClass();
	
	if($_POST['ajax'] == "GetParkingLocations"){
		$result = $obj->GetParkingLocations($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetCarriers"){
  		$result = $obj->GetCarriers($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckTypes"){
  		$result = $obj->GetTruckTypes($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "TruckSave") {
		$result = $obj->TruckSave($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetTruckDetails") {
		$result = $obj->GetTruckDetails($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "GetTruckList") {
		$result = $obj->GetTruckList($_POST);
		echo $result;
  	}
  	if($_POST['ajax'] == "DeleteTruck"){
		$result = $obj->DeleteTruck($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ChangeStatus"){
		$result = $obj->ChangeStatus($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateTruckListxls"){
  		$result = $obj->GenerateTruckListxls($_POST);
		echo $result;
	}


?>