<?php
	session_start();
	include_once("../database/pending_outbound_shipments.class.php");
	$obj = new PendingOutboundShipments();
	
	if($_POST['ajax'] == "GetPendingOutboundContainers") {
		$result = $obj->GetPendingOutboundContainers($_POST);
		echo $result;
	}

    if($_POST['ajax'] == "AddContainerToShipment1") {
		$result = $obj->AddContainerToShipment1($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ClosePendingContainer") {
		$result = $obj->ClosePendingContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "ReopenOutboundContainer") {
		$result = $obj->ReopenOutboundContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateShippingNotes") {
		$result = $obj->UpdateShippingNotes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateContainerLocation") {
		$result = $obj->UpdateContainerLocation($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "RemovePalletFromOutboundContainer") {
		$result = $obj->RemovePalletFromOutboundContainer($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "MoveContainerToPallet") {
		$result = $obj->MoveContainerToPallet($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "DeleteOutboundContainer") {
		$result = $obj->DeleteOutboundContainer($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateRemoveshipXLS") {
		$result = $obj->GenerateRemoveshipXLS($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "MakeContainerActive") {
		$result = $obj->MakeContainerActive($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "UpdateContainerLocationGroup") {
		$result = $obj->UpdateContainerLocationGroup($_POST);
		echo $result;
	}
?>