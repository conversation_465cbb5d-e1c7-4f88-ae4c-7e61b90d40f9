<?php
/**
 * Convert Single Shipment serial
 * Loops through each serial
 */

// Database connection
session_start();
include_once("../connection.php");
$obj1 = new Connection();
$connectionlink = Connection::DBConnect();

// Configuration
$systemUserId = 712; // System user ID for tracking

// $query = "SELECT se.*,c.CustomPalletID,c.BinName FROM shipping_container_serials se 
// left join shipping_containers sc on se.ShippingContainerID = sc.ShippingContainerID 
// left join custompallet c on sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = c.BinName COLLATE utf8mb3_unicode_ci
// where isnull(sc.ShippingID) and se.Completed = 0 limit 10000";


$query = "SELECT 
    a.*,
    c.CustomPalletID,
    c.BinName
FROM speed_server_recovery a
JOIN custompallet c 
    ON a.ShippingContainerID COLLATE utf8mb3_unicode_ci = c.BinName COLLATE utf8mb3_unicode_ci
WHERE a.StatusID = 8
  AND (ISNULL(a.CustomPalletID) OR a.CustomPalletID = '')";

$q = mysqli_query($connectionlink, $query);
if(mysqli_affected_rows($connectionlink) > 0) {
    while($row = mysqli_fetch_assoc($q)) {     
        echo "Processing Serial\n ".$row['ServerID']."<br>";
        $query1 = "update speed_server_recovery set CustomPalletID = '".$row['CustomPalletID']."' where ServerID = '".$row['ServerID']."'";
        $q1 = mysqli_query($connectionlink, $query1);
        if(mysqli_error($connectionlink)) {			
            echo mysqli_error($connectionlink)."<br>";
            continue;		
        }
    }

    echo "Loop Completed";
} else {
    echo "No Records";
}
?>
