<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
//$ProcessDatefrom = '2022-12-11';
//$ProcessDateto = '2022-12-12';
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'InventorySnapshot.'.$today.'.csv';

$csv = "entity_id,location_id,inventory_type,serial_id,mpn_id,part_type,source_type,manufacturer_id,bin_id,container_id,next_step_action,last_touch_datetime,operator_login_id,storage_location_group_id,coo_id,customer_id,classification_type,classification_code_id\n";//Column headers
//Assembly Recovery
$sql = "Select F.FacilityName as location_id,A.SerialNumber as serial_id,
A.UniversalModelNumber as mpn_id,A.part_type as part_type,SCT.Cumstomertype as source_type,AM.ManufacturerName as manufacturer_id,CP.BinName as bin_id,
A.ShippingContainerID as container_id,DA.disposition as next_step_action,A.DateUpdated,UA.UserName as operator_login_id,CPL.LocationName as pallet_id,
ACOO.COO as coo_id,ASCCL.LocationName as containerpallet_id,ACPLG.GroupName as storage_location_group_id,AWSSC.Customer as customer_id,
P.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,A.DateCreated

        FROM asset A 
        LEFT JOIN facility F ON F.FacilityID = A.FacilityID
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
        LEFT JOIN custompallet CP on CP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition DA on DA.disposition_id = A.disposition_id
        LEFT JOIN users UA on UA.UserId = A.CreatedBy
        LEFT JOIN location CPL on CPL.LocationID = CP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = CPL.GroupID
        LEFT JOIN shipping_containers ASCC on ASCC.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCCL on ASCCL.LocationID = ASCC.LocationID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.StatusID NOT IN (6,8) 
        and A.Recoverytypeid IN (Select Recoverytypeid from Recoverytype where Recoverytype = 'Assembly')
        group by A.SerialNumber";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    if($row['serial_id'] != '')
    {
        $row['inventory_type'] = 'Serialized';
    }
    else
    {
        $row['inventory_type'] = 'Unserialized';
    }
    if($row['DateUpdated'] != '')
    {
        $row['DateUpdated'] = date("Y-m-d H:i:s", strtotime($row['DateUpdated']));
    }
    else
    {
        $row['DateUpdated'] = '';
    }
    if($row['DateUpdated'] == '')
    {
        $row['DateUpdated'] = $row['DateCreated'];
    }
    $date1 = explode(" ",$row['DateUpdated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row['DateUpdated']));
    $row['location_id'] = str_replace(","," ",$row['location_id']);
	$row['inventory_type'] = str_replace(","," ",$row['inventory_type']);
	$row['serial_id'] = str_replace(","," ",$row['serial_id']);
    $row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
    $row['part_type'] = str_replace(","," ",$row['part_type']);
    $row['source_type'] = str_replace(","," ",$row['source_type']);
    $row['manufacturer_id'] = str_replace(","," ",$row['manufacturer_id']);
    $row['bin_id'] = str_replace(","," ",$row['bin_id']);
    $row['container_id'] = str_replace(","," ",$row['container_id']);
    $row['next_step_action'] = str_replace(","," ",$row['next_step_action']);
    $row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
    $row['storage_location_group_id'] = str_replace(","," ",$row['storage_location_group_id']);
    $row['coo_id'] = str_replace(","," ",$row['coo_id']);
    $row['customer_id'] = str_replace(","," ",$row['customer_id']);
    $row['classification_type'] = str_replace(","," ",$row['classification_type']);
    $row['classification_code_id'] = str_replace(","," ",$row['classification_code_id']);
    
    if($row['entity_id'] == '')
    {
        $row['entity_id'] = 'n/a';
    }
    if($row['location_id'] == '')
    {
        $row['location_id'] = 'n/a';
    }
    if($row['inventory_type'] == '')
    {
        $row['inventory_type'] = 'n/a';
    }
    if($row['serial_id'] == '')
    {
        $row['serial_id'] = 'n/a';
    }
    if($row['mpn_id'] == '')
    {
        $row['mpn_id'] = 'n/a';
    }
    if($row['part_type'] == '')
    {
        $row['part_type'] = 'n/a';
    }
    if($row['source_type'] == '')
    {
        $row['source_type'] = 'n/a';
    }
    if($row['manufacturer_id'] == '')
    {
        $row['manufacturer_id'] = 'n/a';
    }
    if($row['bin_id'] == '')
    {
        $row['bin_id'] = 'n/a';
    }
    if($row['container_id'] == '')
    {
        $row['container_id'] = 'n/a';
    }
    if($row['next_step_action'] == '')
    {
        $row['next_step_action'] = 'n/a';
    }
    if($row['operator_login_id'] == '')
    {
        $row['operator_login_id'] = 'n/a';
    }
    if($row['storage_location_group_id'] == '')
    {
        $row['storage_location_group_id'] = 'n/a';
    }
    if($row['coo_id'] == '')
    {
        $row['coo_id'] = 'n/a';
    }
    if($row['customer_id'] == '')
    {
        $row['customer_id'] = 'n/a';
    }
    if($row['classification_type'] == '')
    {
        $row['classification_type'] = 'n/a';
    }
    if($row['classification_code_id'] == '')
    {
        $row['classification_code_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row2  = array('eV-Disposition-1',$row['location_id'],$row['inventory_type'],$row['serial_id'],$row['mpn_id'],$row['part_type'],$row['source_type'],$row['manufacturer_id'],$row['bin_id'],$row['container_id'],$row['next_step_action'],$row['DateUpdated'],$row['operator_login_id'],$row['storage_location_group_id'],$row['coo_id'],$row['customer_id'],$row['classification_type'],$row['classification_code_id']);
    $rows[] = $row2;
}
//Assembly Recovery
$sql1 = "Select F.FacilityName as location_id,A.MediaSerialNumber as serial_id,
A.MediaMPN as mpn_id,A.MediaType as part_type,SCT.Cumstomertype as source_type,AM.ManufacturerName as manufacturer_id,CP.BinName as bin_id,
A.ShippingContainerID as container_id,DA.disposition as next_step_action,A.UpdatedDate,UA.UserName as operator_login_id,CPL.LocationName as pallet_id,
ACOO.COO as coo_id,ASCCL.LocationName as containerpallet_id,ACPLG.GroupName as storage_location_group_id,AWSSC.Customer as customer_id,
P.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,A.CreatedDate

        FROM speed_media_recovery A 
        LEFT JOIN facility F ON F.FacilityID = A.FacilityID
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN catlog_creation ACC ON ACC.mpn_id = A.MediaMPN
        LEFT JOIN manufacturer AM on AM.idManufacturer = ACC.idManufacturer
        LEFT JOIN custompallet CP on CP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition DA on DA.disposition_id = A.disposition_id
        LEFT JOIN users UA on UA.UserId = A.CreatedBy
        LEFT JOIN location CPL on CPL.LocationID = CP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = CPL.GroupID
        LEFT JOIN shipping_containers ASCC on ASCC.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCCL on ASCCL.LocationID = ASCC.LocationID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.StatusID NOT IN (5,6) 
        group by A.MediaSerialNumber";
$query1 = mysqli_query($connectionlink1,$sql1);
while($row1 = mysqli_fetch_assoc($query1))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    if($row1['serial_id'] != '')
    {
        $row1['inventory_type'] = 'Serialized';
    }
    else
    {
        $row1['inventory_type'] = 'Unserialized';
    }
    if($row1['UpdatedDate'] == '')
    {
        $row1['UpdatedDate'] = $row1['CreatedDate'];
    }
    if($row1['UpdatedDate'] != '')
    {
        $row1['UpdatedDate'] = date("Y-m-d H:i:s", strtotime($row1['UpdatedDate']));
    }
    else
    {
        $row1['UpdatedDate'] = '';
    }
    $date1 = explode(" ",$row1['UpdatedDate']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row1['UpdatedDate']));
    $row1['location_id'] = str_replace(","," ",$row1['location_id']);
	$row1['inventory_type'] = str_replace(","," ",$row1['inventory_type']);
	$row1['serial_id'] = str_replace(","," ",$row1['serial_id']);
    $row1['mpn_id'] = str_replace(","," ",$row1['mpn_id']);
    $row1['part_type'] = str_replace(","," ",$row1['part_type']);
    $row1['source_type'] = str_replace(","," ",$row1['source_type']);
    $row1['manufacturer_id'] = str_replace(","," ",$row1['manufacturer_id']);
    $row1['bin_id'] = str_replace(","," ",$row1['bin_id']);
    $row1['container_id'] = str_replace(","," ",$row1['container_id']);
    $row1['next_step_action'] = str_replace(","," ",$row1['next_step_action']);
    $row1['operator_login_id'] = str_replace(","," ",$row1['operator_login_id']);
    $row1['storage_location_group_id'] = str_replace(","," ",$row1['storage_location_group_id']);
    $row1['coo_id'] = str_replace(","," ",$row1['coo_id']);
    $row1['customer_id'] = str_replace(","," ",$row1['customer_id']);
    $row1['classification_type'] = str_replace(","," ",$row1['classification_type']);
    $row1['classification_code_id'] = str_replace(","," ",$row1['classification_code_id']);
    
    if($row1['entity_id'] == '')
    {
        $row1['entity_id'] = 'n/a';
    }
    if($row1['location_id'] == '')
    {
        $row1['location_id'] = 'n/a';
    }
    if($row1['inventory_type'] == '')
    {
        $row1['inventory_type'] = 'n/a';
    }
    if($row1['serial_id'] == '')
    {
        $row1['serial_id'] = 'n/a';
    }
    if($row1['mpn_id'] == '')
    {
        $row1['mpn_id'] = 'n/a';
    }
    if($row1['part_type'] == '')
    {
        $row1['part_type'] = 'n/a';
    }
    if($row1['source_type'] == '')
    {
        $row1['source_type'] = 'n/a';
    }
    if($row1['manufacturer_id'] == '')
    {
        $row1['manufacturer_id'] = 'n/a';
    }
    if($row1['bin_id'] == '')
    {
        $row1['bin_id'] = 'n/a';
    }
    if($row1['container_id'] == '')
    {
        $row1['container_id'] = 'n/a';
    }
    if($row1['next_step_action'] == '')
    {
        $row1['next_step_action'] = 'n/a';
    }
    if($row1['operator_login_id'] == '')
    {
        $row1['operator_login_id'] = 'n/a';
    }
    if($row1['storage_location_group_id'] == '')
    {
        $row1['storage_location_group_id'] = 'n/a';
    }
    if($row1['coo_id'] == '')
    {
        $row1['coo_id'] = 'n/a';
    }
    if($row1['customer_id'] == '')
    {
        $row1['customer_id'] = 'n/a';
    }
    if($row1['classification_type'] == '')
    {
        $row1['classification_type'] = 'n/a';
    }
    if($row1['classification_code_id'] == '')
    {
        $row1['classification_code_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row21  = array('eV-Disposition-1',$row1['location_id'],$row1['inventory_type'],$row1['serial_id'],$row1['mpn_id'],$row1['part_type'],$row1['source_type'],$row1['manufacturer_id'],$row1['bin_id'],$row1['container_id'],$row1['next_step_action'],$row1['UpdatedDate'],$row1['operator_login_id'],$row1['storage_location_group_id'],$row1['coo_id'],$row1['customer_id'],$row1['classification_type'],$row1['classification_code_id']);
    $rows[] = $row21;
}

//Rack Recovery
$sql2 = "Select F.FacilityName as location_id,A.ServerSerialNumber as serial_id,
A.MPN as mpn_id,A.Type as part_type,SCT.Cumstomertype as source_type,AM.ManufacturerName as manufacturer_id,CP.BinName as bin_id,
A.ShippingContainerID as container_id,DA.disposition as next_step_action,A.UpdatedDate,UA.UserName as operator_login_id,CPL.LocationName as pallet_id,
ACOO.COO as coo_id,ASCCL.LocationName as containerpallet_id,ACPLG.GroupName as storage_location_group_id,AWSSC.Customer as customer_id,
P.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,A.CreatedDate

        FROM speed_server_recovery A 
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN facility F ON F.FacilityID = P.PalletFacilityID
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN catlog_creation ACC ON ACC.mpn_id = A.MPN
        LEFT JOIN manufacturer AM on AM.idManufacturer = ACC.idManufacturer
        LEFT JOIN custompallet CP on CP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition DA on DA.disposition_id = A.disposition_id
        LEFT JOIN users UA on UA.UserId = A.CreatedBy
        LEFT JOIN location CPL on CPL.LocationID = CP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = CPL.GroupID
        LEFT JOIN shipping_containers ASCC on ASCC.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCCL on ASCCL.LocationID = ASCC.LocationID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.StatusID NOT IN (5,6) 
        group by A.ServerSerialNumber";
$query2 = mysqli_query($connectionlink1,$sql2);
while($row2 = mysqli_fetch_assoc($query2))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    if($row2['serial_id'] != '')
    {
        $row2['inventory_type'] = 'Serialized';
    }
    else
    {
        $row2['inventory_type'] = 'Unserialized';
    }
    if($row2['UpdatedDate'] == '')
    {
        $row2['UpdatedDate'] = $row2['CreatedDate'];
    }
    if($row2['UpdatedDate'] != '')
    {
        $row2['UpdatedDate'] = date("Y-m-d H:i:s", strtotime($row2['UpdatedDate']));
    }
    else
    {
        $row2['UpdatedDate'] = '';
    }
    $date1 = explode(" ",$row2['UpdatedDate']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row2['UpdatedDate']));
    $row2['location_id'] = str_replace(","," ",$row2['location_id']);
	$row2['inventory_type'] = str_replace(","," ",$row2['inventory_type']);
	$row2['serial_id'] = str_replace(","," ",$row2['serial_id']);
    $row2['mpn_id'] = str_replace(","," ",$row2['mpn_id']);
    $row2['part_type'] = str_replace(","," ",$row2['part_type']);
    $row2['source_type'] = str_replace(","," ",$row2['source_type']);
    $row2['manufacturer_id'] = str_replace(","," ",$row2['manufacturer_id']);
    $row2['bin_id'] = str_replace(","," ",$row2['bin_id']);
    $row2['container_id'] = str_replace(","," ",$row2['container_id']);
    $row2['next_step_action'] = str_replace(","," ",$row2['next_step_action']);
    $row2['operator_login_id'] = str_replace(","," ",$row2['operator_login_id']);
    $row2['storage_location_group_id'] = str_replace(","," ",$row2['storage_location_group_id']);
    $row2['coo_id'] = str_replace(","," ",$row2['coo_id']);
    $row2['customer_id'] = str_replace(","," ",$row2['customer_id']);
    $row2['classification_type'] = str_replace(","," ",$row2['classification_type']);
    $row2['classification_code_id'] = str_replace(","," ",$row2['classification_code_id']);
    
    if($row2['entity_id'] == '')
    {
        $row2['entity_id'] = 'n/a';
    }
    if($row2['location_id'] == '')
    {
        $row2['location_id'] = 'n/a';
    }
    if($row2['inventory_type'] == '')
    {
        $row2['inventory_type'] = 'n/a';
    }
    if($row2['serial_id'] == '')
    {
        $row2['serial_id'] = 'n/a';
    }
    if($row2['mpn_id'] == '')
    {
        $row2['mpn_id'] = 'n/a';
    }
    if($row2['part_type'] == '')
    {
        $row2['part_type'] = 'n/a';
    }
    if($row2['source_type'] == '')
    {
        $row2['source_type'] = 'n/a';
    }
    if($row2['manufacturer_id'] == '')
    {
        $row2['manufacturer_id'] = 'n/a';
    }
    if($row2['bin_id'] == '')
    {
        $row2['bin_id'] = 'n/a';
    }
    if($row2['container_id'] == '')
    {
        $row2['container_id'] = 'n/a';
    }
    if($row2['next_step_action'] == '')
    {
        $row2['next_step_action'] = 'n/a';
    }
    if($row2['operator_login_id'] == '')
    {
        $row2['operator_login_id'] = 'n/a';
    }
    if($row2['storage_location_group_id'] == '')
    {
        $row2['storage_location_group_id'] = 'n/a';
    }
    if($row2['coo_id'] == '')
    {
        $row2['coo_id'] = 'n/a';
    }
    if($row2['customer_id'] == '')
    {
        $row2['customer_id'] = 'n/a';
    }
    if($row2['classification_type'] == '')
    {
        $row2['classification_type'] = 'n/a';
    }
    if($row2['classification_code_id'] == '')
    {
        $row2['classification_code_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row22  = array('eV-Disposition-1',$row2['location_id'],$row2['inventory_type'],$row2['serial_id'],$row2['mpn_id'],$row2['part_type'],$row2['source_type'],$row2['manufacturer_id'],$row2['bin_id'],$row2['container_id'],$row2['next_step_action'],$row2['UpdatedDate'],$row2['operator_login_id'],$row2['storage_location_group_id'],$row2['coo_id'],$row2['customer_id'],$row2['classification_type'],$row2['classification_code_id']);
    $rows[] = $row22;
}
//Component recovery
$sql3 = "Select F.FacilityName as location_id,A.SerialNumber as serial_id,
A.UniversalModelNumber as mpn_id,A.part_type as part_type,SCT.Cumstomertype as source_type,AM.ManufacturerName as manufacturer_id,CP.BinName as bin_id,
A.ShippingContainerID as container_id,DA.disposition as next_step_action,A.DateUpdated,UA.UserName as operator_login_id,CPL.LocationName as pallet_id,
ACOO.COO as coo_id,ASCCL.LocationName as containerpallet_id,ACPLG.GroupName as storage_location_group_id,AWSSC.Customer as customer_id,
P.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,A.DateCreated

        FROM asset A 
        LEFT JOIN facility F ON F.FacilityID = A.FacilityID
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
        LEFT JOIN custompallet CP on CP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition DA on DA.disposition_id = A.disposition_id
        LEFT JOIN users UA on UA.UserId = A.CreatedBy
        LEFT JOIN location CPL on CPL.LocationID = CP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = CPL.GroupID
        LEFT JOIN shipping_containers ASCC on ASCC.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCCL on ASCCL.LocationID = ASCC.LocationID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.StatusID NOT IN (6,8) 
        and A.Recoverytypeid IN (Select Recoverytypeid from Recoverytype where Recoverytype = 'Component')
        group by A.SerialNumber";
$query3 = mysqli_query($connectionlink1,$sql3);
while($row3 = mysqli_fetch_assoc($query3))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    if($row3['serial_id'] != '')
    {
        $row3['inventory_type'] = 'Serialized';
    }
    else
    {
        $row3['inventory_type'] = 'Unserialized';
    }
    if($row3['DateUpdated'] == '')
    {
        $row3['DateUpdated'] = $row3['DateCreated'];
    }
    if($row3['DateUpdated'] != '')
    {
        $row3['DateUpdated'] = date("Y-m-d H:i:s", strtotime($row3['DateUpdated']));
    }
    else
    {
        $row3['DateUpdated'] = '';
    }
    $date1 = explode(" ",$row3['DateUpdated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row3['DateUpdated']));
    $row3['location_id'] = str_replace(","," ",$row3['location_id']);
	$row3['inventory_type'] = str_replace(","," ",$row3['inventory_type']);
	$row3['serial_id'] = str_replace(","," ",$row3['serial_id']);
    $row3['mpn_id'] = str_replace(","," ",$row3['mpn_id']);
    $row3['part_type'] = str_replace(","," ",$row3['part_type']);
    $row3['source_type'] = str_replace(","," ",$row3['source_type']);
    $row3['manufacturer_id'] = str_replace(","," ",$row3['manufacturer_id']);
    $row3['bin_id'] = str_replace(","," ",$row3['bin_id']);
    $row3['container_id'] = str_replace(","," ",$row3['container_id']);
    $row3['next_step_action'] = str_replace(","," ",$row3['next_step_action']);
    $row3['operator_login_id'] = str_replace(","," ",$row3['operator_login_id']);
    $row3['storage_location_group_id'] = str_replace(","," ",$row3['storage_location_group_id']);
    $row3['coo_id'] = str_replace(","," ",$row3['coo_id']);
    $row3['customer_id'] = str_replace(","," ",$row3['customer_id']);
    $row3['classification_type'] = str_replace(","," ",$row3['classification_type']);
    $row3['classification_code_id'] = str_replace(","," ",$row3['classification_code_id']);
    
    if($row3['entity_id'] == '')
    {
        $row3['entity_id'] = 'n/a';
    }
    if($row3['location_id'] == '')
    {
        $row3['location_id'] = 'n/a';
    }
    if($row3['inventory_type'] == '')
    {
        $row3['inventory_type'] = 'n/a';
    }
    if($row3['serial_id'] == '')
    {
        $row3['serial_id'] = 'n/a';
    }
    if($row3['mpn_id'] == '')
    {
        $row3['mpn_id'] = 'n/a';
    }
    if($row3['part_type'] == '')
    {
        $row3['part_type'] = 'n/a';
    }
    if($row3['source_type'] == '')
    {
        $row3['source_type'] = 'n/a';
    }
    if($row3['manufacturer_id'] == '')
    {
        $row3['manufacturer_id'] = 'n/a';
    }
    if($row3['bin_id'] == '')
    {
        $row3['bin_id'] = 'n/a';
    }
    if($row3['container_id'] == '')
    {
        $row3['container_id'] = 'n/a';
    }
    if($row3['next_step_action'] == '')
    {
        $row3['next_step_action'] = 'n/a';
    }
    if($row3['operator_login_id'] == '')
    {
        $row3['operator_login_id'] = 'n/a';
    }
    if($row3['storage_location_group_id'] == '')
    {
        $row3['storage_location_group_id'] = 'n/a';
    }
    if($row3['coo_id'] == '')
    {
        $row3['coo_id'] = 'n/a';
    }
    if($row3['customer_id'] == '')
    {
        $row3['customer_id'] = 'n/a';
    }
    if($row3['classification_type'] == '')
    {
        $row3['classification_type'] = 'n/a';
    }
    if($row3['classification_code_id'] == '')
    {
        $row3['classification_code_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row23  = array('eV-Disposition-1',$row3['location_id'],$row3['inventory_type'],$row3['serial_id'],$row3['mpn_id'],$row3['part_type'],$row3['source_type'],$row3['manufacturer_id'],$row3['bin_id'],$row3['container_id'],$row3['next_step_action'],$row3['DateUpdated'],$row3['operator_login_id'],$row3['storage_location_group_id'],$row3['coo_id'],$row3['customer_id'],$row3['classification_type'],$row3['classification_code_id']);
    $rows[] = $row23;
}

$sql4 = "Select F.FacilityName as location_id,NULL as serial_id,
NULL as mpn_id,PA.parttype as part_type,SCT.Cumstomertype as source_type,NULL as manufacturer_id,CP.BinName as bin_id,
A.ShippingContainerID as container_id,DA.disposition as next_step_action,A.ModifiedDate,UA.UserName as operator_login_id,CPL.LocationName as pallet_id,
ACOO.COO as coo_id,ASCCL.LocationName as containerpallet_id,ACPLG.GroupName as storage_location_group_id,AWSSC.Customer as customer_id,
P.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,A.CreatedDate
        FROM unserialized_recovery_records A 
        LEFT JOIN facility F ON F.FacilityID = A.FacilityID
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN custompallet CP on CP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition DA on DA.disposition_id = A.DispositionID
        LEFT JOIN parttype PA on PA.parttypeid = A.parttypeid
        LEFT JOIN users UA on UA.UserId = A.ModifiedBy
        LEFT JOIN location CPL on CPL.LocationID = CP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = CPL.GroupID
        LEFT JOIN shipping_containers ASCC on ASCC.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCCL on ASCCL.LocationID = ASCC.LocationID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.StatusID NOT IN (6,8)";
$query4 = mysqli_query($connectionlink1,$sql4);
while($row4 = mysqli_fetch_assoc($query4))
{
    /*if($row['FacilityName'] == 'CVG110')
    {
        $row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
    }
    else if($row['FacilityName'] == 'DUB210')
    {
        $row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
    }
    else if($row['FacilityName'] == 'SIN100')
    {
        $row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
    }*/
    if($row4['serial_id'] != '')
    {
        $row4['inventory_type'] = 'Serialized';
    }
    else
    {
        $row4['inventory_type'] = 'Unserialized';
    }
    if($row4['ModifiedDate'] == '')
    {
        $row4['ModifiedDate'] = $row4['CreatedDate'];
    }
    if($row4['ModifiedDate'] != '')
    {
        $row4['ModifiedDate'] = date("Y-m-d H:i:s", strtotime($row4['ModifiedDate']));
    }
    else
    {
        $row4['ModifiedDate'] = '';
    }
    $date1 = explode(" ",$row4['ModifiedDate']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row4['ModifiedDate']));
    $row4['location_id'] = str_replace(","," ",$row4['location_id']);
    $row4['inventory_type'] = str_replace(","," ",$row4['inventory_type']);
    $row4['serial_id'] = str_replace(","," ",$row4['serial_id']);
    $row4['mpn_id'] = str_replace(","," ",$row4['mpn_id']);
    $row4['part_type'] = str_replace(","," ",$row4['part_type']);
    $row4['source_type'] = str_replace(","," ",$row4['source_type']);
    $row4['manufacturer_id'] = str_replace(","," ",$row4['manufacturer_id']);
    $row4['bin_id'] = str_replace(","," ",$row4['bin_id']);
    $row4['container_id'] = str_replace(","," ",$row4['container_id']);
    $row4['next_step_action'] = str_replace(","," ",$row4['next_step_action']);
    $row4['operator_login_id'] = str_replace(","," ",$row4['operator_login_id']);
    $row4['storage_location_group_id'] = str_replace(","," ",$row4['storage_location_group_id']);
    $row4['coo_id'] = str_replace(","," ",$row4['coo_id']);
    $row4['customer_id'] = str_replace(","," ",$row4['customer_id']);
    $row4['classification_type'] = str_replace(","," ",$row4['classification_type']);
    $row4['classification_code_id'] = str_replace(","," ",$row4['classification_code_id']);
    
    if($row4['entity_id'] == '')
    {
        $row4['entity_id'] = 'n/a';
    }
    if($row4['location_id'] == '')
    {
        $row4['location_id'] = 'n/a';
    }
    if($row4['inventory_type'] == '')
    {
        $row4['inventory_type'] = 'n/a';
    }
    if($row4['serial_id'] == '')
    {
        $row4['serial_id'] = 'n/a';
    }
    if($row4['mpn_id'] == '')
    {
        $row4['mpn_id'] = 'n/a';
    }
    if($row4['part_type'] == '')
    {
        $row4['part_type'] = 'n/a';
    }
    if($row4['source_type'] == '')
    {
        $row4['source_type'] = 'n/a';
    }
    if($row4['manufacturer_id'] == '')
    {
        $row4['manufacturer_id'] = 'n/a';
    }
    if($row4['bin_id'] == '')
    {
        $row4['bin_id'] = 'n/a';
    }
    if($row4['container_id'] == '')
    {
        $row4['container_id'] = 'n/a';
    }
    if($row4['next_step_action'] == '')
    {
        $row4['next_step_action'] = 'n/a';
    }
    if($row4['operator_login_id'] == '')
    {
        $row4['operator_login_id'] = 'n/a';
    }
    if($row4['storage_location_group_id'] == '')
    {
        $row4['storage_location_group_id'] = 'n/a';
    }
    if($row4['coo_id'] == '')
    {
        $row4['coo_id'] = 'n/a';
    }
    if($row4['customer_id'] == '')
    {
        $row4['customer_id'] = 'n/a';
    }
    if($row4['classification_type'] == '')
    {
        $row4['classification_type'] = 'n/a';
    }
    if($row4['classification_code_id'] == '')
    {
        $row4['classification_code_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row24  = array('eV-Disposition-1',$row4['location_id'],$row4['inventory_type'],$row4['serial_id'],$row4['mpn_id'],$row4['part_type'],$row4['source_type'],$row4['manufacturer_id'],$row4['bin_id'],$row4['container_id'],$row4['next_step_action'],$row4['ModifiedDate'],$row4['operator_login_id'],$row4['storage_location_group_id'],$row4['coo_id'],$row4['customer_id'],$row4['classification_type'],$row4['classification_code_id']);
    $rows[] = $row24;
}
//Container Recovery
$sql5 = "Select F.FacilityName as location_id,A.SerialNumber as serial_id,
A.UniversalModelNumber as mpn_id,A.part_type as part_type,SCT.Cumstomertype as source_type,AM.ManufacturerName as manufacturer_id,CP.BinName as bin_id,
A.ShippingContainerID as container_id,DA.disposition as next_step_action,A.DateUpdated,UA.UserName as operator_login_id,CPL.LocationName as pallet_id,
ACOO.COO as coo_id,ASCCL.LocationName as containerpallet_id,ACPLG.GroupName as storage_location_group_id,AWSSC.Customer as customer_id,
P.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,A.DateCreated

        FROM asset A 
        LEFT JOIN facility F ON F.FacilityID = A.FacilityID
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
        LEFT JOIN custompallet CP on CP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition DA on DA.disposition_id = A.disposition_id
        LEFT JOIN users UA on UA.UserId = A.CreatedBy
        LEFT JOIN location CPL on CPL.LocationID = CP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = CPL.GroupID
        LEFT JOIN shipping_containers ASCC on ASCC.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCCL on ASCCL.LocationID = ASCC.LocationID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.StatusID NOT IN (6,8) 
        and A.Recoverytypeid IN (Select Recoverytypeid from Recoverytype where Recoverytype = 'Container')
        group by A.SerialNumber";
$query5 = mysqli_query($connectionlink1,$sql5);
while($row5 = mysqli_fetch_assoc($query5))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    if($row5['serial_id'] != '')
    {
        $row5['inventory_type'] = 'Serialized';
    }
    else
    {
        $row5['inventory_type'] = 'Unserialized';
    }
    if($row5['DateUpdated'] == '')
    {
        $row5['DateUpdated'] = $row5['DateCreated'];
    }
    if($row5['DateUpdated'] != '')
    {
        $row5['DateUpdated'] = date("Y-m-d H:i:s", strtotime($row5['DateUpdated']));
    }
    else
    {
        $row5['DateUpdated'] = '';
    }
    $date1 = explode(" ",$row5['DateUpdated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row5['DateUpdated']));
    $row5['location_id'] = str_replace(","," ",$row5['location_id']);
	$row5['inventory_type'] = str_replace(","," ",$row5['inventory_type']);
	$row5['serial_id'] = str_replace(","," ",$row5['serial_id']);
    $row5['mpn_id'] = str_replace(","," ",$row5['mpn_id']);
    $row5['part_type'] = str_replace(","," ",$row5['part_type']);
    $row5['source_type'] = str_replace(","," ",$row5['source_type']);
    $row5['manufacturer_id'] = str_replace(","," ",$row5['manufacturer_id']);
    $row5['bin_id'] = str_replace(","," ",$row5['bin_id']);
    $row5['container_id'] = str_replace(","," ",$row5['container_id']);
    $row5['next_step_action'] = str_replace(","," ",$row5['next_step_action']);
    $row5['operator_login_id'] = str_replace(","," ",$row5['operator_login_id']);
    $row5['storage_location_group_id'] = str_replace(","," ",$row5['storage_location_group_id']);
    $row5['coo_id'] = str_replace(","," ",$row5['coo_id']);
    $row5['customer_id'] = str_replace(","," ",$row5['customer_id']);
    $row5['classification_type'] = str_replace(","," ",$row5['classification_type']);
    $row5['classification_code_id'] = str_replace(","," ",$row5['classification_code_id']);
    
    if($row5['entity_id'] == '')
    {
        $row5['entity_id'] = 'n/a';
    }
    if($row5['location_id'] == '')
    {
        $row5['location_id'] = 'n/a';
    }
    if($row5['inventory_type'] == '')
    {
        $row5['inventory_type'] = 'n/a';
    }
    if($row5['serial_id'] == '')
    {
        $row5['serial_id'] = 'n/a';
    }
    if($row5['mpn_id'] == '')
    {
        $row5['mpn_id'] = 'n/a';
    }
    if($row5['part_type'] == '')
    {
        $row5['part_type'] = 'n/a';
    }
    if($row5['source_type'] == '')
    {
        $row5['source_type'] = 'n/a';
    }
    if($row5['manufacturer_id'] == '')
    {
        $row5['manufacturer_id'] = 'n/a';
    }
    if($row5['bin_id'] == '')
    {
        $row5['bin_id'] = 'n/a';
    }
    if($row5['container_id'] == '')
    {
        $row5['container_id'] = 'n/a';
    }
    if($row5['next_step_action'] == '')
    {
        $row5['next_step_action'] = 'n/a';
    }
    if($row5['operator_login_id'] == '')
    {
        $row5['operator_login_id'] = 'n/a';
    }
    if($row5['storage_location_group_id'] == '')
    {
        $row5['storage_location_group_id'] = 'n/a';
    }
    if($row5['coo_id'] == '')
    {
        $row5['coo_id'] = 'n/a';
    }
    if($row5['customer_id'] == '')
    {
        $row5['customer_id'] = 'n/a';
    }
    if($row5['classification_type'] == '')
    {
        $row5['classification_type'] = 'n/a';
    }
    if($row5['classification_code_id'] == '')
    {
        $row5['classification_code_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row25  = array('eV-Disposition-1',$row5['location_id'],$row5['inventory_type'],$row5['serial_id'],$row5['mpn_id'],$row5['part_type'],$row5['source_type'],$row5['manufacturer_id'],$row5['bin_id'],$row5['container_id'],$row5['next_step_action'],$row5['DateUpdated'],$row5['operator_login_id'],$row5['storage_location_group_id'],$row5['coo_id'],$row5['customer_id'],$row5['classification_type'],$row5['classification_code_id']);
    $rows[] = $row25;
}

//with out recovery type
$sql44 = "Select F.FacilityName as location_id,A.SerialNumber as serial_id,
A.UniversalModelNumber as mpn_id,A.part_type as part_type,SCT.Cumstomertype as source_type,AM.ManufacturerName as manufacturer_id,CP.BinName as bin_id,
A.ShippingContainerID as container_id,DA.disposition as next_step_action,A.DateUpdated,UA.UserName as operator_login_id,CPL.LocationName as pallet_id,
ACOO.COO as coo_id,ASCCL.LocationName as containerpallet_id,ACPLG.GroupName as storage_location_group_id,AWSSC.Customer as customer_id,
P.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,A.DateCreated

        FROM asset A 
        LEFT JOIN facility F ON F.FacilityID = A.FacilityID
        LEFT JOIN pallets P on P.idPallet = A.idPallet
        LEFT JOIN loads L on L.LoadId = P.LoadId
        LEFT JOIN customer SC on SC.CustomerID = P.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = P.idCustomertype
        LEFT JOIN manufacturer AM on AM.idManufacturer = A.idManufacturer
        LEFT JOIN custompallet CP on CP.CustomPalletID = A.CustomPalletID
        LEFT JOIN disposition DA on DA.disposition_id = A.disposition_id
        LEFT JOIN users UA on UA.UserId = A.CreatedBy
        LEFT JOIN location CPL on CPL.LocationID = CP.LocationID
        LEFT JOIN location_group ACPLG ON ACPLG.GroupID = CPL.GroupID
        LEFT JOIN shipping_containers ASCC on ASCC.ShippingContainerID = A.ShippingContainerID
        LEFT JOIN location ASCCL on ASCCL.LocationID = ASCC.LocationID
        LEFT JOIN COO ACOO ON ACOO.COOID = A.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE A.StatusID NOT IN (6,8) 
        and A.Recoverytypeid IS NULL
        group by A.SerialNumber";
$query44 = mysqli_query($connectionlink1,$sql44);
while($row44 = mysqli_fetch_assoc($query44))
{
    /*if($row44['FacilityName'] == 'CVG110')
	{
		$row44['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row44['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row44['FacilityName'] == 'DUB210')
	{
		$row44['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row44['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row44['FacilityName'] == 'SIN100')
	{
		$row44['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row44['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    if($row44['serial_id'] != '')
    {
        $row44['inventory_type'] = 'Serialized';
    }
    else
    {
        $row44['inventory_type'] = 'Unserialized';
    }
    if($row44['DateUpdated'] != '')
    {
        $row44['DateUpdated'] = date("Y-m-d H:i:s", strtotime($row44['DateUpdated']));
    }
    else
    {
        $row44['DateUpdated'] = '';
    }
    if($row44['DateUpdated'] == '')
    {
        $row44['DateUpdated'] = $row44['DateCreated'];
    }
    $date1 = explode(" ",$row44['DateUpdated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row44['DateUpdated']));
    $row44['location_id'] = str_replace(","," ",$row44['location_id']);
	$row44['inventory_type'] = str_replace(","," ",$row44['inventory_type']);
	$row44['serial_id'] = str_replace(","," ",$row44['serial_id']);
    $row44['mpn_id'] = str_replace(","," ",$row44['mpn_id']);
    $row44['part_type'] = str_replace(","," ",$row44['part_type']);
    $row44['source_type'] = str_replace(","," ",$row44['source_type']);
    $row44['manufacturer_id'] = str_replace(","," ",$row44['manufacturer_id']);
    $row44['bin_id'] = str_replace(","," ",$row44['bin_id']);
    $row44['container_id'] = str_replace(","," ",$row44['container_id']);
    $row44['next_step_action'] = str_replace(","," ",$row44['next_step_action']);
    $row44['operator_login_id'] = str_replace(","," ",$row44['operator_login_id']);
    $row44['storage_location_group_id'] = str_replace(","," ",$row44['storage_location_group_id']);
    $row44['coo_id'] = str_replace(","," ",$row44['coo_id']);
    $row44['customer_id'] = str_replace(","," ",$row44['customer_id']);
    $row44['classification_type'] = str_replace(","," ",$row44['classification_type']);
    $row44['classification_code_id'] = str_replace(","," ",$row44['classification_code_id']);
    
    if($row44['entity_id'] == '')
    {
        $row44['entity_id'] = 'n/a';
    }
    if($row44['location_id'] == '')
    {
        $row44['location_id'] = 'n/a';
    }
    if($row44['inventory_type'] == '')
    {
        $row44['inventory_type'] = 'n/a';
    }
    if($row44['serial_id'] == '')
    {
        $row44['serial_id'] = 'n/a';
    }
    if($row44['mpn_id'] == '')
    {
        $row44['mpn_id'] = 'n/a';
    }
    if($row44['part_type'] == '')
    {
        $row44['part_type'] = 'n/a';
    }
    if($row44['source_type'] == '')
    {
        $row44['source_type'] = 'n/a';
    }
    if($row44['manufacturer_id'] == '')
    {
        $row44['manufacturer_id'] = 'n/a';
    }
    if($row44['bin_id'] == '')
    {
        $row44['bin_id'] = 'n/a';
    }
    if($row44['container_id'] == '')
    {
        $row44['container_id'] = 'n/a';
    }
    if($row44['next_step_action'] == '')
    {
        $row44['next_step_action'] = 'n/a';
    }
    if($row44['operator_login_id'] == '')
    {
        $row44['operator_login_id'] = 'n/a';
    }
    if($row44['storage_location_group_id'] == '')
    {
        $row44['storage_location_group_id'] = 'n/a';
    }
    if($row44['coo_id'] == '')
    {
        $row44['coo_id'] = 'n/a';
    }
    if($row44['customer_id'] == '')
    {
        $row44['customer_id'] = 'n/a';
    }
    if($row44['classification_type'] == '')
    {
        $row44['classification_type'] = 'n/a';
    }
    if($row44['classification_code_id'] == '')
    {
        $row44['classification_code_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row244  = array('eV-Disposition-1',$row44['location_id'],$row44['inventory_type'],$row44['serial_id'],$row44['mpn_id'],$row44['part_type'],$row44['source_type'],$row44['manufacturer_id'],$row44['bin_id'],$row44['container_id'],$row44['next_step_action'],$row44['DateUpdated'],$row44['operator_login_id'],$row44['storage_location_group_id'],$row44['coo_id'],$row44['customer_id'],$row44['classification_type'],$row44['classification_code_id']);
    $rows[] = $row244;
}
foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>