<?php
/**
 * Convert Single Shipment serial
 * Loops through each serial
 */

// Database connection
session_start();
include_once("../connection.php");
$obj1 = new Connection();
$connectionlink = Connection::DBConnect();

// Configuration
$systemUserId = 712; // System user ID for tracking

// $query = "SELECT se.*,c.CustomPalletID,c.BinName FROM shipping_container_serials se 
// left join shipping_containers sc on se.ShippingContainerID = sc.ShippingContainerID 
// left join custompallet c on sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = c.BinName COLLATE utf8mb3_unicode_ci
// where isnull(sc.ShippingID) and se.Completed = 0 limit 10000";


$query = 'SELECT se.*,c.CustomPalletID,c.BinName FROM shipping_container_serials se 
left join shipping_containers sc on se.ShippingContainerID = sc.ShippingContainerID 
left join custompallet c on sc.ShippingContainerID COLLATE utf8mb3_unicode_ci = c.BinName COLLATE utf8mb3_unicode_ci
where se.ShippingContainerID in ("B12851173","SPEED-FA-OPTICS-HP-WK24","SPEED-FA-OPTICS-WK24","B7854834","B7854836","B7824550","B7824551","B7824552","B7824553","B7824554","B7824555","B7824556","B9097372","B9097373","B9097374","B9097375","B9097376","B9097377","B9097378","B9100638","B9100639","B9100640","B9100641","B9100642","B9100643","B9100644","B9100645","B9100646","B9100647","B9100648","B9100649","B9100650","B9100664","B9040062","B7929606","B7929607","B7929608","B7929609","B7929610","B7929611","B7929612","B7929613","B7929615","B8766955","B9095689","B13202881","B13202882","B13202883","B13202884","B13202885","B13202886","B13202887","B13202888","B13202889","B13202890","B13202891","B13202892","B13202893","B13202894","B13202895","B13202896","B13202897","B13202898","B13202899","B13202900","B13202901","B13202902","B13202903","B13202904","B13202905","B13202906","B13202907","B13202908","B13202909","B13202910","B13202911","B13202912","B13202913","B13202914","B13202915","B13202956","B13202957","B13202958","B13202959","B13202960","B13202961","B13202962","B13202963","B13202964","B13202965","B13202966","B13202967","B13202968","B13202969","B13202970","B13202971","B13202972","B13202973","B13202974","B13202975","B13202976","B13202977","B13202978","B13202979","B13202980","B13202981","B13202982","B13202983","B13202984","B13202985","B13202986","B13202987","B13202988","B13202989","B13202990","B13202992","B13202993","B13202994","B13202995","B13202996","B13202997","B13202998","B13202999","B13203000","B13203001","B13203002","B13203003","B13203004","B13203005","B13203006","B13203007","B13203008","B13203009","B13203010","B13203011","B13203012","B13203013","B13203014","B13203015","B13203016","B13203017","B13203018","B13203019","B13203020","B13203021","B13203022","B13203023","B13203024","B13203025","B13203026","B13203027","B13203028","B13203029","B13203030","B13203031","B13203032","B13203033","B13203034","B13203035","B13203036","B13203037","B13203038","B13203039","B13203040","B13203041","B13203042","B13203043","B13203044","B13203045","B13203046","B13203047","B13203048","B13203049","B13203050","B13203051","B13203053","B13203054","B13203055","B13203056","B13203058","B13203161","B13203162","B13203163","B13203164","B13203165","B13203166","B13203167","B13203168","B13203169","B13203170","B13203171","B13203172","B13203173","B13203174","B13203175","B13203176","B13203177","B13203178","B13203179","B13203180","B13203181","B13203182","B13203183","B13203184","B13203185","B13203186","B13203187","B13203188","B13203189","B13203190","B13203191","B13203192","B13203193","B13203194","B13203195","B13203196","B13203197","B13203198","B13203199","B13203200","B13203201","B13203202","B13203203","B13203204","B13203205","B13203206","B13203207","B13203208","B13203209","B13203210","B13203211","B13203212","B13203213","B13203214","B13203215","B13203216","B13203217","B13203218","B13203219","B13203220","B13203221","B13203222","B13203223","B13203224","B13203225","B13203226","B13203227","B13203228","B13203229","B13203230","B13203231","B13203232","B13203233","B13203234","B13203235","B13203236","B13203237","B13203238","B13203239","B13203240","B13203241","B13203242","B13203243","B13203244","B13203245","B13203246","B13203247","B13203248","B13203249","B13203250","B13203251","B13203252","B13203253","B13203254","B13203255","B13203256","B13203257","B13203258","B13203259","B13203260","B13203261","B13203262","B13203263","B13203264","B13203265","B13203266","B13203267","B13203268","B13203269","B13203270","B13203271","B13203272","B13203273","B13203274","B13203275","B13203321","B13203322","B13203323","B13203324","B13203325","B13203326","B13203327","B13203328","B13203329","B13203330","B13203331","B13203332","B13203333","B13203334","B13203335","B13203336","B13203337","B13203338","B13203339","B13203340","B13203341","B13203342","B13203343","B13203344","B13203345","B13203346","B13203347","B13203348","B13203349","B13203350","B13203351","B13203352","B13203353","B13203355","B13203356","B13203357","B13203358","B13203359","B13203360","B13203361","B13203362","B13203363","B13203364","B13203365","B13203367","B13203368","B13203369","B13203370","B13203371","B13203372","B13203373","B13203374","B13203375","B13203376","B13203377","B13203378","B13203379","B13203380","B13203381","B13203382","B13203383","B13203384","B13203385","B13203386","B13203387","B13203388","B13203389","B13203390","B13203391","B13203392","B13203393","B13203394","B13203395","B13203396","B13203397","B13203398","B12497874","B12499318","B12499389","B12499390","B13293124","B13363802","B13369437","B13369452","B7828516","B7828528","B7830225","B9041470","B12496318","B13203366","B13203401","B13203402","B13203403","B13203404","B13203405","B13203406","B13203407","B13203408","B13203409","B13203410","B13203411","B13203412","B13203413","B13203414","B13203415","B13203416","B13203417","B13203418","B13203419","B13203420","B13203421","B13203422","B13203423","B13203424","B13203425","B13203426","B13203427","B13203428","B13203429","B13203430","B13203431","B13203432","B13203433","B13203434","B13203435","B13203436","B13203437","B13203438","B13203439","B13203440","B13203441","B13203442","B13203443","B13203444","B13203445","B13203446","B13203447","B13203448","B13203449","B13203450","B13203451","B13203452","B13203453","B13203454","B13203455","B13203456","B13203457","B13203458","B13203459","B13203460","B13204821","B13204822","B13204823","B13204824","B13204825","B13204826","B13204827","B13204828","B13204829","B13204830","B13204831","B13204832","B13204833","B13204834","B13204835","B13204836","B13204837","B13204838","B13204839","B13204840","B13204841","B13204842","B13204843","B13204844","B13204845","B13204846","B13204847","B13204848","B13204849","B13204850","B13204851","B13204852","B13204853","B13204854","B13204855","B13204856","B13204857","B13204858","B13204859","B13204860","B13204861","B13204862","B13204863","B13204864","B13204865","B13204866","B13204867","B13204868","B13204869","B13204870","B13204871","B13204872","B13204873","B13204874","B13204875","B13204876","B13204877","B13204878","B13204879","B13204880","B13204881","B13204882","B13204883","B13204884","B13204885","B13204886","B13204887","B13204888","B13204889","B13204890","B13204891","B13204892","B13204893","B13204894","B13204895","B13204896","B13204897","B13204898","B13204899","B13204900","B13204901","B13204902","B13204903","B13204904","B13204905","B13204906","B13204907","B13204908","B13204909","B13204910","B13204911","B13204912","B13204913","B13204914","B13204915","B13204916","B13204917","B13204918","B13204919","B13204920","B13210481","B13210482","B13210483","B13210484","B13210485","B13210486","B13210487","B13210488","B13210489","B13210490","B13210491","B13210492","B13210493","B13210494","B13210495","B13210496","B13210497","B13210498","B13210499","B13210500","B13210501","B13210502","B13210503","B13210504","B13210505","B13210506","B13210507","B13210508","B13210509","B13210510","B13210511","B13210512","B13210513","B13210514","B13210515","B13210516","B13210517","B13210518","B13210519","B13210520","B13210521","B13210522","B13210523","B13210524","B13210525","B13210526","B13210527","B13210528","B13210529","B13210530","B13210531","B13210532","B13210533","B13210534","B13210535","B13210536","B13210537","B13210538","B13210539","B13210540","B13210541","B13210542","B13210543","B13210544","B13210545","B13210546","B13210547","B13210548","B13210549","B13210550","B13210551","B13210552","B13210553","B13210554","B13207151","B13207152","B13207153","B13207154","B13207155","B13207156","B13207157","B13207158","B13207159","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","*********","7sjht5j3ffvnvmaprfybz3b0","kpvi9s2p9soz78e0w1wiacd3","pqxs866wtmp8w10gwwzudgoh","rat0rgf9ynvroka7p75pk7su","vwwkd67vpcdllkpfyjkvc42p","RRLDUBS00000682","*********","*********","2MDTMB012","MDT RP 228","MDTMB001","MDTMB002","MDTMB003","MDTMB008","MDTMB012","MDTMB136","MDTMB198","MDTMB199","MDTMB229","MDTMB272","MDTMB274","MDTMB275","MDTMB276","MDTMB277","MDTMB278","MDTMB307","MDTMB310","MDTMB311","MDTMB35","MDTMB36","Battery213","Rack1","Rack10","Rack11","Rack12","Rack13","Rack14","Rack15","Rack16","Rack2","Rack3","Rack4","Rack5","Rack6","Rack7","Rack8","Rack9","*********","1HeatsinkA0313","1HeatsinkMix0312","1HeatsinkMix0313","1HeatsinkMix0314","1MetalEOL0317","1MetalEOL313","1MetalEOL314","1Rack","2MetalEOL0317","2MetalEOL313","2MetalEOL314","2Rack","3Metal0312","3MetalEOL0313","3MetalEOL314","3Rack","4MetalEOL0313","4MetalEOL0314","4Rack","5MetalEOL0312","5MetalEOL0314","5Rack","6Metal313","6MetalEOL0314","6Rack","7Metal313","01rack0320","1HeatsinkA0321","1HeatsinkMix0321","1MetalEOL0320","1MetalEOL0321","1MetalEOL0324","1PlasticsEOL0321","1Shred324","2Metal324","2MetalEOL0320","2MetalEOL0321","2MetalEOL0324","2Shred324","3Metal324","3MetalEOL0320","3MetalEOL0321","3MetalEOL0324","4MetalEOL0320","4MetalEOL0321","4MetalEOL0324","4Metals320","5MetalEOL0321","5Metals320","6MetalEOL0321","RRLDUBS00007468","RRLDUBS00007472","RRLDUBS00007546","B12494788","B12494789","B12494790","B12494791","B12494792","B12494793","B12494794","B12494795","B12494796","B12494797","B12494798","B12494799","B12494800","B12494801","B12494802","B12494803","B12494804","B12494805","B12494806","B12494807","B12494808","B12494809","B12494810","B12494811","B12494812","B12494813","B12494814","B12494815","B12494816","B12494818","B12494819","B12494820","B12494821","B12494822","B12494823","B12494824","B12494825","B12494826","B12494827","B12494828","B12494829","B12494830","B12494831","B12494832","B12494833","B12494834","B12494835","B12494836","B12494837","B12494838","B12494839","B12494840","B12494841","B12494842","B12494843","B12494844","B12494845","B12494846","B12494847","B12494848","B12494849","B12851692","B12851693","B12851694","B12851695","B12851696","B12851697","B12851698","B12851699","B12851700","B12851701","B12851702","B12851703","B12851704","B12851705","B12851706","B12851707","B12851708","B12851709","B12851710","B12851711","B12851712","B12851713","B12851714","B12851715","B12851716","B12851717","B12851718","B12851719","B12851720","B12851721","B12851722","B12851723","B12851724","B12851725","B12851726","B12851727","B12851728","B12851729","B12851730","B12851731","B12851732","B12851733","B12851734","B12851735","B12851736","B12851737","B12851738","B12856356","B13353547","B13353548","B13353549","B13353550","B13353551","B13353552","B13353553","B13353554","B13353555","B13353556","B13353557","B13353558","B13353559","B13353560","B13353561","B13353562","B13353563","B13353564","B13353565","B13353566","B13353567","B13353568","B13353569","B13353570","B13353571","B13353572","B13353573","B13353574","B13353575","B13353576","B13353577","B13353578","B13353579","B13353580","B13353581","B13353582","B13353583","B13353584","B13353585","B13353586","B13353588","B13353589","B13353590","B13353591","B13353592","B13353593","B13353594","B13353595","B13353596","B13353597","B13353601","B13353602","B13353603","B13353604","B13353605","B13353606","B13353607","B13353608","B13353609","B13353610","B13353611","B13353612","B13353613","B13353614","B13353615","B13353616","B13353617","B13353618","B13353619","B13353620","B13353621","B13353622","B13353623","B13353624","B13353625","B13353626","B13353627","B13353628","B13353629","B13353630","B13353631","B13353632","B13353633","B13353634","B13353635","B13353636","B13353637","B13353638","B13353639","B13353640","B13353641","B13353642","B13353643","B13353644","B13353645","B13353646","B13353647","B13353648","B13353649","B13353650","B13353651","B13353652","B13353653","B13353654","B13353655","B13353656","B13353657","B13353658","B13353659","B13353660","B14596786","B14596787","B14596788","B14596789","B14596790","B14596791","B14596792","B14596793","B14597228","B14597229","B14597230","B14597231","B14597232","B14597233","B14597234","B14597235","B14597236","B14597237","B14597238","B14597239","B14597240","B14597241","B14597242","B14597243","B14597244","B14597245","B14597246","B14597247","B14597248","B14597249","B14597250","B14597251","B14597252","B14597253","B14597254","B14597255","B14597256","B14597257","B14597258","B14597259","B14597260","B14597261","B14597262","B14597263","B14597264","B14597265","B14597266","B14597267","B14597268","B14597269","B14597270","B14597271","B14597272","B14597273","B14597274","B14597275","B14597276","B14597277","B14597278","B14597279","B14598156","B14598157","B14598158","B14598159","B14598160","B14598161","B14598162","B14598163","B14599241","B14599242","B14599243","B14599244","B14599245","B14599246","B14599247","B14599248","B14599249","B14599250","B14600860","B14601279","B14601280","B14601281","B14601282","B14601283","B14601284","B14601285","B14601286","B14601287","B14601288","B14601289","B14601290","B14601291","B14601292","B14601293","B14601294","B14601295","B14601296","B14601297","B14601298","B14601299","B14601300","B14601301","B14601302","B14601303","B14601304","B14601305","B14601306","B14601307","B14601308","B14601309","B14601310","B14601311","B14601312","B14601313","B14601314","RRLDUBS00007547","B13352721","RRLDUBS00004577","B13352731","[Security Seal ID]","B13352756","B13352757","B13352761","B13352760","RRLDUBS00002863","RRLDUBS00004985","RRLDUBS00008490","B12828199","B12828200","B12828201","B12828202","B12828203","B12828204","B12828205","B12828206","B12828207","B12828208","B12828209","B12828210","B12828211","B12828212","B12828213","B12828214","B12828215","B12828216","B12828217","B12828218","B12828219","B12828220","B12828221","B12828222","B12828223","B12828224","B12828225","B12828226","B12828227","B12828228","B12828229","B12828230","B12828231","B12828232","B12828233","B12828234","B12828235","B12828236","B12828237","B12828238","B12828239","B12828240","B12828241","B12828242","B12828243","B12828244","B12828245","B12828246","B12828247","B12828248","B12828249","B12828250","B12828251","B12828252","B12828253","B12828254","B12828255","B12828256","B12828257","B12828258","B12828259","B12828260","B12828261","B12828262","B12828263","B12828264","B12828265","B12828266","B12828267","B12828268","B12828269","B12828270","B12828271","B12828272","B12828273","B12828274","B12828275","B12828276","B12828277","B12828278","B12828279","B12828280","B12828281","B12828282","B12828283","B12828284","B12828285","B12828286","B12828287","B12828288","B12828289","B12828290","B12828291","B12828292","B12828293","B12828294","B12828295","B12828296","B12828297","B12828298","B12828299","B12828300","B12828301","B12828302","B12828303","B12828304","B12828305","B12828306","B12828307","B12828308","B12828309","B12828310","B12828311","B12828312","B12828313","B12828314","B12828315","B12828316","B12828317","B12828318","B12828319","B12828320","B12828321","B12828322","B12828323","B12828324","B12828325","B12828326","B12828327","B12828328","B12828329","B12828330","B12828331","B12828332","B12828333","B12828334","B12828335","B12828336","B12828337","B12828338","B12828339","B12828340","B12828341","B12828342","B12828343","B12828344","B12828345","B12828346","B12828347","B12828348","B12828349","B12828350","B12828351","B12828352","B12828353","B12828354","B12828355","B12828356","B12828357","B12828358","B12828359","B12828360","B12832284","B12832292","B12832293","B12855615","B12855616","B12855617","B12855618","B12855619","B12855620","B12855621","B12855622","B12855623","B12855624","B12855625","B12855626","B12855627","B12855628","B12855629","B12855630","B12855631","B12855632","B12855633","B12855634","B12855635","B12855636","B12855637","B12855638","B12855639","B12855640","B12855641","B12855642","B12855643","B12855644","B12855645","B12855646","B12855647","B12855648","B12855649","B12855650","B12855651","B12855652","B12855653","B12855654","B12855655","B12855656","B12855657","B12855658","B12855659","B12855660","B12855661","B12855662","B12855663","B12855664","B12855665","B12855666","B12855667","B12855668","B12855669","B12855670","B12855671","B12855672","B12855673","B12855674","B12855675","B12855676","B12855677","B12855678","B12855679","B12855680","B12855681","B12855682","B12855683","B12855684","B12855685","B12855686","B12855687","B12855688","B12855689","B12855690","B12855691","B12855692","B12855693","B12855694","B12855695","B12855696","B12855697","B12855698","B12855699","B12855700","B12855701","B12855702","B12855703","B12855704","B12855705","B12855706","B12855707","B12855708","B12855709","B12855710","B12855711","B12855712","B12855713","B12855714","B12855715","B12855716","B12855717","B12855718","B12855719","B12855720","B12855721","B12855722","B12855723","B12855724","B12855725","B12855726","B12855727","B12855728","B12855729","B12855730","B12855731","B12855732","B12855733","B12855734","B12855735","B12855736","B12855737","B12855738","B12855739","B12855740","B12855741","B12855742","B12855743","B12855744","B12855745","B12855746","B12855747","B12855748","B12855749","B12855750","B12855751","B12855752","B12855753","B12855754","B12855755","B12855756","B12855757","B12855758","B12855759","B12855760","B12855761","B12855762","B12855763","B12855764","B12855765","B12855766","B12855767","B12855768","B12855769","B12855770","B12855771","B12855772","B12855773","B12855774","B12855775","B12855776","B12855777","B12855778","B12855779","B12855780","B12855781","B12855782","B12855783","B12855784","B12855785","B12855786","B12855787","B12855788","B12855789","B12855790","B12855791","B12855792","B12855793","B12855794","B13293641","B13293642","B13293643","B13293644","B13293647","B13293648","B13293649","B14597475","B14597476","B14597477","B14597478","B14597479","B14597480","B14597481","B14597482","B14597483","B14597485","B12830527","B12830529","B12830530","B12830531","B12830532","B12830533","B12830534","B12830535","B12831935","B12831936","B12831937","B12831939","B12831940","B12833309","B12833310","B12833311","B12833312","B12833313","B12833314","B12833315","B12833316","B12833317","B12833318","B12833319","B12833320","B12833321","B12833322","B12833323","B12833324","B12833325","B12833326","B12833327","B12833328","B12833329","B12833330","B12833331","B12833332","B12833333","B12833334","B12833335","B12833336","B12833337","B12833338","B12833339","B12833340","B12833341","B12833342","B12833343","B12833344","B12833345","B12833346","B12833347","B12833348","B12833349","B12833350","B12833351","B12833352","B12833353","B12833354","B12833355","B12833356","B12833357","B12833358","B12833359","B12833360","B12833361","B12833362","B12833363","B12833364","B12833365","B12833366","B12833367","B12833368","B12833369","B12833370","B12833371","B12833372","B12833373","B12833374","B12833375","B12833376","B12833377","B12833378","B12833379","B12833380","B12833381","B12833382","B12833383","B12833384","B12833385","B12833386","B12833387","B12833388","B12833389","B12833390","B12833391","B12833392","B12833393","B12833394","B12833395","B12833396","B12833397","B12833398","B12833399","B12833400","B12833401","B12833402","B12833403","B12833404","B12833405","B12833406","B12833407","B12833408","B12833409","B12833410","B12833411","B12833412","B12833413","B12833414","B12833415","B12833416","B12833417","B12833418","B12833419","B12833420","B12833421","B12833422","B12833423","B12833424","B12833869","B12833870","B12833871","B12833872","B12833873","B12833874","B12833875","B12833876","B12833877","B12833878","B12833879","B12833880","B12833881","B12833882","B12833883","B12833884","B12833885","B12833886","B12833887","B12833888","B12833889","B12833890","B12833891","B12833892","B12833893","B12833894","B12833895","B12833896","B12833897","B12833898","B12833937","B12833938","B12833939","B12833940","B12833941","B12833942","B12833943","B12833944","B12833945","B12833946","B12833947","B12833948","B12833949","B12833950","B12833951","B12833952","B12833953","B12833954","B12833955","B14598090","B14598091","B14598092","B14598093","B14598094","B14598095","B14598096","B14598097","B14598098","B14598099","B14598100","B14598101","B14598102","B14598103","B14598104","B14598105","B14598106","B14598107","B14598108","B14598109","B14598110","B14598111","B14598112","B14598113","B14598114","B14598115","B14598116","B14598117","B14598118","B14598119","B14598120","B14598121","B14598122","B14598123","B14598124","B14598125","B14598126","B14598127","B14598128","B14598129","B14598130","B14598131","B14598132","B14598133","B14598134","B14598135","B14598136","B14598137","B14598138","B14598139","B14598140","B14598141","B14598142","B14598143","B14598144","B14598145","B14598146","B14598147","B14598148","B14598149","B14598150","B14598151","B14598152","B14598153","B14598154","B14598155","B14598164","B14598165","B14598166","B14598167","B14598168","B14598169","B14598170","B14598171","B14598172","B14598173","B14598174","B14598175","B14598176","B14598177","B14598178","B14598179","B14598180","B14598181","B14598182","B14598183","B14598184","B14598185","B14598186","B14598187","B14598188","B14598189","B14598190","B14598191","B14598192","B14598193","B14598194","B14598195","B14598196","B14598197","B14598198","B14598199","B14598200","B14598201","B14598202","B14598203","B14598204","B14598205","B14598206","B14598207","B14598208","B14598209","B14598210","B14598211","B14598212","B14598213","B14598214","B14598215","B14598216","B14598217","B14598218","B14598219","B14598220","B14598221","B14598222","B14598223","B14598224","B14598225","B14598226","B14598227","B14598228","B14598229","B14598230","B14598231","B14598232","B14598233","B14598234","B14598235","B14598236","B12831858","B12831859","B12831860","B12831861","B12831862","B12831863","B12831864","B12831865","B12831866","B12831867","B12831868","B12831869","B12831870","B12831871","B12831872","B12831873","B12831874","B12831875","B12831876","B12831877","B12831878","B12831879","B12831880","B12831881","B12831882","B12831883","B12831884","B12831885","B12831886","B12831887","B12831888","B12831889","B12831890","B12831891","B12831892","B12831893","B12831894","B12831895","B12831896","B12831897","B12831898","B12831899","B12831900","B12831901","B12831902","B12831903","B12831904","B12831905","B12831906","B12831907","B12831908","B12831909","B12831910","B12831911","B12831912","B12831913","B12831914","B12831915","B12831916","B12831917","B12831918","B12831919","B12831920","B12831921","B12831922","B12831923","B12831924","B12831925","B12831926","B12831927","B12831928","B12831929","B12831930","B12831931","B12831932","B12831933","B12831934","B12833425","B12833426","B12833427","B12833428","B12833429","B12833430","B12833431","B12833432","B12833433","B12833434","B12833435","B12833436","B12833437","B12833438","B12833439","B12833440","B12833441","B12833442","B12833443","B12833444","B12833445","B12833446","B12833447","B12833448","B12833449","B12833450","B12833451","B12833452","B12833453","B12833454","B12833455","B12833456","B12833457","B12833458","B12833459","B12833460","B12833461","B12833462","B12833463","B12833464","B12833465","B12833466","B12833467","B12833468","B12833469","B12833470","B12833471","B12833472","B12833473","B12833474","B12833475","B12833476","B12833477","B12833478","B12833479","B12833480","B12833481","B12833482","B12833483","B12833484","B12833485","B12833486","B12833487","B12833488","B12833489","B12833490","B12833491","B12833492","B12833493","B12833494","B12833495","B12833496","B12833497","B12833498","B12833499","B12833500","B12833501","B12833502","B12833503","B12833504","B12833899","B12833900","B12833901","B12833902","B12833903","B12833905","B12833906","B14598237","B14598238","B14598239","B14598240","B14598241","B14598242","B14598243","B14598244","B14598245","B14598246","B14598247","B14598248","B14598249","B14598250","B14598251","B14598252","B14598253","B14598254","B14598255","B14598256","B14598257","B14598258","B14598259","B14598260","B14598261","B14598262","B14598263","B14598264","B14598265","B14598266","B14598267","B14598268","B14598269","B14598270","B14598271","B14598273","B14598274","B14598275","B14598276","B14598277","B12833505","B12833506","B12833507","B12833508","B12833509","B12833510","B12833511","B12833512","B12833513","B12833514","B12833515","B12833516","B12833517","B12833518","B12833519","B12833520","B12833521","B12833907","B12833908","B12833909","B12833910","B12833911","B12833912","B12833913","B12833914","B12833915","B12833916","B12833917","B12833918","B12833919","B12833920","B12833921","B12833922","B12833923","B12833924","B12833925","B12833926","B12833927","B12833928","B14597898","B14597899","B14597900","B14597901","B14597902","B14597903","B14597904","B14597905","B14597906","B14597907","B14597908","B14597909","B14597910","B14597911","B14597912","B14597913","B14597914","B14597915","B14597916","B14597917","B14597918","B14597919","B14597920","B14597921","10Heatsink","11Heatsink","12Heatsink","1Heatsink","2Heatsink","3Heatsink","4Heatsink","5Heatsink","6Heatsink","7Heatsink","8Heatsink","9Heatsink","B13352782","B13202821","B13202822","B13202823","B13202824","B13202825","B13202826","B13202827","B13202828","B13202829","B13202830","B13202831","B13202832","B13202833","B13202834","B13202835","B13202836","B13202837","B13202838","B13202839","B13202840","B13202841","B13202842","B13202843","B13202844","B13202845","B13202846","B13202847","B13202848","B13202849","B13202850","B13202852","B13202853","B13202854","B13202855","B13202856","B13202857","B13202858","B13202859","B13202860","B13202861","B13202871","B13202872","B13202873","B13202874","B13202875","B13202876","B13202877","B13202878","B13202879","B13202880","B14598511","B14598512","B14598513","B14598514","B14598515","B14598516","B14598517","B14598518","B14598519","B14598520","B14598522","B14598523","B14598524","B14598525","B14598526","B14598527","B14598528","B14598529","B14598530","B14598531","B14598541","B14598542","B14598543","B14598544","B14598545","B14598546","B14598547","B14598548","B14598549","B14598550","B14598551","B14598552","B14598553","B14598554","B14598555","B14598556","B14598557","B14598558","B14598559","B14598560","B15244511","B15244512","B15244513","B15244514","B15244515","B15244516","B15244517","B15244518","B15244519","B15244520","B15244521","B15244522","B15244523","B15244524","B15244525","B15244526","B15244527","B15244528","B15244529","B15244530","B13352799","B13352800","B13352801","B13352802","RRLDUBS00008409","FT_MDT110-06062025-0mjZ","FT_RMS-RAMS-31286","FT_RMS-RAMS-31694","FT_RMS-RAMS-31711","FT_RMS-RAMS-31824","FT_RMS-RAMS-31838","FT_RMS-RAMS-31867","FT_RMS-RAMS-31869","FT_RMS-RAMS-31994","FT_RMS-RAMS-32000","B12851320","B12851321","B12851322","B12851323","B13204921","B13204922","B13204923","B13209813","B13209814","B13209815","B13209816","B13209817","B13209818","B13209905","B13209906","B13209907","B13209908","B13209909","B13209910","B13209947","B13209948","B13209949","WW-RZRR-5345 HEATSINK 1","WW-RZRR-5345 HEATSINK 2","WW-RZRR-5345 HEATSINK 3","WW-RZRR-5345 HEATSINK 4","WW-RZRR-5345 HEATSINK 5","WW-RZRR-5345 HEATSINK 6","WW-RZRR-5345 HEATSINK 7","WW-RZRR-5345 MISCELLANEOUS 1","Cables-01-06082025","Cables-01-07082025","Cables-01-08082025","Cables-01-22072025","Cables-01-24072025","Metal-01-07082025","Metal-01-08082025","Metal-01-18072025","Metal-01-24072025","OpticCables-01-24072025","Plastic-01-06082025","Plastic-01-07082025","Plastic-01-18072025","Plastic-01-22072025","Plastic-01-24072025","Plastic-02-06082025","Plasticbag-01-06082025","Plasticbag-01-08082025","Plasticbag-01-18072025","Plasticbag-01-24072025","RPDU-01-07082025","RPDU-01-08082025","RPDU-01-24072025","RRLDUBS00007548","RRLDUBS00007549","1K520","2K520","K520LA111","K520LA315","ENC1-29072025","ENC10-05082025","ENC11-05082025","ENC12-05082025","ENC13-05082025","ENC14-06082025","ENC15-06082025","ENC16-06082025","ENC17-06082025","ENC18-06082025","ENC19-06082025","ENC2-05082025","ENC20-06082025","ENC21-06082025","ENC22-06082025","ENC23-06082025","ENC24-06082025","ENC25-06082025","ENC26-06082025","ENC27-08082025","ENC28-08082025","ENC29-08082025","ENC3-05082025","ENC30-08082025","ENC31-08082025","ENC32-08082025","ENC33-08082025","ENC34-08082025","ENC35-08082025","ENC36-08082025","ENC37-08082025","ENC38-08082025","ENC39-08082025","ENC4-05082025","ENC40-08082025","ENC41-08082025","ENC42-08082025","ENC43-08082025","ENC44-08082025","ENC45-08082025","ENC46-08082025","ENC47-08082025","ENC5-05082025","ENC6-05082025","ENC7-05082025","ENC8-05082025","ENC9-05082025","RRLDUBS00003308","RRLDUBS00003309","RRLDUBS00003310","RRLDUBS00003311","RRLDUBS00003312","RRLDUBS00003313","RRLDUBS00003314","RRLDUBS00003315","RRLDUBS00003316","RRLDUBS00003317","RRLDUBS00003318","RRLDUBS00003319","RRLDUBS00003320","RRLDUBS00003321","RRLDUBS00003322","RRLDUBS00003323","RRLDUBS00003324","RRLDUBS00003325","RRLDUBS00005121","RRLDUBS00005188","RRLDUBS00005194","RRLDUBS00005199","RRLDUBS00005200","RRLDUBS00005201","RRLDUBS00005202","RRLDUBS00005203","RRLDUBS00005204","RRLDUBS00005205","RRLDUBS00005206","RRLDUBS00005207","RRLDUBS00005208","RRLDUBS00005209","RRLDUBS00005210","RRLDUBS00005211","RRLDUBS00005213","RRLDUBS00005214","RRLDUBS00005215","RRLDUBS00005216","RRLDUBS00005217","RRLDUBS00005218","RRLDUBS00005219","RRLDUBS00005220","RRLDUBS00005221","RRLDUBS00005222","RRLDUBS00005223","RRLDUBS00005224","RRLDUBS00005225","RRLDUBS00005226","RRLDUBS00005227","RRLDUBS00005228","RRLDUBS00005229","RRLDUBS00005230","RRLDUBS00005231","RRLDUBS00005232","RRLDUBS00005233","RRLDUBS00005234","RRLDUBS00005235","RRLDUBS00005236","RRLDUBS00005237","RRLDUBS00005238","RRLDUBS00005239","RRLDUBS00005240","RRLDUBS00005241","RRLDUBS00005242","RRLDUBS00005243","RRLDUBS00005244","RRLDUBS00005245","RRLDUBS00005246","RRLDUBS00005247","RRLDUBS00005252","RRLDUBS00005253","RRLDUBS00005254","RRLDUBS00005255","RRLDUBS00005256","RRLDUBS00005257","RRLDUBS00005258","RRLDUBS00005259","RRLDUBS00005260","RRLDUBS00005261","RRLDUBS00005262","RRLDUBS00005263","RRLDUBS00005264","RRLDUBS00005265","RRLDUBS00005266","RRLDUBS00005267","RRLDUBS00005268","RRLDUBS00005269","RRLDUBS00005270","RRLDUBS00005271","RRLDUBS00005272","RRLDUBS00005273","RRLDUBS00005274","RRLDUBS00005275","RRLDUBS00005276","RRLDUBS00005277","RRLDUBS00005278","RRLDUBS00005279","RRLDUBS00005280","4986018169","B12831820","B12831821","B12831822","B12831823","B12831824","B12831825","B12831826","B12831827","B12831828","B12831830","B13209951","B12327608","B12327609","B12327610","B12327611","B12327612","B12327614","B12327615","B12327616","B12327617","B12327618","B12327619","B12327620","B12327621","B12327622","B12327623","B12327624","B13714356","B13714357","B13714364","B13714365","B13714366","B13714367","B13714368","B13714369","B13714370","B13714371","B13714372","B13715187","B13715188","B13715189","B13715190","B13715191","B13715192","B13715193","B13715194","B13715195","B13715196","B13715197","B13715198","B13715199","B13715200","B13715201","B13715202","B13715203","B13715204","B13715205","B13715206","B13715207","B13715208","B13715209","B13715210","B13715212","B13715213","B13715217","B13716754","B13716755","B13716756","B13716757","B13716758","B13716759","B13716760","B13716761","B13716976","B13205999","B13206000","B13206001","B13206002","B13206003","B13206234","B13206235","B13206236","B13206237","B13206238","B13206239","B13206240","B13206241","B13206242","B13206244","B13206245","B13206246","B13206247","B13206248","B13206249","B13206250","B13206251","B13206252","B13206253","B13206254","B13206255","B13206256","B13206257","B13206258","B13206259","B13206260","B13206261","B13206262","B13206263","B13206264","B13206265","B13206266","B13206267","B13206268","B13206269","B13206270","B13206271","B13206272","B13206273","B13206274","B13206275","B13206276","B13206277","B13206278","B13206279","B13206280","B13206281","B13206282","B13206283","B13206284","B13206285","B13206286","B13206287","B13206288","B13206289","B13206290","B13206291","B13206292","B13206293","B13206294","B13206295","B13206296","B13206297","B13206298","B13206299","B13206300","B13206301","B13206302","B13206303","B13206304","B13206305","B13206306","B13206307","B13206308","B13206309","B13206310","B13206311","B13206312","B13206313","B13206314","B13206315","B13206316","B13206317","B13206318","B13206319","B13206320","B13206321","B13206322","B13206323","B13206324","B13206325","B13206326","B13206327","B13206328","B13206329","B13206330","B13206331","B13206332","B13206333","B13206334","B13206335","B13206336","B13206337","B13206338","B13206339","B13206340","B13206341","B13206342","B13206343","B13206344","B13206345","B13206346","B13206347","B13206348","B13206349","B13206350","B13206351","B13206352","B13206353","B13206354","B13206355","B13206356","B13206357","B13206358","B13206359","B13206360","B13206361","B13206362","B13206363","B13206364","B13206365","B13206366","B13206367","B13206368","B13206369","B13206370","B13206371","B13206372","B13206373","B13206374","B13206375","B13206376","B13206377","B13206378","B13206379","B13206380","B13206381","B13206382","B13206383","B13206384","B13206385","B13206386","B13206387","B13206388","B13206389","B13206390","B13206391","B13206392","B13206393","B13206394","B13206395","B13206396","B13206397","B13206398","B13206399","B13206400","B13206401","B13206402","B13206403","B13206404","B13206405","B13206406","B13206407","B13206408","B13209952","B13209953","B13209954","B13209955","B13209956","B13209957","B13209958","B13209959","B13209960","B13209961","B13209962","B13209963","B13209964","B13209965","B13209966","B13209967","B13209968","B13209969","B13209970","B13209971","B13209972","B13209973","B13209974","B13209975","B13209976","B13209977","B13209978","B13209979","B13209980","B13209981","B13209982","B13209983","B13209984","B13349193","B13349194","B13349195","B13349196","B13349197","B13349198","B13349199","B13349200","B13349202","B13349203","B13349204","B13349205","B13349206","B13349207","B13349208","B13349210","B13349211","B13349212","B13349213","B13349214","B13349215","B13349216","B13349217","B13349218","B13349219","B13349220","B13349221","B13349222","B13349223","B13349224","B13349225","B13349226","B13349227","B13349228","B13349229","B15244699","B15244700","B15244701","B15244702","B15244703","B15244704","B15244705","B15244706","B15244707","B15244708","B15244709","B15244710","B15244711","B15244712","B15244713","B15244714","B15244715","B15244716","B15244717","B15244718","B15244719","B15244720","B15244721","B15244722","B15244723","B15244724","B15244725","B15244726","B15244727","B15244728","B15244729","B15244730","B15244731","B15244732","B15244733","B15244734","B15244735","B15244736","B15244737","B15244738","B15244739","B15244740","B15244741","B15244742","B15244743","B15244744","B15244745","B15244746","B15244747","B15244748","B15244749","B15244750","B15244751","B15244752","B15244753","B15244754","B15244755","B15244756","B15244757","B15244758","B15244759","B15244760","10Rack-080825","11Rack-080825","12Rack-080825","13Rack-080825","1CB-08072025","1FM-080825","1HSC-080725","1MTL-080825","1PL-080725","1PSU-08072025","1RACK-080725","2CB-08072025","2MTL-08072025","2MTL-08082025","2PL-08072025","2Rack080725","3CB-080825","3PL-08072025","3Rack-080725","4Rack-080825","5Rack-080825","6Rack-080825","7Rack-080825","8Rack-080825","9Rack-080825","RRLDUBS00003332","RRLDUBS00003333","RRLDUBS00003334","RRLDUBS00003335","RRLDUBS00003336","RRLDUBS00003337","RRLDUBS00003338","RRLDUBS00003339","RRLDUBS00003340","RRLDUBS00003341","RRLDUBS00003342","RRLDUBS00003343","RRLDUBS00003344","RRLDUBS00003345","RRLDUBS00003346","RRLDUBS00003347","RRLDUBS00003348","RRLDUBS00003349","RRLDUBS00003350","RRLDUBS00003351","RRLDUBS00003352","RRLDUBS00003353","RRLDUBS00003354","RRLDUBS00003363","RRLDUBS00005248","RRLDUBS00005249","RRLDUBS00005250","RRLDUBS00005251","B15244761","B15244762","123","123456"
) and completed = 0 limit 10000';

$q = mysqli_query($connectionlink, $query);
if(mysqli_affected_rows($connectionlink) > 0) {
    while($row = mysqli_fetch_assoc($q)) {
        echo "Processing Serial\n ".$row['SerialID'];
        if($row['AssetScanID'] > 0) {
            $query1 = "select count(*) from custompallet_items where AssetScanID = '".mysqli_real_escape_string($connectionlink,$row['AssetScanID'])."' and CustomPalletID = '".$row['CustomPalletID']."'";
        } else if($row['ServerID'] > 0) {
            $query1 = "select count(*) from custompallet_items where ServerID = '".mysqli_real_escape_string($connectionlink,$row['ServerID'])."' and CustomPalletID = '".$row['CustomPalletID']."'";
        } else if($row['MediaID'] > 0) {
            $query1 = "select count(*) from custompallet_items where MediaID = '".mysqli_real_escape_string($connectionlink,$row['MediaID'])."' and CustomPalletID = '".$row['CustomPalletID']."'";
        } else if($row['byproduct_id'] > 0) {
            $query1 = "select count(*) from custompallet_items where byproduct_id = '".mysqli_real_escape_string($connectionlink,$row['byproduct_id'])."' and CustomPalletID = '".$row['CustomPalletID']."'";
        } else if($row['UnserializedRecoveryRecordID'] > 0) {
            $query1 = "select count(*) from custompallet_items where UnserializedRecoveryRecordID = '".mysqli_real_escape_string($connectionlink,$row['UnserializedRecoveryRecordID'])."' and CustomPalletID = '".$row['CustomPalletID']."'";
        }

        if($query1) {
            $q1 = mysqli_query($connectionlink, $query1);
            if(mysqli_error($connectionlink)) {			
                echo mysqli_error($connectionlink)."<br>";
                continue;		
            }
            if(mysqli_affected_rows($connectionlink) > 0) {
                $row1 = mysqli_fetch_assoc($q1);
                if($row1['count(*)'] > 0) {
                    echo "Already Exists\n".$row['SerialID']."<br>";

                    $query4 = "update shipping_container_serials set CustomPalletID = '".$row['CustomPalletID']."',BinName = '".$row['BinName']."',Completed = 1,Skipped = 1 where SerialID = '".$row['SerialID']."'";
                    $q4 = mysqli_query($connectionlink, $query4);   
                    if(mysqli_error($connectionlink)) {			
                        echo mysqli_error($connectionlink)."<br>";
                        continue;		
                    };                    
                    continue;
                } else {
                    //update the below query based on condition if AssetScanID > 0 use asset query

                    // Build dynamic query based on which fields have valid values
                    $columns = array('CustomPalletID', 'DateCreated', 'CreatedBy', 'status', 'Quantity');
                    $values = array(
                        "'".mysqli_real_escape_string($connectionlink,$row['CustomPalletID'])."'",
                        'NOW()',
                        "'".$systemUserId."'",
                        "'1'",
                        "'1'"
                    );

                    // Add AssetScanID only if > 0
                    if($row['AssetScanID'] > 0) {
                        $columns[] = 'AssetScanID';
                        $values[] = "'".mysqli_real_escape_string($connectionlink,$row['AssetScanID'])."'";
                    }

                    // Add ServerID only if > 0
                    if($row['ServerID'] > 0) {
                        $columns[] = 'ServerID';
                        $values[] = "'".mysqli_real_escape_string($connectionlink,$row['ServerID'])."'";
                    }

                    // Add MediaID only if > 0
                    if($row['MediaID'] > 0) {
                        $columns[] = 'MediaID';
                        $values[] = "'".mysqli_real_escape_string($connectionlink,$row['MediaID'])."'";
                    }

                    // Add byproduct_id only if > 0
                    if($row['byproduct_id'] > 0) {
                        $columns[] = 'byproduct_id';
                        $values[] = "'".mysqli_real_escape_string($connectionlink,$row['byproduct_id'])."'";
                    }

                    if($row['UnserializedRecoveryRecordID'] > 0) {
                        $columns[] = 'UnserializedRecoveryRecordID';
                        $values[] = "'".mysqli_real_escape_string($connectionlink,$row['UnserializedRecoveryRecordID'])."'";
                    }

                    $query2 = "INSERT INTO custompallet_items (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $values) . ")";
                    $q2 = mysqli_query($connectionlink, $query2);
                    if(mysqli_error($connectionlink)) {
                        echo mysqli_error($connectionlink).$query2."<br>";
                        continue;
                    }

                    $query3 ="update custompallet set AssetsCount = AssetsCount + 1 where CustomPalletID = '".$row['CustomPalletID']."'";
                    $q3 = mysqli_query($connectionlink, $query3);
                    if(mysqli_error($connectionlink)) {			
                        echo mysqli_error($connectionlink)."<br>";
                        continue;		
                    };
                    $query4 = "update shipping_container_serials set CustomPalletID = '".$row['CustomPalletID']."',BinName = '".$row['BinName']."',Completed = 1 where SerialID = '".$row['SerialID']."'";
                    $q4 = mysqli_query($connectionlink, $query4);   
                    if(mysqli_error($connectionlink)) {			
                        echo mysqli_error($connectionlink)."<br>";
                        continue;		
                    };
                }
            }
        } else {
            echo "No Query for ".$row['SerialID'];
            continue;
        }
        
    }

    echo "Loop Completed";
} else {
    echo "No Records";
}
?>
