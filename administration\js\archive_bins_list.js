angular.module('app').controller("ArchiveBinsList", function ($scope, $location, $rootScope, $mdToast, $q, $mdDialog) {
    $scope.busy = false;
    $scope.ArchiveBinsList = [];
    $scope.pagedItems = [];

    //Start Pagination Logic
    $scope.itemsPerPage = 10;
    $scope.OrderBy = '';
    $scope.OrderByType = '';
    $scope.filter_text = [{}];

    // Initialize current page
    $scope.currentPage = 0;

    $scope.range = function() {
        var rangeSize = 10;
        var ret = [];
        var start;

        start = $scope.currentPage;
        if ( start > $scope.pageCount()-rangeSize ) {
            start = $scope.pageCount()-rangeSize;
        }
        if (start < 0) {
            start = 0;
        }

        for (var i=start; i<start+rangeSize && i<$scope.pageCount(); i++) {
            if (i >= 0) {
                ret.push(i);
            }
        }
        return ret;
    };

    // ReActivate Bin functionality
    $scope.ReActivateBin = function(item, ev) {
        $scope.reactivateBinData = {
            CustomPalletID: item.CustomPalletID,
            BinName: item.BinName,
            locationGroup: '',
            selectedLocationGroup: null
        };

        $scope.reactivateBinBusy = false;

        $mdDialog.show({
            controller: "ArchiveBinsList",
            templateUrl: 'reactivateBinModal.html',
            parent: angular.element(document.body),
            targetEvent: ev,
            clickOutsideToClose: false,
            scope: $scope,
            preserveScope: true
        });
    };

    // Location Group search functionality
    $scope.queryLocationGroupSearch = function(query) {
        var deferred = $q.defer();

        if (!query || query.length < 1) {
            deferred.resolve([]);
            return deferred.promise;
        }

        jQuery.ajax({
            url: host + 'administration/includes/archive_bins_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetLocationGroups&search=' + encodeURIComponent(query) + '&wipOnly=true',
            success: function(data) {
                if (data.Success) {
                    deferred.resolve(data.Data || []);
                } else {
                    deferred.resolve([]);
                }
                $scope.$apply();
            },
            error: function() {
                deferred.resolve([]);
                $scope.$apply();
            }
        });

        return deferred.promise;
    };

    $scope.LocationGroupChange = function(text) {
        // Handle location group search text change
    };

    $scope.selectedLocationGroupChange = function(item) {
        if (item) {
            $scope.reactivateBinData.selectedLocationGroup = item;
        }
    };

    $scope.confirmReActivateBin = function() {        

        $scope.reactivateBinBusy = true;

        jQuery.ajax({
            url: host + 'administration/includes/archive_bins_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=ReActivateBin&CustomPalletID=' + encodeURIComponent($scope.reactivateBinData.CustomPalletID) + '&GroupName=' + encodeURIComponent($scope.reactivateBinData.locationGroup),
            success: function(data) {
                $scope.reactivateBinBusy = false;

                if (data.Success) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-success md-block')
                    );
                    $mdDialog.hide();                    
                    $scope.CallServerFunction($scope.currentPage);
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result || 'Failed to reactivate bin')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                $scope.$apply();
            },
            error: function() {
                $scope.reactivateBinBusy = false;
                $mdToast.show(
                    $mdToast.simple()
                        .content('Error occurred while reactivating bin')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                $scope.$apply();
            }
        });
    };

    $scope.cancel = function() {
        $mdDialog.hide();
    };

    $scope.prevPage = function() {
        if ($scope.currentPage > 0) {
            $scope.currentPage--;
        }
    };

    $scope.firstPage = function () {
        $scope.currentPage = 0;
    };

    $scope.prevPageDisabled = function() {
        return $scope.currentPage === 0 ? "disabled" : "";
    };

    $scope.pageCount = function() {
        return Math.ceil($scope.total/$scope.itemsPerPage);
    };

    $scope.nextPage = function() {
        if ($scope.currentPage < $scope.pageCount()-1) {
            $scope.currentPage++;
        }
    };

    $scope.lastPage = function() {
        $scope.currentPage = $scope.pageCount() - 1;
    };

    $scope.nextPageDisabled = function() {
        return $scope.currentPage === $scope.pageCount()-1 ? "disabled" : "";
    };

    $scope.setPage = function(n) {
        if (n >= 0 && n < $scope.pageCount()) {
            $scope.currentPage = n;
        }
    };

    $scope.CallServerFunction = function (newValue) {
        $scope.busy = true;
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/archive_bins_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetArchiveBins&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
            success: function(data) {
                $scope.busy = false;
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                    $scope.pagedItems = data.Result;
                    if(data.total) {
                        $scope.total = data.total;
                    }
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.busy = false;
                $rootScope.$broadcast('preloader:hide');
                console.log(data);
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
    };

    $scope.$watch("currentPage", function(newValue, oldValue) {
        $scope.CallServerFunction(newValue);
    });

    $scope.convertSingle = function (multiarray) {
        var result = {};
        for(var i=0;i<multiarray.length;i++) {
            result[i] = multiarray[i];
        }
        return result;
    };

    $scope.MakeOrderBy = function (orderby) {
        $scope.OrderBy = orderby;
        if($scope.OrderByType == 'asc') {
            $scope.OrderByType = 'desc';
        } else {
            $scope.OrderByType = 'asc';
        }
        $scope.busy = true;
        $rootScope.$broadcast('preloader:active');

        jQuery.ajax({
            url: host+'administration/includes/archive_bins_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetArchiveBins&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
            success: function(data) {
                $scope.busy = false;
                $rootScope.$broadcast('preloader:hide');
                if(data.Success) {
                    $scope.pagedItems = data.Result;
                    if(data.total) {
                        $scope.total = data.total;
                    }
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .position('right')
                            .hideDelay(3000)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.busy = false;
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
    };

    $scope.MakeFilter = function () {
        if($scope.currentPage == 0) {
            $scope.CallServerFunction($scope.currentPage);
        } else {
            $scope.currentPage = 0;
        }
    };

    // Export to Excel function - exports ALL archived bins (no filters)
    $scope.ArchiveBinsListxls = function () {
        // Directly navigate to Excel export - no need to store filters since we want all bins
        window.location = host + "administration/templates/ArchiveBinsListxls.php";
    };
});
