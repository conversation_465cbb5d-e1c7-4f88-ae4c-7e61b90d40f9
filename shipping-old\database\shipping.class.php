<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
class ShippingClass extends CommonClass {
	public $responseParameters;	
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect();
	}


	public function GetDispositions ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select * from disposition where status = 'Active' and shipping_removal_type = '1' order by disposition";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Dispositions Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAllVendors ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select * from vendor where Status = '1' order by VendorName";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Destinations Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function CreateShipment ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			if($data['CreatedBy'] > 0) { //Update existing Shipment
				//$query = "update shipping set VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
				//$query = "update shipping set disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
				$query = "update shipping set VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."',DestinationFacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['DestinationFacilityID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";

				$query = "UPDATE shipping SET 
				VendorID = " . ($data['VendorID'] > 0 ? "'".mysqli_real_escape_string($this->connectionlink, $data['VendorID'])."'" : "NULL") . ",
				DestinationFacilityID = " . ($data['DestinationFacilityID'] > 0 ? "'".mysqli_real_escape_string($this->connectionlink, $data['DestinationFacilityID'])."'" : "NULL") . ",
				UpdatedDate = NOW(),
				UpdatedBy = '" . $_SESSION['user']['UserId'] . "' 
				WHERE ShippingID = '" . mysqli_real_escape_string($this->connectionlink, $data['ShippingID']) . "' ";
				$q = mysqli_query($this->connectionlink,$query);		
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}

				$json['Success'] = true;
				$json['Result'] = 'Shipment Modfied';
				return json_encode($json);
			}

			//Start check If ShippingID exists
			$query1 = "select count(*) from shipping where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$json['Success'] = false;			
					$json['Result'] = 'TicketID already exists';			
					return json_encode($json);
				}
			}
			//ENd check If ShippingId exists
			
			//$query = "insert into shipping (ShippingID,disposition_id,VendorID,ShipmentStatusID,CreatedDate,CreatedBy,FacilityID) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."')";
			$query = "insert into shipping (ShippingID,";
			if($data['VendorID'] > 0) {
				$query = $query."VendorID,";
			}
			if($data['DestinationFacilityID'] > 0) {
				$query = $query."DestinationFacilityID,";
			}
			$query = $query."ShipmentStatusID,CreatedDate,CreatedBy,FacilityID) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',";
			if($data['VendorID'] > 0) {
				$query = $query."'".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."',";
			}
			if($data['DestinationFacilityID'] > 0) {
				$query = $query."'".mysqli_real_escape_string($this->connectionlink,$data['DestinationFacilityID'])."',";
			}
			$query= $query."'1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."')";
			$q = mysqli_query($this->connectionlink,$query);		
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}	
			$ShippingID = $data['ShippingID'];
			$json['ShippingID'] = $ShippingID;
			$json['Success'] = true;
			$json['Result'] = 'Shipment Created';
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetShipmentDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			$query = "select s.*,v.ContactName as DestinationPOC from shipping s 
			left join vendor v on s.VendorID = v.VendorID 
			where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['ShipmentStatusID'] == '3') {
					$json['Success'] = false;
					$json['Result'] = 'Shipment is already shipped';
					return json_encode($json);
				}

				//Start get Shipment Containers
				$query2 = "select d.*,p.packageName,s.Status,l.LocationName,g.GroupName,g.GroupName as `group`,dd.disposition from shipping_containers d 
				left join package p on d.idPackage = p.idPackage 
				left join location l on d.LocationID = l.LocationID 
				LEFT JOIN location_group g on l.GroupID = g.GroupID 
				left join disposition dd on d.disposition_id = dd.disposition_id 
				left join shipping_status s on d.StatusID = s.ShipmentStatusID where d.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$row['ShippingID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;
					$containers = array();
					while($row2 = mysqli_fetch_assoc($q2)) {
						$row2['ContainerWeight'] = intval($row2['ContainerWeight']);
						$containers[$i] = $row2;
						$i++;
					}
					$row['Containers'] = $containers;
				} else {
					$row['Containers'] = array();
				}
				//End get Shipment Containers				
				$json['Success'] = true;			
				$json['Result'] = $row;			
				return json_encode($json);
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Ticket ID';			
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetPackageTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select * from package where Active = '1' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and ContainerClassification = 'Outbound' order by packageName";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Container Types Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function CreateContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['ContainerWeight']
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			if(! $data['BatchRecovery']) {
				$data['BatchRecovery'] = 0;
			}
			
			//Start get Shipping Details
			$query = "select * from shipping where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipment = mysqli_fetch_assoc($q);
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment';			
				return json_encode($json);
			}
			//End get Shipping Details


			if($data['CreatedBy'] > 0) { //Update existing Container
				//Start get Container Details
				$query = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['StatusID'] != '1' && $row['StatusID'] != '7' && $row['StatusID'] != '6') {
					//if($row['StatusID'] != '1') {
						$json['Success'] = false;			
						$json['Result'] = 'Container Status is not Active';
						return json_encode($json);
					}					
				}
				if($row['SealID'] != $data['SealID']) {
					$seal_changed = '1';
				} else {
					$seal_changed = '0';
				}
				///End get Container Details

				//Start check for min weight
				$query4 = "select packageWeight,OptionalLocation from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					// if($row4['packageWeight'] >= $data['ContainerWeight']) {
					// 	$json['Success'] = false;			
					// 	$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
					// 	return json_encode($json);
					// }
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight

				if($data['group'] != $data['GroupName']) { // Location Changed
					if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

						//Start check if valid Group
						$query10 = "select GroupID,FacilityID,LocationType,BinTypeID from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
						$q10 = mysqli_query($this->connectionlink,$query10);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row10 = mysqli_fetch_assoc($q10);						
							if($row10['FacilityID'] != $data['FacilityID']) {
								$json['Success'] = false;
								$json['Result'] = 'Location Facility is different from Shipment Facility';
								return json_encode($json);
							}
							if($row10['LocationType'] != 'Outbound Storage') {
								$json['Success'] = false;
								$json['Result'] = 'Location is not Outbound Storage Location';
								return json_encode($json);
							}				
							$GroupID = $row10['GroupID'];

							//Start get free location from group selected
							$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."'";
							$q112 = mysqli_query($this->connectionlink,$query112);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row112 = mysqli_fetch_assoc($q112);
								$data['NewLocationID'] = $row112['LocationID'];								
								$newLocationName = $row112['LocationName'];
								$data['LocationID'] = $row112['LocationID'];
						 		$location_changed = true;
							} else {
								$json['Success'] = false;
								$json['Result'] = 'No locations available, in selected group';
								return json_encode($json);
							}
							//End get free location from group selected	

						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid Location Group';
							return json_encode($json);
						}
						//End check if valid Group								
					} else {
						$data['LocationID'] = '';
						if($row4['OptionalLocation'] == 0) {
							$json['Success'] = false;
							$json['Result'] = 'Invalid Location';
							return json_encode($json);
						}
						if($row['LocationID'] > 0) {
							$location_changed = true;
						} else {
							$location_changed = false;
						}
					}
				} else {
					$location_changed = false;
				}


				// if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
				// 	$query20 = "select * from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
				// 	$q20 = mysqli_query($this->connectionlink,$query20);
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row20 = mysqli_fetch_assoc($q20);
				// 		if($row20['LocationID'] != $row['LocationID']) {//Location Changed

				// 			if($row20['FacilityID'] != $data['FacilityID']) {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location Facility is different from Shipment Facility';
				// 				return json_encode($json);
				// 			}
	
				// 			if($row20['Locked'] == '1') {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location is Locked';
				// 				return json_encode($json);
				// 			}
	
				// 			if($row20['LocationType'] != 'Outbound Storage') {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location is not Outbound Storage Location';
				// 				return json_encode($json);
				// 			}
	
				// 			if($row20['LocationStatus'] != '1') {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location is not Active';
				// 				return json_encode($json);
				// 			}						
				// 			$data['LocationID'] = $row20['LocationID'];
				// 			$location_changed = true;
				// 		} else {
				// 			$location_changed = false;
				// 		}											
				// 	} else {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// }  else {
				// 	$data['LocationID'] = '';
				// 	if($row4['OptionalLocation'] == 0) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// 	if($row['LocationID'] > 0) {
				// 		$location_changed = true;
				// 	} else {
				// 		$location_changed = false;
				// 	}
				// }


				//$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."',LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."',StatusID = 1 where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				//$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."'";
				//$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."'";
				$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',ReferenceIDRequired = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceIDRequired'])."',ReferenceID = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceID'])."',ReferenceTypeID = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."',ReferenceType = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."' ";
				$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."',ReferenceIDRequired = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceIDRequired'])."',ReferenceID = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceID'])."',ReferenceTypeID = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."',ReferenceType = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."',BatchRecovery = '".mysqli_real_escape_string($this->connectionlink,$data['BatchRecovery'])."' ";
				if($data['LocationID'] > 0) {
					$query1 = $query1.",LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."'";
				} else {
					$query1 = $query1.",LocationID = NULL";
				}
				$query1 = $query1.",StatusID = 1";
				if($seal_changed == 1) {
					//$query1 = $query1 . ",RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."' ";
				}
				$query1 = $query1." where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);				
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Success'] = true;
				$json['Result'] = 'Container Modified';

				//Start check If Container is closed
				if($data['CloseContainer'] == true) {
					$query2 = "update shipping_containers set StatusID = '6' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$json['Result'] = 'Container Closed';
				}
				//End check If Container is closed

				//Start Lock/Unlock Locations
				if($location_changed == true) {
					if($row['LocationID'] > 0) {
						$query6 = "update location set Locked='2',currentItemType='',currentItemID='' where LocationID = '".mysqli_real_escape_string($this->connectionlink,$row['LocationID'])."' ";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;			
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
					}	
					
					if($data['LocationID'] > 0) {
						$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Shipment Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."'";
						$queryloc = mysqli_query($this->connectionlink,$sqlloc);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							//return json_encode($json);	
						}
					}					
				}
				//End Lock/Unlock Locations

				return json_encode($json);
			} else { //Create new Container
				//Start check If ShippingID exists
				$query1 = "select count(*) from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['count(*)'] > 0) {
						$json['Success'] = false;			
						$json['Result'] = 'Container ID already exists';			
						return json_encode($json);
					}
				}
				//ENd check If ShippingId exists

				//Start check for min weight
				$query4 = "select packageWeight,OptionalLocation from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					// if($row4['packageWeight'] >= $data['ContainerWeight']) {
					// 	$json['Success'] = false;			
					// 	$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
					// 	return json_encode($json);
					// }
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight


				if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

					//Start check if valid Group
					$query10 = "select GroupID,FacilityID,LocationType,BinTypeID from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
					$q10 = mysqli_query($this->connectionlink,$query10);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row10 = mysqli_fetch_assoc($q10);						
						if($row10['FacilityID'] != $data['FacilityID']) {
							$json['Success'] = false;
							$json['Result'] = 'Location Facility is different from Shipment Facility';
							return json_encode($json);
						}
						if($row10['LocationType'] != 'Outbound Storage') {
							$json['Success'] = false;
							$json['Result'] = 'Location is not Outbound Storage Location';
							return json_encode($json);
						}				
						$GroupID = $row10['GroupID'];

						//Start get free location from group selected
						$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."'";
						$q112 = mysqli_query($this->connectionlink,$query112);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row112 = mysqli_fetch_assoc($q112);
							$data['NewLocationID'] = $row112['LocationID'];
							$data['location'] = $row112['LocationID'];
							$newLocationName = $row112['LocationName'];
						} else {
							$json['Success'] = false;
							$json['Result'] = 'No locations available, in selected group';
							return json_encode($json);
						}
						//End get free location from group selected	

					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
					//End check if valid Group								
				} else {
					if($row4['OptionalLocation'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
				}





				//Start check for valid location

				// if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
				// 	$query20 = "select * from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
				// 	$q20 = mysqli_query($this->connectionlink,$query20);
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row20 = mysqli_fetch_assoc($q20);

				// 		if($row20['FacilityID'] != $data['FacilityID']) {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location Facility is different from Shipment Facility';
				// 			return json_encode($json);
				// 		}

				// 		if($row20['Locked'] == '1') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is Locked';
				// 			return json_encode($json);
				// 		}

				// 		if($row20['LocationType'] != 'Outbound Storage') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is not Outbound Storage Location';
				// 			return json_encode($json);
				// 		}

				// 		if($row20['LocationStatus'] != '1') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is not Active';
				// 			return json_encode($json);
				// 		}						
				// 		$data['location'] = $row20['LocationID'];						
				// 	} else {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// }
				// else {
				// 	if($row4['OptionalLocation'] == 0) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// }

				//End check for valid location

				//Start check for pallet 
				//$query6 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$query6 = "select count(*) from pallets where idPallet like '%".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."%'";
				$q6 = mysqli_query($this->connectionlink,$query6);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row6 = mysqli_fetch_assoc($q6);
					if($row6['count(*)'] > 0) {
						$pallet_exists = true;
					} else {
						$pallet_exists = false;
					}
				} else {
					$pallet_exists = false;
				}
				if($pallet_exists == true) {
					//start get servers count 
					//$query7 = "select count(*) from speed_server_recovery where idPallet= '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and Type = 'Server'";
					$query7 = "select count(*) from speed_server_recovery where idPallet like '%".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."%' and Type = 'Server'";
					$q7 = mysqli_query($this->connectionlink,$query7);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row7 = mysqli_fetch_assoc($q7);
						$expected_servers = $row7['count(*)'];
					} else {
						$expected_servers = 0;
					}
					//End get servers count
				} else {
					$expected_servers = 0;
				}
				//End check for pallet

				//$query = "insert into shipping_containers (ShippingID,ShippingContainerID,idPackage,StatusID,CreatedDate,CreatedBy,CustomID,ContainerNotes,SealID,ContainerWeight,ShippingControllerLoginID,FacilityID,LocationID) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['location'])."')";
				//$query = "insert into shipping_containers (ShippingID,ShippingContainerID,idPackage,StatusID,CreatedDate,CreatedBy,CustomID,ContainerNotes,SealID,ContainerWeight,ShippingControllerLoginID,FacilityID";
				//$query = "insert into shipping_containers (ShippingID,ShippingContainerID,idPackage,StatusID,CreatedDate,CreatedBy,CustomID,ContainerNotes,ContainerWeight,ShippingControllerLoginID,FacilityID";
				$query = "insert into shipping_containers (ShippingID,ShippingContainerID,idPackage,StatusID,CreatedDate,CreatedBy,CustomID,ContainerNotes,ShippingControllerLoginID,FacilityID,disposition_id ,ReferenceIDRequired,ReferenceID,ReferenceTypeID,ReferenceType,BatchRecovery ";
				if($data['location'] > 0) {
					$query = $query . ",LocationID";
				}
				if(true) {
					$query = $query . ",ExpectedServersCount,ScannedServersCount";
				}
				//$query = $query . ",RecentSealDate,RecentSealBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
				//$query = $query . ") values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."'";
				$query = $query . ") values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceIDRequired'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."','".mysqli_real_escape_string($this->connectionlink,$data['BatchRecovery'])."'";
				if($data['location'] > 0) {
					$query = $query . ",'".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
				}
				if(true) {
					$query = $query . ",'".mysqli_real_escape_string($this->connectionlink,$expected_servers)."','0'";
				}
				$query = $query . ")";
				$q = mysqli_query($this->connectionlink,$query);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}

				//Start Lock Location
				if($data['location'] > 0) {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Shipment Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
				}				
				//End Lock Location

				//Start get Container details
				$query2 = "select d.*,p.packageName,s.Status,l.LocationName,dd.disposition from shipping_containers d 
				left join package p on d.idPackage = p.idPackage 
				left join location l on d.LocationID = l.LocationID 
				left join disposition dd on d.disposition_id = dd.disposition_id 
				left join shipping_status s on d.StatusID = s.ShipmentStatusID where d.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);		
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row2 = mysqli_fetch_assoc($q2);
					$row2['location'] = $row2['LocationName'];
					$json['Container'] = $row2;
				}
				//End get Container details

				$json['Success'] = true;
				$json['Result'] = 'Container Created';
				return json_encode($json);
			}
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function AddSerialToContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Prep';
				return json_encode($json);
			}


			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container


			if(!$data['COOID']) {
				$json['Success'] = false;
				$json['Result'] = 'COO is missing';
				return json_encode($json);
			} else {
				$query234 = "select COO from COO where COOID = '".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."'";
				$q234 = mysqli_query($this->connectionlink,$query234);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row234 = mysqli_fetch_assoc($q234);
					$data['COO'] = $row234['COO'];
				}
			}


			//Start check If ContainerMPNLock Satisfies
			$contaner_lock = $this->isContainerMPNLock($data['ShippingID'],$data['UniversalModelNumber'],$data['ShippingContainerID']);
			if($contaner_lock['Success']) {                    				
			} else {
				$json['Success'] = false;
				$json['Result'] = $contaner_lock['Error'];
				return json_encode($json);
			}
			//End check If ContainerMPNLock Satisfied


			//Start check If ContainerMPNLock Satisfies
			$contaner_lock = $this->isContainerCOOLock($data['ShippingID'],$data['COOID'],$data['ShippingContainerID']);
			if($contaner_lock['Success']) {                    				
			} else {
				$json['Success'] = false;
				$json['Result'] = $contaner_lock['Error'];
				return json_encode($json);
			}
			//End check If ContainerMPNLock Satisfied

			//Start get serialnumber details
			// $query = "select a.*,m.part_type,co.COO from asset a 
			// left join catlog_creation m on a.UniversalModelNumber = m.mpn_id 
			// left join COO co on a.COOID = co.COOID 
			// where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";

			$query = "select a.*,co.COO from asset a 			
			left join COO co on a.COOID = co.COOID 
			where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$query = "select a.*,co.COO from asset a 
			left join COO co on a.COOID = co.COOID 
			where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and a.StatusID in ('1','9') ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				// if($row['StatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Serial Number Status is not active';			
				// 	return json_encode($json);
				// }

				//Start check for Seal Matching
				$query22 = "select sanitization_verification_id,sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc";
				$q22 = mysqli_query($this->connectionlink,$query22);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row22 = mysqli_fetch_assoc($q22);
					if($row22['sanitization_seal_id'] != $data['sanitization_seal_id'])	{
						$json['Success'] = false;
						$json['Result'] = 'Seal ID not matching';
						return json_encode($json);	
					} else {						
					}

					if($row22['sanitization_verification_id'] != $data['SanitizationVerificationID'])	{
						$json['Success'] = false;
						$json['Result'] = 'Sanitization Verification ID not matching';
						return json_encode($json);	
					} else {						
					}
				} else {
					// $json['Success'] = false;
					// $json['Result'] = 'Serial not involved in Sanitization';
					// return json_encode($json);
				}
				//End check for Seal Matching

				//Start get Shipping Disposition
				$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";				
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					if($row1['FacilityID'] != $row['FacilityID']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Facility is Different from Serial Number Facility';
						return json_encode($json);
					}

					//Start get Part type from MPN and validate MPN
					if($data['UniversalModelNumber'] != '')
					{
						$query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
						$q6 = mysqli_query($this->connectionlink,$query6);	
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row6 = mysqli_fetch_assoc($q6);
						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid MPN';
							return json_encode($json);
						}
					}
					else
					{
						$row6['part_type'] = '';
					}
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update asset set CustomPalletID = NULL,StatusID=8,DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
					$query4 = "update asset set CustomPalletID = NULL,StatusID=8,DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."')";
					//$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					//$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,serial_scan_time,mpn_scan_time,COO) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$row['COO'])."')";
					$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,serial_scan_time,mpn_scan_time,COO,COOID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['COO'])."','".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					//Start insert into tracking
					$action = 'Added to Shipment Container ('.$data['ShippingContainerID'].') of Shipment ('.$data['ShippingID'].')';
					$query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into tracking	
					
					//Start check IF MPN Changed
					if($row['UniversalModelNumber'] != $data['UniversalModelNumber']) {//If MPN of Asset Changed
						$query7 = "update asset set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
						$q7 = mysqli_query($this->connectionlink,$query7);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);			
						}

						$desc = "Asset MPN Changed from '".$row['UniversalModelNumber']."' to '".$data['UniversalModelNumber']."' in Shipment Prep Screen";
						$query8 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
						$q8 = mysqli_query($this->connectionlink,$query8);
					}
					//End check IF MPN Changed
					
					$json['Success'] = true;
					$json['Result'] = 'Serial Number added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ValidateSerialNumber ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			//$query = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and StatusID = 1";
			$query = "select a.*,co.COO from asset a 
			left join COO co on a.COOID = co.COOID 
			where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by a.StatusID";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] == '8') {
					$json['Success'] = false;		
					$json['Result'] = 'SN is already on RZRR';			
					return json_encode($json);
				}

				if($row['StatusID'] == '6') {
					$json['Success'] = false;		
					$json['Result'] = 'SN has been shipped';			
					return json_encode($json);
				}

				if($row['StatusID'] != '1' && $row['StatusID'] != '9') {
					$json['Success'] = false;		
					$json['Result'] = 'Serial Number Status is not active';			
					return json_encode($json);
				}
				//Start get Shipping Disposition
				$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }
					// if($row1['disposition_id'] != $row['disposition_id']) {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Removal Type is not matching with Serial Number';
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					//Start get Sanitization Verification ID If available
					$query22 = "select sanitization_verification_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc";
					$q22 = mysqli_query($this->connectionlink,$query22);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row22 = mysqli_fetch_assoc($q22);
						$json['sanitization_verification_id'] = $row22['sanitization_verification_id'];
						//$json['sanitization_verification_id'] = $query22;
					}
					//end get Sanitization Verificatio ID If available

					$json['AssetScanID'] = $row['AssetScanID'];
					$json['UniversalModelNumber'] = $row['UniversalModelNumber'];
					$json['COO'] = $row['COO'];
					$json['COOID'] = $row['COOID'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetContainerSerials($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No Data'
			);
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}

			$query = "select s.* from shipping_container_serials s where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'SerialNumber') {
							$query = $query . " AND s.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'InventorySerialNumber') {
							$query = $query . " AND s.InventorySerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'Notes') {
							$query = $query . " AND s.Notes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ShippingContainerID') {
							$query = $query . " AND s.ShippingContainerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'CreatedDate') {
							$query = $query . " AND s.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'UniversalModelNumber') {
							$query = $query . " AND s.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'part_type') {
							$query = $query . " AND s.part_type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ServerSerialNumber') {
							$query = $query . " AND s.ServerSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'MediaSerialNumber') {
							$query = $query . " AND s.MediaSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'COO') {
							$query = $query . " AND s.COO like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}

			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				if($data['OrderBy'] == 'SerialNumber') {
					$query = $query . " order by s.SerialNumber ".$order_by_type." ";
				} else if($data['OrderBy'] == 'Notes') {
					$query = $query . " order by s.Notes ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ShippingContainerID') {
					$query = $query . " order by s.ShippingContainerID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CreatedDate') {
					$query = $query . " order by s.CreatedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'UniversalModelNumber') {
					$query = $query . " order by s.UniversalModelNumber ".$order_by_type." ";
				} else if($data['OrderBy'] == 'part_type') {
					$query = $query . " order by s.part_type ".$order_by_type." ";
				} else if($data['OrderBy'] == 'InventorySerialNumber') {
					$query = $query . " order by s.InventorySerialNumber ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ServerSerialNumber') {
					$query = $query . " order by s.ServerSerialNumber ".$order_by_type." ";
				} else if($data['OrderBy'] == 'MediaSerialNumber') {
					$query = $query . " order by s.MediaSerialNumber ".$order_by_type." ";
				} else if($data['OrderBy'] == 'COO') {
					$query = $query . " order by s.COO ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by CreatedDate desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink))	{
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Serials Available";
			}
			
			if($data['skip'] == 0) {

				$query1 = "select count(*) from shipping_container_serials s where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							if($key == 'SerialNumber') {
								$query1 = $query1 . " AND s.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'InventorySerialNumber') {
								$query1 = $query1 . " AND s.InventorySerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'Notes') {
								$query1 = $query1 . " AND s.Notes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ShippingContainerID') {
								$query1 = $query1 . " AND s.ShippingContainerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'CreatedDate') {
								$query1 = $query1 . " AND s.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'UniversalModelNumber') {
								$query1 = $query1 . " AND s.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'part_type') {
								$query1 = $query1 . " AND s.part_type like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ServerSerialNumber') {
								$query1 = $query1 . " AND s.ServerSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'MediaSerialNumber') {
								$query1 = $query1 . " AND s.MediaSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}	
							
							if($key == 'COO') {
								$query1 = $query1 . " AND s.COO like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}

			//Start get total Servers scanned
			$query118 = "select count(*) from shipping_container_serials s where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and part_type = 'Server' and ServerSerialNumber != ''";
			$q118 = mysqli_query($this->connectionlink,$query118);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row118 = mysqli_fetch_assoc($q118);
				$scanned_servers_count = $row118['count(*)'];
				$json['Scanned_Servers'] = $scanned_servers_count;
			} else {
				$json['Scanned_Servers'] = '0';
			}
			//End get total Servers scanned

			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


	public function DeleteSerialFromContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				if($shipping['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Shipment';
				return json_encode($json);		
			}
			//End get Shiping Disposition

			//Start get disposition from shipping container
			if($data['ShippingContainerID'] != '') {
				$query112 = "select disposition_id from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";

				$q112 = mysqli_query($this->connectionlink,$query112);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$shipping_container = mysqli_fetch_assoc($q112);
				} else {
					$json['Success'] = false;		
					$json['Result'] = 'Invalid Shipment Container';			
					return json_encode($json);
				}
			}
			//End get dispostion from shipping container

			//Start validate BINNAME
			$query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$custompallet = mysqli_fetch_assoc($q);
				if($custompallet['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}
				//if($custompallet['disposition_id'] != $shipping['disposition_id']) {
				if($custompallet['disposition_id'] != $shipping_container['disposition_id']) {
					$json['Success'] = false;
					$json['Result'] = 'Serial Disposition is different from BIN Disposition';
					return json_encode($json);
				}
				if($custompallet['InventoryBased'] == '1' && ($data['SerialNumber'] != '' || $data['ServerSerialNumber'] != '')) {
					$json['Success'] = false;
					$json['Result'] = 'BIN is meant for Sub Component';
					return json_encode($json);
				}
				if($custompallet['InventoryBased'] == '0' && $data['InventorySerialNumber'] != '') {
					$json['Success'] = false;
					$json['Result'] = 'BIN is not meant for Sub Component';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
				return json_encode($json);
			}
			//End validate BINNAME
			
			//Start Delete Serial from Shipment Container
			$query2 = "delete from shipping_container_serials where SerialID = '".mysqli_real_escape_string($this->connectionlink,$data['SerialID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}	
			//End Delete Serial from Shipment Container

			//Start move asset to BIN
			if($data['SerialNumber'] != '') {
				$query6 = "insert into custompallet_items (CustomPalletID,AssetScanID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."',NOW(),'1')";
			} else if($data['InventoryID'] > 0) {
				$query6 = "insert into custompallet_items (CustomPalletID,InventoryID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['InventoryID'])."',NOW(),'1')";
			} else if($data['ServerID'] > 0) {
				$query6 = "insert into custompallet_items (CustomPalletID,ServerID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerID'])."',NOW(),'1')";
			} else if ($data['MediaID'] > 0) {
				$query6 = "insert into custompallet_items (CustomPalletID,MediaID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."',NOW(),'1')";
			}
			$q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$query9 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."'";
			$q9 = mysqli_query($this->connectionlink,$query9);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if($data['SerialNumber'] != '') {
				$query3 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '1',ShippingID = NULL,ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL,RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$desc = "Asset Removed from Shipment Container (Container ID : ".$data['ShippingContainerID'].") and Moved to BIN (BIN ID : ".$custompallet['BinName'].")";
				$query7 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else if($data['InventoryID'] > 0) {
				$query3 = "update inventory set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',InventoryStatusID = '1',ShippingID = NULL,ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$data['InventoryID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}	
			} else if($data['ServerID'] > 0) {
				$query3 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '1',ShippingID = NULL,ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL where ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['ServerID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else if($data['MediaID'] > 0) {

				$query3 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '4',ShippingContainerID = NULL,DateAddedToShipmentContainer = NULL,ShippingID = NULL where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$desc = "Media Removed from Shipment Container (Container ID : ".$data['ShippingContainerID'].") and Moved to BIN (BIN ID : ".$custompallet['BinName'].")";
				//$query7 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$query7 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy,MediaType) values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['part_type'])."')";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

			}

			//End move asset to BIN

			$json['Success'] = true;
			$json['Result'] = 'Serial Number removed from Shipment Container';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function DeleteByProductFromContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				if($shipping['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Shipment';
				return json_encode($json);		
			}
			//End get Shiping Disposition


			
			
			//Start Delete Serial from Shipment Container
			$query2 = "delete from shipping_container_serials where SerialID = '".mysqli_real_escape_string($this->connectionlink,$data['SerialID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}	
			//End Delete Serial from Shipment Container

			$json['Success'] = true;
			$json['Result'] = 'Byproduct removed from Shipment Container';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function DeleteByProductFromShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Container';
				return json_encode($json);
			}

			//Start Delete Serial from Shipment Container
			$query2 = "delete from shipping_container_serials where SerialID = '".mysqli_real_escape_string($this->connectionlink,$data['SerialID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}	
			//End Delete Serial from Shipment Container

			$json['Success'] = true;
			$json['Result'] = 'Byproduct removed from Shipment Container';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetActiveShipments($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No Data'
			);
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}

			$query = "select s.*,GROUP_CONCAT(DISTINCT d.disposition ORDER BY d.disposition ASC SEPARATOR ', ') AS disposition,v.VendorName,v.ContactName,f.FacilityName,df.FacilityName as DestinationFacilityName from shipping s  
			left join shipping_containers sc on s.ShippingID = sc.ShippingID 
			left join disposition d on sc.disposition_id = d.disposition_id 
			left join vendor v on s.VendorID = v.VendorID 
			left join facility f on s.FacilityID = f.FacilityID  
			left join facility df on s.DestinationFacilityID = df.FacilityID 
			where ShipmentStatusID = '1' ";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'ShippingID') {
							$query = $query . " AND s.ShippingID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'VendorName') {
							$query = $query . " AND v.VendorName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ContactName') {
							$query = $query . " AND v.ContactName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'DestinationFacilityName') {
							$query = $query . " AND df.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			$query = $query. " group by s.ShippingID ";
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				if($data['OrderBy'] == 'ShippingID') {
					$query = $query . " order by s.ShippingID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'VendorName') {
					$query = $query . " order by v.VendorName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ContactName') {
					$query = $query . " order by v.ContactName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'DestinationFacilityName') {
					$query = $query . " order by df.FacilityName ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by s.CreatedDate desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink))	{
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$row['Containers'] = array();
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tickets Available";
			}
			
			if($data['skip'] == 0) {

				$query1 = "select count(*) from shipping s  
				left join shipping_containers sc on s.ShippingID = sc.ShippingID 
				left join disposition d on sc.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on s.FacilityID = f.FacilityID 
				where ShipmentStatusID = '1' ";

				$query1 = "select count(*) from shipping s  				
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on s.FacilityID = f.FacilityID 
				left join facility df on s.DestinationFacilityID = df.FacilityID 
				where ShipmentStatusID = '1' ";
				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							if($key == 'ShippingID') {
								$query1 = $query1 . " AND s.ShippingID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'disposition') {
								$query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'VendorName') {
								$query1 = $query1 . " AND v.VendorName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ContactName') {
								$query1 = $query1 . " AND v.ContactName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}	
							if($key == 'FacilityName') {
								$query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}	
							
							if($key == 'DestinationFacilityName') {
								$query1 = $query1 . " AND df.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
						}
					}
				}				
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


	public function GetShipmentContainers ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}			
			$query = "select s.*,v.ContactName as DestinationPOC from shipping s 
			left join vendor v on s.VendorID = v.VendorID 
			where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				//Start get Shipment Containers
				$containers = array();
				$query2 = "select d.*,p.packageName,s.Status,l.LocationName,dd.disposition from shipping_containers d 
				left join package p on d.idPackage = p.idPackage 
				left join location l on d.LocationID = l.LocationID  
				left join disposition dd on d.disposition_id = dd.disposition_id 
				left join shipping_status s on d.StatusID = s.ShipmentStatusID where d.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$row['ShippingID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;					
					while($row2 = mysqli_fetch_assoc($q2)) {
						$containers[$i] = $row2;
						$i++;
					}
					$row['Containers'] = $containers;
				} else {
					$row['Containers'] = array();
				}
				//End get Shipment Containers				
				$json['Success'] = true;			
				$json['Result'] = $containers;			
				return json_encode($json);
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Ticket ID';			
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ShipShipment ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Removal';
				return json_encode($json);
			}

			//Start get shipping details
			$query1 = "select * from shipping where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;			
					$json['Result'] = 'Shipment Facility is different from User Facility';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment';
				return json_encode($json);	
			}
			//End get shipping details

			//Start get shipping containers
			$shipping_containers = '';
			$query112 = "select ShippingContainerID,ASNContainer,idPallet,disposition_id from shipping_containers where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q112 = mysqli_query($this->connectionlink,$query112);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row112 = mysqli_fetch_assoc($q112)) {
					$shipping_containers = $shipping_containers . "'".$row112['ShippingContainerID']."'";
					$shipping_containers = $shipping_containers . ',';
				}
			}
			if($shipping_containers != '') {
				$shipping_containers = rtrim($shipping_containers, ",");
			}
			//End get shipping containers

			$chkdt = $data['ApprovedDate'];
			$chkdtarr=explode("GMT",$chkdt);
			$newdt= strtotime($chkdtarr[0]);
			$data['ApprovedDate'] = date("Y-m-d",$newdt);

			$chkdt = $data['ShippedTime'];
			$chkdtarr=explode("GMT",$chkdt);
			$newdt= strtotime($chkdtarr[0]);
			$data['ShippedTime'] = date("H:i",$newdt);

			$chkdt = $data['ShippedDate'];
			$chkdtarr=explode("GMT",$chkdt);
			$newdt= strtotime($chkdtarr[0]);
			$data['ShippedDate'] = date("Y-m-d",$newdt);

			//Start loop through shipping serials to find any media not met SLA
			$query11 = "select s.*,sh.ShippingID,m.idPallet FROM shipping_container_serials s,shipping_containers c,shipping sh,speed_media_recovery m where s.ShippingContainerID = c.ShippingContainerID and c.ShippingID = sh.ShippingID and s.MediaID > 0 and sh.ShippingID= '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' and s.MediaID = m.MediaID";
			$q11 = mysqli_query($this->connectionlink,$query11);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row11 = mysqli_fetch_assoc($q11)) {
					$rack_received_since = $this->getTimeSinceRackReceived($row11['idPallet']);
					if($rack_received_since['Success'] == true) {
						if($rack_received_since['HoursSinceReceived'] < 68) {

						} else {
							$json['Success'] = false;
							$json['Result'] = '68 Hour SLA for Removing Media was not met for Media Serial '.$row11['MediaSerialNumber'].' (Container ID : '.$row11['idPallet'].')';
							return json_encode($json);
						}
					} else {
						$json['Success'] = false;
						$json['Result'] = $rack_received_since['Error'];
						return json_encode($json);
					}
				}
			}
			//Start loop through shipping serials to find any media not met SLA



			$query1123 = "select ShippingContainerID,ASNContainer,idPallet,BatchRecovery,disposition_id from shipping_containers where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";			
			$q1123 = mysqli_query($this->connectionlink,$query1123);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row1123 = mysqli_fetch_assoc($q1123)) {
					if($row1123['ASNContainer'] == '1') {//ASN Container
						$PalletID = $row1123['ShippingContainerID'];
						//start check If Load exists
						$query1x = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
						$q1x = mysqli_query($this->connectionlink,$query1x);
						
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row1x = mysqli_fetch_assoc($q1x);
							if($row1x['count(*)'] == 0) {
								$query2x = "insert into loads (LoadId,DateCreated,CreatedBy,LoadType,FacilityID,SourceFacilityID) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',NOW(),'".$_SESSION['user']['UserId']."','Inter Facility Shipment','".mysqli_real_escape_string($this->connectionlink,$row1['DestinationFacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$row1['FacilityID'])."')";
								$q2x = mysqli_query($this->connectionlink,$query2x);
								
								//Start insert in load tracking
								$query10x = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','New Inbound Shipment Ticket Created through Inter Facility Transfer',NOW(),'".$_SESSION['user']['UserId']."')";
								$q10x = mysqli_query($this->connectionlink,$query10x);
								//End insert in load tracking
			
							}
						}
						//End check If Load exists



						//Start check If Pallet exists
						$query3x = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";
						$q3x = mysqli_query($this->connectionlink,$query3x);
						
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row3x = mysqli_fetch_assoc($q3x);
							if($row3x['count(*)'] == 0) {																
								if($rowpal['PackagingCode'] != '') {
									
									$query11 = "select * from material_types where MaterialType = '".mysqli_real_escape_string($this->connectionlink,$rowpal['PackagingCode'])."'";
									$q11 = mysqli_query($this->connectionlink, $query11);                        
									if (mysqli_affected_rows($this->connectionlink) > 0) {
										$row1 = mysqli_fetch_assoc($q1);
										$MaterialTypeID = $row11['MaterialTypeID'];
									} else {
										$MaterialTypeID = 0;
									}                                                
								} else {
									$MaterialTypeID = 0;
									$idCustomertype = 0;
								}
			
								if($MaterialTypeID > 0 && $AWSCustomerID > 0) {
									$query12 = "select * from source_type_configuration where Status = 'Active' and FacilityID = '".$rowfac['FacilityID']."' and AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$AWSCustomerID)."' and MaterialTypeID = '".mysqli_real_escape_string($this->connectionlink,$MaterialTypeID)."'";
									$q12 = mysqli_query($this->connectionlink, $query12);
									if (mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if (mysqli_affected_rows($this->connectionlink) > 0) {
										$row12 = mysqli_fetch_assoc($q12);
										$idCustomertype = $row12['idCustomertype'];
									} else {
										$idCustomertype = 0;
									}
								} else {
									$idCustomertype = 0;
								}
			
								$query4 = "insert into pallets (idPallet,LoadId,status,CreatedDate,CreatedBy,Type,SealNo1,SealNo2,SealNo3,SealNo4,PalletFacilityID,MaterialType,disposition_id,ASNContainer,FacilityTransferContainer";								
								$query4 = $query4 . ") values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','6',NOW(),'".$_SESSION['user']['UserId']."','Inter Facility Transfer','n/a','n/a','n/a','n/a','".mysqli_real_escape_string($this->connectionlink,$row1['DestinationFacilityID'])."','','".mysqli_real_escape_string($this->connectionlink,$row1123['disposition_id'])."','1','1'";
								$query4 = $query4 . ")";
								$q4 = mysqli_query($this->connectionlink,$query4);
								$query12 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','Container Created through Inter Facility Transfer','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
								$q12 = mysqli_query($this->connectionlink,$query12);
								
							}
						}
						//end check If Pallet exists
			
						//Start check If Pallet Item exists
						$query3x = "select id from pallet_items where palletId = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";
						$q3x = mysqli_query($this->connectionlink,$query3x);
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row3x = mysqli_fetch_assoc($q3x);
							$pallet_item_id = $row3x['id'];
							$query5x = "update pallet_items set quantity = quantity + 1 where id = '".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."'";
							$q5x = mysqli_query($this->connectionlink,$query5x);
							
						} else {
							$query4x = "insert into pallet_items (palletId,quantity,CreatedDate,CreatedBy,Type) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','1',NOW(),'".$_SESSION['user']['UserId']."','Inter Facility Transfer')";
							$q4x = mysqli_query($this->connectionlink,$query4x);
							$pallet_item_id = mysqli_insert_id($this->connectionlink);
						}
						//end check If Pallet Item exists

						//Start Copy all asn assets of the pallet to asn_assets_shipped table
						$query16 = "insert into asn_assets_shipped (ID, LoadId, idPallet, PalletItemsID, SerialNumber, UniversalModelNumber, AssetScanID, CreatedDate, CreatedBy, Type, AssetCreatedDate, AssetCreatedBy, UploadID, OriginalPalletID, apn_id, part_type, idManufacturer, ActualSerialNumber, ShippingContainerID, ShippingContainerAddedDate, ShippingContainerAddedBy, ShippingID) 
							select ID, LoadId, idPallet, PalletItemsID, SerialNumber, UniversalModelNumber, AssetScanID, CreatedDate, CreatedBy, Type, AssetCreatedDate, AssetCreatedBy, UploadID, OriginalPalletID, apn_id, part_type, idManufacturer, ActualSerialNumber, ShippingContainerID, ShippingContainerAddedDate, ShippingContainerAddedBy, '".$data['ShippingID']."' from asn_assets where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$row1123['ShippingContainerID'])."'
						";
						$q16 = mysqli_query($this->connectionlink, $query16);   
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;			
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						//End Copy all asn assets of the pallet to asn_assets_shipped table


						//Start update existing asn_assets with new pallet id and load id
						$query17 = "update asn_assets set LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."',PalletItemsID = '".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."',Type = 'Inter Facility Transfer',ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL,old_idPallet = '".mysqli_real_escape_string($this->connectionlink,$row1123['idPallet'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$row1123['ShippingContainerID'])."'";
						$q17 = mysqli_query($this->connectionlink, $query17);
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;			
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						//End update existing asn_assets with new pallet id and load id



					}

					if($row1123['ASNContainer'] == '0' && $row1['DestinationFacilityID'] > 0) {


						$PalletID = $row1123['ShippingContainerID'];
						//start check If Load exists
						$query1x = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
						$q1x = mysqli_query($this->connectionlink,$query1x);
						
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row1x = mysqli_fetch_assoc($q1x);
							if($row1x['count(*)'] == 0) {
								$query2x = "insert into loads (LoadId,DateCreated,CreatedBy,LoadType,FacilityID,SourceFacilityID) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',NOW(),'".$_SESSION['user']['UserId']."','Inter Facility Shipment','".mysqli_real_escape_string($this->connectionlink,$row1['DestinationFacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$row1['FacilityID'])."')";
								$q2x = mysqli_query($this->connectionlink,$query2x);
								
								//Start insert in load tracking
								$query10x = "insert into load_tracking (LoadId,`Action`,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','New Inbound Shipment Ticket Created through Inter Facility Transfer',NOW(),'".$_SESSION['user']['UserId']."')";
								$q10x = mysqli_query($this->connectionlink,$query10x);
								//End insert in load tracking
			
							}
						}
						//End check If Load exists



						//Start check If Pallet exists
						$query3x = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";
						$q3x = mysqli_query($this->connectionlink,$query3x);
						
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row3x = mysqli_fetch_assoc($q3x);
							if($row3x['count(*)'] == 0) {																
								if($rowpal['PackagingCode'] != '') {
									
									$query11 = "select * from material_types where MaterialType = '".mysqli_real_escape_string($this->connectionlink,$rowpal['PackagingCode'])."'";
									$q11 = mysqli_query($this->connectionlink, $query11);                        
									if (mysqli_affected_rows($this->connectionlink) > 0) {
										$row1 = mysqli_fetch_assoc($q1);
										$MaterialTypeID = $row11['MaterialTypeID'];
									} else {
										$MaterialTypeID = 0;
									}                                                
								} else {
									$MaterialTypeID = 0;
									$idCustomertype = 0;
								}
			
								if($MaterialTypeID > 0 && $AWSCustomerID > 0) {
									$query12 = "select * from source_type_configuration where Status = 'Active' and FacilityID = '".$rowfac['FacilityID']."' and AWSCustomerID = '".mysqli_real_escape_string($this->connectionlink,$AWSCustomerID)."' and MaterialTypeID = '".mysqli_real_escape_string($this->connectionlink,$MaterialTypeID)."'";
									$q12 = mysqli_query($this->connectionlink, $query12);
									if (mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if (mysqli_affected_rows($this->connectionlink) > 0) {
										$row12 = mysqli_fetch_assoc($q12);
										$idCustomertype = $row12['idCustomertype'];
									} else {
										$idCustomertype = 0;
									}
								} else {
									$idCustomertype = 0;
								}
			
								$query4 = "insert into pallets (idPallet,LoadId,status,CreatedDate,CreatedBy,Type,SealNo1,SealNo2,SealNo3,SealNo4,PalletFacilityID,MaterialType,BatchRecovery,disposition_id,FacilityTransferContainer";								
								$query4 = $query4 . ") values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."','6',NOW(),'".$_SESSION['user']['UserId']."','Inter Facility Transfer','n/a','n/a','n/a','n/a','".mysqli_real_escape_string($this->connectionlink,$row1['DestinationFacilityID'])."','','".mysqli_real_escape_string($this->connectionlink,$row1123['BatchRecovery'])."','".mysqli_real_escape_string($this->connectionlink,$row1123['disposition_id'])."','1'";
								$query4 = $query4 . ")";
								$q4 = mysqli_query($this->connectionlink,$query4);
								$query12 = "insert into pallet_tracking (idPallet,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','Container Created through Inter Facility Transfer','','',NOW(),'".$_SESSION['user']['UserId']."','','','')";			
								$q12 = mysqli_query($this->connectionlink,$query12);
								
							}
						}
						//end check If Pallet exists
			
						//Start check If Pallet Item exists
						$query3x = "select id from pallet_items where palletId = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."'";
						$q3x = mysqli_query($this->connectionlink,$query3x);
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row3x = mysqli_fetch_assoc($q3x);
							$pallet_item_id = $row3x['id'];
							$query5x = "update pallet_items set quantity = quantity + 1 where id = '".mysqli_real_escape_string($this->connectionlink,$pallet_item_id)."'";
							$q5x = mysqli_query($this->connectionlink,$query5x);
							
						} else {
							$query4x = "insert into pallet_items (palletId,quantity,CreatedDate,CreatedBy,Type) values ('".mysqli_real_escape_string($this->connectionlink,$PalletID)."','1',NOW(),'".$_SESSION['user']['UserId']."','Inter Facility Transfer')";
							$q4x = mysqli_query($this->connectionlink,$query4x);
							$pallet_item_id = mysqli_insert_id($this->connectionlink);
						}
						//end check If Pallet Item exists

						//Start Copy all asn assets of the pallet to asn_assets_shipped table
						$query16 = "insert into asn_assets (LoadId, idPallet, PalletItemsID, SerialNumber, UniversalModelNumber,  CreatedDate, CreatedBy, Type,  part_type) 
							select '".$data['ShippingID']."', '".$PalletID."', '".$pallet_item_id."', SerialNumber, UniversalModelNumber,  NOW(), '".$_SESSION['user']['UserId']."', 'Inter Facility Transfer',  part_type from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$row1123['ShippingContainerID'])."'
						";

						$query16 = "INSERT INTO asn_assets 
						(LoadId, idPallet, PalletItemsID, SerialNumber, UniversalModelNumber, CreatedDate, CreatedBy, `Type`, part_type)
						SELECT '".mysqli_real_escape_string($this->connectionlink, $data['ShippingID'])."', 
							'".mysqli_real_escape_string($this->connectionlink, $PalletID)."', 
							'".mysqli_real_escape_string($this->connectionlink, $pallet_item_id)."', 
							CASE 
								WHEN AssetScanID > 0 THEN SerialNumber
								WHEN InventoryID > 0 THEN InventorySerialNumber
								WHEN ServerID > 0 THEN ServerSerialNumber
								WHEN MediaID > 0 THEN MediaSerialNumber
							END AS SerialNumber, 
							UniversalModelNumber, NOW(), 
							'".mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId'])."', 
							'Inter Facility Transfer', 
							part_type 
						FROM shipping_container_serials 
						WHERE ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink, $row1123['ShippingContainerID'])."'";
						$q16 = mysqli_query($this->connectionlink, $query16);   
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;			
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						//End Copy all asn assets of the pallet to asn_assets_shipped table
					}
				}
			}


			//Start update Inventory with Shipped Status
			//$query = "update inventory set InventoryStatusID = '5',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			//$query = "update inventory set InventoryStatusID = '6',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$query = "update inventory set InventoryStatusID = '6',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' where InventoryID in (select InventoryID from shipping_container_serials c,shipping_containers s where c.ShippingContainerID = s.ShippingContainerID and s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			//End update Inventory with Shipped Status

			//Start update Asset with Shipped Status
			//$query = "update asset set StatusID = '6',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			//$query = "update asset set StatusID = '6',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' where AssetScanID in (select AssetScanID from shipping_container_serials c,shipping_containers s where c.ShippingContainerID = s.ShippingContainerID and s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."')";
			$query = "update asset set StatusID = '6',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' where ShippingContainerID in (".$shipping_containers.")";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			//End update Asset with Shipped Status

			//Start  update Servers/Switches with Shipped Status
			$query = "update speed_server_recovery set  StatusID = 6,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."',ShippingID =  '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' where ServerID in (select ServerID from shipping_container_serials c,shipping_containers s where c.ShippingContainerID = s.ShippingContainerID and s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			//End update Servers/Switches with Shipped Status

			//Start update Media with Shipped status
			$query = "update speed_media_recovery set  StatusID = 6,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."',ShippingID =  '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' where MediaID in (select MediaID from shipping_container_serials c,shipping_containers s where c.ShippingContainerID = s.ShippingContainerID and s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."')";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			//End update Media with Shipped status

			//Start update Asset Tracking
			$desc = "Serial Number Shipped (Removal Date : ".$data['ShippedDate'].") through (Ticket ID : ".$data['ShippingID'].")";
			//$query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy)  select AssetScanID,'".$desc."','','',NOW(),'".$_SESSION['user']['UserId']."' from asset where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy)  select c.AssetScanID,'".$desc."','','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials c,shipping_containers s where c.ShippingContainerID = s.ShippingContainerID and s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				//$json['Result'] = mysqli_error($this->connectionlink);			
				$json['Result'] = $query2;			
				return json_encode($json);			
			}
			//End update Asset Tracking

			//Start update Media Tracking
			$desc = "Media Shipped (Removal Date : ".$data['ShippedDate'].") through (Ticket ID : ".$data['ShippingID'].")";
			//$query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy)  select c.AssetScanID,'".$desc."','','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials c,shipping_containers s where c.ShippingContainerID = s.ShippingContainerID and s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$query2 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy) 
			select c.MediaID,c.MediaSerialNumber,'".$desc."','',NOW(),'".$_SESSION['user']['UserId']."' from shipping_container_serials c,shipping_containers s where c.ShippingContainerID = s.ShippingContainerID and s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				//$json['Result'] = mysqli_error($this->connectionlink);			
				$json['Result'] = $query2;			
				return json_encode($json);			
			}
			//End update Media Tracking
			
			//Start unlocking shipment containers
			$query5 = "select * from shipping_containers where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row5 = mysqli_fetch_assoc($q5)) {
					if($row5['LocationID'] > 0) {
						$query6 = "update location set Locked='2',currentItemType='',currentItemID='' where LocationID = '".mysqli_real_escape_string($this->connectionlink,$row5['LocationID'])."' ";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;			
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
					}
				}
			}
			//end unlocking shipment containers

			//Start update Shipping Containers
			$query3 = "update shipping_containers set StatusID = '3',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',LocationID = NULL where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			//End update Shipping Containers

			//Start update Shipping
			$query4 = "update shipping set ShipmentStatusID = '3',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShipmentProcessedDate = NOW(),ShipmentProcessedBy = '".$_SESSION['user']['UserId']."',NextStep = '".mysqli_real_escape_string($this->connectionlink,$data['NextStep'])."',ApproverLogin = '".mysqli_real_escape_string($this->connectionlink,$data['ApproverLogin'])."',ApprovedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ApprovedDate'])."',ShippedDate = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedDate'])."',ShippedTime = '".mysqli_real_escape_string($this->connectionlink,$data['ShippedTime'])."',EscortLogin = '".mysqli_real_escape_string($this->connectionlink,$data['EscortLogin'])."',CarrierID = '".mysqli_real_escape_string($this->connectionlink,$data['CarrierID'])."',VehicleID = '".mysqli_real_escape_string($this->connectionlink,$data['VehicleID'])."',Trailer = '".mysqli_real_escape_string($this->connectionlink,$data['Trailer'])."',TrailerSeal = '".mysqli_real_escape_string($this->connectionlink,$data['TrailerSeal'])."',ShipmentTracking = '".mysqli_real_escape_string($this->connectionlink,$data['ShipmentTracking'])."',PONumber = '".mysqli_real_escape_string($this->connectionlink,$data['PONumber'])."',RemovalController = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			//End update Shipping

			$json['Success'] = true;
			$json['Result'] = 'Ticket Removed';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetRemovedShipments($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No Data'
			);
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Removed Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Removed Shipments Page';
				return json_encode($json);
			}
			
			$query = "select s.*,d.disposition,v.VendorName,v.ContactName,f.FacilityName,df.FacilityName as DestinationFacilityName from shipping s  
			left join disposition d on s.disposition_id = d.disposition_id 
			left join vendor v on s.VendorID = v.VendorID 
			left join facility f on s.FacilityID = f.FacilityID 
			left join facility df on s.DestinationFacilityID = df.FacilityID
			where ShipmentStatusID != '1' ";

			$query = "select s.*,GROUP_CONCAT(DISTINCT d.disposition ORDER BY d.disposition ASC SEPARATOR ', ') AS disposition,v.VendorName,v.ContactName,f.FacilityName,df.FacilityName as DestinationFacilityName from shipping s  
			left join shipping_containers sc on s.ShippingID = sc.ShippingID 
			left join disposition d on sc.disposition_id = d.disposition_id 
			left join vendor v on s.VendorID = v.VendorID 
			left join facility f on s.FacilityID = f.FacilityID 
			left join facility df on s.DestinationFacilityID = df.FacilityID
			where ShipmentStatusID != '1' ";
			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'ShippingID') {
							$query = $query . " AND s.ShippingID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'VendorName') {
							$query = $query . " AND v.VendorName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ContactName') {
							$query = $query . " AND v.ContactName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ApprovedDate') {
							$query = $query . " AND s.ApprovedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ShippedDate') {
							$query = $query . " AND s.ShippedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'DestinationFacilityName') {
							$query = $query . " AND df.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			$query = $query. " group by s.ShippingID ";
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				if($data['OrderBy'] == 'ShippingID') {
					$query = $query . " order by s.ShippingID ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'VendorName') {
					$query = $query . " order by v.VendorName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ContactName') {
					$query = $query . " order by v.ContactName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ApprovedDate') {
					$query = $query . " order by s.ApprovedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'ShippedDate') {
					$query = $query . " order by s.ShippedDate ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'DestinationFacilityName') {
					$query = $query . " order by df.FacilityName ".$order_by_type." ";
				}
			} else {
				$query = $query . " order by s.CreatedDate desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink))	{
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$ApprovedDate = strtotime( $row['ApprovedDate'] );
					$row['ApprovedDate'] = date( 'M d, Y', $ApprovedDate );

					$ShippedDate = strtotime( $row['ShippedDate'] );
					$row['ShippedDate'] = date( 'M d, Y', $ShippedDate );

					$row['Containers'] = array();
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tickets Available";
			}
			
			if($data['skip'] == 0) {

				$query1 = "select count(*) from shipping s  
				left join disposition d on s.disposition_id = d.disposition_id 
				left join vendor v on s.VendorID = v.VendorID 
				left join facility f on s.FacilityID = f.FacilityID 
				left join facility df on s.DestinationFacilityID = df.FacilityID
				where ShipmentStatusID != '1' ";
				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							if($key == 'ShippingID') {
								$query1 = $query1 . " AND s.ShippingID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'disposition') {
								$query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'VendorName') {
								$query1 = $query1 . " AND v.VendorName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ContactName') {
								$query1 = $query1 . " AND v.ContactName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ApprovedDate') {
								$query1 = $query1 . " AND s.ApprovedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ShippedDate') {
								$query1 = $query1 . " AND s.ShippedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'FacilityName') {
								$query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'DestinationFacilityName') {
								$query1 = $query1 . " AND df.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}													
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function GetRemovedShipmentContainers ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Removed Shipments')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Removed Shipments Page';
				return json_encode($json);
			}			
			$query = "select s.*,v.ContactName as DestinationPOC from shipping s 
			left join vendor v on s.VendorID = v.VendorID 
			where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				//Start get Shipment Containers
				$containers = array();
				$query2 = "select d.*,p.packageName,s.Status,dd.disposition from shipping_containers d 
				left join disposition dd on d.disposition_id = dd.disposition_id 
				left join package p on d.idPackage = p.idPackage 
				left join shipping_status s on d.StatusID = s.ShipmentStatusID where d.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$row['ShippingID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$i = 0;					
					while($row2 = mysqli_fetch_assoc($q2)) {
						$containers[$i] = $row2;
						$i++;
					}
					$row['Containers'] = $containers;
				} else {
					$row['Containers'] = array();
				}
				//End get Shipment Containers				
				$json['Success'] = true;			
				$json['Result'] = $containers;			
				return json_encode($json);
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Ticket ID';			
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetFacilities ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select * from facility where FacilityStatus = '1' order by FacilityName";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Facilities Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function DeleteContainerFromShipment ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				// if($shipping['ShipmentStatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Shipment Status is not active';			
				// 	return json_encode($json);
				// }
				
				if($shipping['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not active';			
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container';
				return json_encode($json);		
			}
			//End get Shiping Disposition


			//Start validate BINNAME
			$query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$custompallet = mysqli_fetch_assoc($q);
				if($custompallet['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}
				if($custompallet['disposition_id'] != $shipping['disposition_id']) {
					$json['Success'] = false;
					$json['Result'] = 'Serial Disposition is different from BIN Disposition';
					return json_encode($json);
				}
				if($custompallet['FacilityID'] != $shipping['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'Facility Mismatch';
					return json_encode($json);
				}
				// if($custompallet['InventoryBased'] == '1') {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'BIN is meant for Inventory';
				// 	return json_encode($json);
				// }
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
				return json_encode($json);
			}
			//End validate BINNAME
		
			$query2 = "select * from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row2 = mysqli_fetch_assoc($q2)) {
					if($row2['AssetScanID'] > 0) {
						$query6 = "insert into custompallet_items (CustomPalletID,AssetScanID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row2['AssetScanID'])."',NOW(),'1')";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					} else if($row2['InventoryID'] > 0) {
						$query6 = "insert into custompallet_items (CustomPalletID,InventoryID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row2['InventoryID'])."',NOW(),'1')";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					} else if($row2['ServerID'] >  0) {
						$query6 = "insert into custompallet_items (CustomPalletID,ServerID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row2['ServerID'])."',NOW(),'1')";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					} else if($row2['MediaID'] > 0) {
						$query6 = "insert into custompallet_items (CustomPalletID,MediaID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row2['MediaID'])."',NOW(),'1')";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					}

					$query9 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."'";
					$q9 = mysqli_query($this->connectionlink,$query9);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if($row2['AssetScanID'] > 0) {
						$query3 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '1',ShippingID = NULL, ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL,RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row2['AssetScanID'])."'";
						$q3 = mysqli_query($this->connectionlink,$query3);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

						$desc = "Asset Removed from Shipment Container (Container ID : ".$data['ShippingContainerID'].") and Moved to BIN (BIN ID : ".$custompallet['BinName'].")";
						$query7 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row2['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
						$q7 = mysqli_query($this->connectionlink,$query7);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					} else if($row2['InventoryID'] > 0) {
						$query3 = "update inventory set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',InventoryStatusID = '1',ShippingID = NULL,ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$row2['InventoryID'])."'";
						$q3 = mysqli_query($this->connectionlink,$query3);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					} else if($row2['ServerID'] >  0) {
						$query3 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '1',ShippingID = NULL,ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row2['ServerID'])."'";
						$q3 = mysqli_query($this->connectionlink,$query3);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					} else if($row2['MediaID'] > 0) {
						$query3 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '4',ShippingContainerID = NULL,DateAddedToShipmentContainer = NULL,ShippingID = NULL where MediaID = '".mysqli_real_escape_string($this->connectionlink,$row2['MediaID'])."'";
						$q3 = mysqli_query($this->connectionlink,$query3);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						
						$desc = "Media Removed from Shipment Container (Container ID : ".$data['ShippingContainerID'].") and Moved to BIN (BIN ID : ".$custompallet['BinName'].")";
						//$query7 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
						$query7 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy,MediaType) values ('".mysqli_real_escape_string($this->connectionlink,$row2['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$row2['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row2['part_type'])."')";
						$q7 = mysqli_query($this->connectionlink,$query7);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
					}

					$query8 = "delete from shipping_container_serials where SerialID = '".mysqli_real_escape_string($this->connectionlink,$row2['SerialID'])."'";
					$q8 = mysqli_query($this->connectionlink,$query8);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}
			}

			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				if($container['LocationID'] > 0) {
					$query11 = "update location set Locked='2',currentItemType='',currentItemID='' where LocationID = '".mysqli_real_escape_string($this->connectionlink,$container['LocationID'])."' ";
					$q11 = mysqli_query($this->connectionlink,$query11);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}	
				}

			}
			//End get shipping container details

			$query8 = "delete from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q8 = mysqli_query($this->connectionlink,$query8);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Serial Numbers moved to BIN and Shipment Container is removed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetDispositioNextStep ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}
			$query = "select NextStep from disposition where disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' ";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				$json['Success'] = true;
				$json['Result'] = $row['NextStep'];
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Type";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function Vendor ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if($data['VendorID']=='') { //If New Class
		//$sql = "CALL ClassCreate('".mysqli_real_escape_string($this->connectionlink,$data['ProductClassName'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProductClassDesc'])."','".mysqli_real_escape_string($this->connectionlink,$data['ProductClassStatus'])."')";
			$duplicate = $this->CheckDuplicate('New','vendor','VendorName',$data['VendorName'],true,'','');	
			if($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'Vendor already Exists';
				return json_encode($json);
			}
			$query = "INSERT INTO `vendor` (`VendorID`,`VendorName`,`ShortCode`,`StreetAddress`,`City`,`State`,`Country`,`Zip`,`Phone`,`Fax`,`Email`,`Website`,`Title`,`ContactName`,`ContactPhone`,`ContactEmail`,`SpecialInstructions`,`BillContact`,`BillCity`,`BillZip`,`BillPhone`,`BillEmail`,`BillAddress`,`BillState`,`BillCountry`,`BillFax`,`Status`,`AccountID`,`CreatedBy`,`DateCreated`,`VendorCategory`,`PaymentCode`,`InvoiceCode`,`PaymentTerms`,`SAPCustomer`,`DocCurrency`,`vendor_currency`,`TaxCode`,ContainerMPNLock,WasteCertificationID,ContainerCOOLock) 
			VALUES (NULL, '".mysqli_real_escape_string($this->connectionlink,$data['VendorName'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShortCode'])."','".mysqli_real_escape_string($this->connectionlink,$data['StreetAddress'])."','".mysqli_real_escape_string($this->connectionlink,$data['City'])."','".mysqli_real_escape_string($this->connectionlink,$data['State'])."','".mysqli_real_escape_string($this->connectionlink,$data['Country'])."','".mysqli_real_escape_string($this->connectionlink,$data['Zip'])."','".mysqli_real_escape_string($this->connectionlink,$data['Phone'])."','".mysqli_real_escape_string($this->connectionlink,$data['Fax'])."','".mysqli_real_escape_string($this->connectionlink,$data['Email'])."','".mysqli_real_escape_string($this->connectionlink,$data['Website'])."','".mysqli_real_escape_string($this->connectionlink,$data['Title'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContactName'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContactPhone'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContactEmail'])."','".mysqli_real_escape_string($this->connectionlink,$data['SpecialInstructions'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillContact'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillCity'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillZip'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillPhone'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillEmail'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillAddress'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillState'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillCountry'])."','".mysqli_real_escape_string($this->connectionlink,$data['BillFax'])."','".mysqli_real_escape_string($this->connectionlink,$data['Status'])."','".$_SESSION['user']['AccountID']."','".$_SESSION['user']['UserId']."',NOW(),'".mysqli_real_escape_string($this->connectionlink,$data['VendorCategory'])."','".mysqli_real_escape_string($this->connectionlink,$data['PaymentCode'])."','".mysqli_real_escape_string($this->connectionlink,$data['InvoiceCode'])."','".mysqli_real_escape_string($this->connectionlink,$data['PaymentTerms'])."','".mysqli_real_escape_string($this->connectionlink,$data['SAPCustomer'])."','".mysqli_real_escape_string($this->connectionlink,$data['DocCurrency'])."','".mysqli_real_escape_string($this->connectionlink,$data['vendor_currency'])."','".mysqli_real_escape_string($this->connectionlink,$data['TaxCode'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerMPNLock'])."','".mysqli_real_escape_string($this->connectionlink,$data['WasteCertificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerCOOLock'])."')";
		}
		else {
			$duplicate = $this->CheckDuplicate('Edit','vendor','VendorName',$data['VendorName'],true,'VendorID',$data['VendorID']);	
			if($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'Vendor already Exists';
				return json_encode($json);
			}
			$query = "UPDATE `vendor` SET `VendorName`='".mysqli_real_escape_string($this->connectionlink,$data['VendorName'])."',`ShortCode`='".mysqli_real_escape_string($this->connectionlink,$data['ShortCode'])."',`StreetAddress`='".mysqli_real_escape_string($this->connectionlink,$data['StreetAddress'])."',`City`='".mysqli_real_escape_string($this->connectionlink,$data['City'])."',`State`='".mysqli_real_escape_string($this->connectionlink,$data['State'])."',`Country`='".mysqli_real_escape_string($this->connectionlink,$data['Country'])."',`Zip`='".mysqli_real_escape_string($this->connectionlink,$data['Zip'])."',`Phone`='".mysqli_real_escape_string($this->connectionlink,$data['Phone'])."',`Fax`='".mysqli_real_escape_string($this->connectionlink,$data['Fax'])."',`Email`='".mysqli_real_escape_string($this->connectionlink,$data['Email'])."',`Website`='".mysqli_real_escape_string($this->connectionlink,$data['Website'])."',`Title`='".mysqli_real_escape_string($this->connectionlink,$data['Title'])."',`ContactName`='".mysqli_real_escape_string($this->connectionlink,$data['ContactName'])."',`ContactPhone`='".mysqli_real_escape_string($this->connectionlink,$data['ContactPhone'])."',`ContactEmail`='".mysqli_real_escape_string($this->connectionlink,$data['ContactEmail'])."',`SpecialInstructions`='".mysqli_real_escape_string($this->connectionlink,$data['SpecialInstructions'])."',`BillContact`='".mysqli_real_escape_string($this->connectionlink,$data['BillContact'])."',`BillCity`='".mysqli_real_escape_string($this->connectionlink,$data['BillCity'])."',`BillZip`='".mysqli_real_escape_string($this->connectionlink,$data['BillZip'])."',`BillPhone`='".mysqli_real_escape_string($this->connectionlink,$data['BillPhone'])."',`BillEmail`='".mysqli_real_escape_string($this->connectionlink,$data['BillEmail'])."',`BillAddress`='".mysqli_real_escape_string($this->connectionlink,$data['BillAddress'])."',`BillState`='".mysqli_real_escape_string($this->connectionlink,$data['BillState'])."',`BillCountry`='".mysqli_real_escape_string($this->connectionlink,$data['BillCountry'])."',`TaxCode`='".mysqli_real_escape_string($this->connectionlink,$data['TaxCode'])."',`BillFax`='".mysqli_real_escape_string($this->connectionlink,$data['BillFax'])."',`Status`='".mysqli_real_escape_string($this->connectionlink,$data['Status'])."',`UpdatedBy`='".$_SESSION['user']['UserId']."',`DateUpdated`=NOW(),`PaymentTerms`='".mysqli_real_escape_string($this->connectionlink,$data['PaymentTerms'])."',`VendorCategory`='".mysqli_real_escape_string($this->connectionlink,$data['VendorCategory'])."',`PaymentCode`='".mysqli_real_escape_string($this->connectionlink,$data['PaymentCode'])."',`InvoiceCode`='".mysqli_real_escape_string($this->connectionlink,$data['InvoiceCode'])."',`SAPCustomer` = '".mysqli_real_escape_string($this->connectionlink,$data['SAPCustomer'])."',`DocCurrency` = '".mysqli_real_escape_string($this->connectionlink,$data['DocCurrency'])."',`vendor_currency` = '".mysqli_real_escape_string($this->connectionlink,$data['vendor_currency'])."',`ContainerMPNLock` = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerMPNLock'])."',WasteCertificationID = '".mysqli_real_escape_string($this->connectionlink,$data['WasteCertificationID'])."',ContainerCOOLock = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerCOOLock'])."' where `VendorID`='".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."'";
		}
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if($data['VendorID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New Destination Created";
			$json['VendorID'] = $insert_id;
		}else {
			$json['Success'] = true;
			$json['Result'] = "Destination Modified";
		}
		return json_encode($json);
	}

	


	public function GetVendorDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		$query = "select * from vendor where VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."' ORDER BY VendorName";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink))
		{
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Destination";
		}
		return json_encode($json);
	}
	public function Getcountries()
	{
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select CountryID, Country from countries where Status = 1 ORDER BY Country ASC";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink))
		{
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		else
		{
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($query)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			}
			else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}
		}
		return json_encode($json);
	}

	public function Getstates()
	{
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select StateID, State from states where Status = 1";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink))
		{
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		else
		{
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($query)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			}
			else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}
		}
		return json_encode($json);
	}


	public function GetVendorCategory()
	{
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select idvendor_category, VendorCategoryName from vendor_category where vendor_cat_status = 1";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink))
		{
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		else
		{
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($query)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			}
			else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}
		}
		return json_encode($json);
	}

	public function GetPaymentTerms()
	{
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
		$sql = "Select payment_terms_ID, payment_terms from payment_terms where 1";
		$query = mysqli_query($this->connectionlink,$sql);
		if(mysqli_error($this->connectionlink))
		{
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		else
		{
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($query)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			}
			else {
				$json['Success'] = false;
				$json['Result'] = 'No Results';
			}
		}
		return json_encode($json);
	}

	public function AddInventorySerialToContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}


			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container


			//Start check If ContainerMPNLock Satisfies
			$contaner_lock = $this->isContainerMPNLock($data['ShippingID'],$data['UniversalModelNumber'],$data['ShippingContainerID']);
			if($contaner_lock['Success']) {                    				
			} else {
				$json['Success'] = false;
				$json['Result'] = $contaner_lock['Error'];
				return json_encode($json);
			}
			//End check If ContainerMPNLock Satisfied

			//Start get serialnumber details			
			//$query = "select i.*,s.InventoryStatus from inventory i,inventory_status s where i.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and i.InventoryStatusID = s.InventoryStatusID ";
			$query = "select i.*,s.InventoryStatus,co.COO from inventory i 
			left join inventory_status s on i.InventoryStatusID = s.InventoryStatusID 
			left join COO co on i.COOID = co.COOID  
			where i.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				//if($row['InventoryStatusID'] != '1') {
				if($row['InventoryStatusID'] != '1' && $row['InventoryStatusID'] != '3') { //If not active and not inactive
					$json['Success'] = false;
					$json['Result'] = 'Serial Number Status is '.$row['InventoryStatus'];
					return json_encode($json);
				}
				//Start get Shipping Disposition
				$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					// if($row1['FacilityID'] != $row['FacilityID']) {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Facility is Different from Serial Number Facility';
					// 	return json_encode($json);
					// }

					//Start get Part type from MPN and validate MPN
					if($data['UniversalModelNumber'] != '')
					{
						$query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
						$q6 = mysqli_query($this->connectionlink,$query6);	
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row6 = mysqli_fetch_assoc($q6);
						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid MPN';
							return json_encode($json);
						}
					}
					else
					{
						$row6['part_type'] = '';
					}
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update inventory set CustomPalletID = NULL,InventoryStatusID=4,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',ShippingContainerID ='".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."' where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."')";
					//$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,serial_scan_time,mpn_scan_time,COO) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$row['COO'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					//Start check IF MPN Changed
					if($row['UniversalModelNumber'] != $data['UniversalModelNumber']) {//If MPN of Inventory Changed
						$query7 = "update inventory set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."'";
						$q7 = mysqli_query($this->connectionlink,$query7);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);			
						}						
					}
					//End check IF MPN Changed

					//Start insert into tracking
					// $action = 'Added to Shipment Container ('.$data['ShippingContainerID'].') of Shipment ('.$data['ShippingID'].')';
					// $query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					// $q6 = mysqli_query($this->connectionlink,$query6);
					// if(mysqli_error($this->connectionlink)) {
					// 	$json['Success'] = false;
					// 	$json['Result'] = mysqli_error($this->connectionlink);
					// 	return json_encode($json);			
					// }
					//End insert into tracking					
					$json['Success'] = true;
					$json['Result'] = 'Serial Number added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Serial Number';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetShippingRemovalTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Destination')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Destination Page';
				return json_encode($json);
			}
			$query = "select * from disposition where status = 'Active' and shipping_removal_type = '1' order by disposition";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Removal Types Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function AddRemovalTypeToDestination ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Destination')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Destination Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Destination')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Destination Page';
				return json_encode($json);
			}
			//Start check If removal_type already mapped
			$query = "select count(*) from vendor_removal_types where VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."'";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Removal Type already mapped to Destination';
					return json_encode($json);
				}
			}
			//End check If removal_type already mapped

			$query = "insert into vendor_removal_types (disposition_id,VendorID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."',NOW(),'".$_SESSION['user']['UserId']."')";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$json['Success'] = true;		
			$json['Result'] = 'Removal Type Mapped to Destination';
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetMappedRemovalTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Destination')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Destination Page';
				return json_encode($json);
			}
			$query = "select m.*,d.disposition from vendor_removal_types m,disposition d where m.disposition_id = d.disposition_id and m.VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."' order by disposition";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Removal Types Available for Destination";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function DeleteMappedRemovalType ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Destination')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Destination Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Destination')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Destination Page';
				return json_encode($json);
			}

			$query = "delete from vendor_removal_types where ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."'";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$json['Success'] = true;
			$json['Result'] = 'Removal Type removed for Destination';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetDestinationRemovalTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select m.*,d.disposition,d.sub_disposition from vendor_removal_types m,disposition d where m.disposition_id = d.disposition_id and m.VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."' order by disposition";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Removal Types Available for Destination";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function DeleteShipment ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Removal';
				return json_encode($json);
			}

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				if($shipping['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Shipment';
				return json_encode($json);		
			}
			//End get Shiping Disposition
			$query8 = "select count(*) from shipping_containers where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$query8 = "select count(*) from shipping_container_serials se,shipping_containers c where se.ShippingContainerID = c.ShippingContainerID and c.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q8 = mysqli_query($this->connectionlink,$query8);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row8 = mysqli_fetch_assoc($q8);
				if($row8['count(*)'] == 0) {

					$query9 = "delete from shipping_containers where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
					$q9 = mysqli_query($this->connectionlink,$query9);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					$query9 = "delete from shipping where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
					$q9 = mysqli_query($this->connectionlink,$query9);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					//$json['Result'] = $row8['count(*)']." Containers exists for the Shipment, Delete containers to delete Shipment";
					$json['Result'] = $row8['count(*)']." Serials exists for the Shipment";
					return json_encode($json);	
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid';
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Shipment Deleted';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateInventorySerialNumber ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			$query = "select i.*,s.InventoryStatus from inventory i,inventory_status s where i.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and i.InventoryStatusID = s.InventoryStatusID ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['InventoryStatusID'] == '4') {
					$json['Success'] = false;
					$json['Result'] = 'Inventory SN is already on RZRR';
					return json_encode($json);
				}

				if($row['InventoryStatusID'] == '5') {
					$json['Success'] = false;
					$json['Result'] = 'Inventory SN has been shipped';
					return json_encode($json);
				}

				if($row['InventoryStatusID'] != '1' && $row['InventoryStatusID'] != '3') { //If not active and not inactive
					$json['Success'] = false;
					$json['Result'] = 'Serial Number Status is '.$row['InventoryStatus'];
					return json_encode($json);
				}
				//Start get Shipping Disposition
				//$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Inventory Serial Number';
						return json_encode($json);
					}					
					$json['UniversalModelNumber'] = $row['UniversalModelNumber'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Inventory SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateRemovalController ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['UserName'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['RemovalController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Removal Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Valid';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Controller or Password";
				return json_encode($json);
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GenerateShipmentPalletID ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Removal';
				return json_encode($json);
			}

			$PalletID = $this->GetRandomPalletID();			
			$json['PalletID'] = $PalletID;			

			if(count($data['Containers']) > 0) {
				for($i=0;$i<count($data['Containers']);$i++) {
					$data['Containers'][$i]['ContainerID'];
					//Start updating PalletID to Shipment Container
					$query = "update shipping_containers set PalletID = '".mysqli_real_escape_string($this->connectionlink,$PalletID)."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['Containers'][$i]['ContainerID'])."'";
					$q = mysqli_query($this->connectionlink,$query);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End updating PalletID to Shipment Container
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "No containers available";
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Pallet ID ('.$PalletID.') generated';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetRandomPalletID() {
		while(1) {

			$str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
			$randomNumber = substr(str_shuffle($str_result),0, 8);			
			// check if it exists in database
			$query = "SELECT * FROM `shipping_containers` WHERE PalletID = '".mysqli_real_escape_string($this->connectionlink,$randomNumber)."'";
			$res = mysqli_query($this->connectionlink,$query);
			$rowCount = mysqli_num_rows($res);
			/*if(mysqli_affected_rows($this->connectionlink) > 0){	
				$row = mysqli_fetch_assoc($res);
				$rowCount = $row['count(*)'];
			} else {
				$rowCount = 1;
			}*/
			if($rowCount < 1) {
				break;
			}
		}
		return $randomNumber;
	}

	public function GetMatchingLocations($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//$query = "select LoadId from loads where LoadId like '%".$keyword."%'";		
		
		$query = "select * from location l where l.FacilityID='".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' AND l.Locked != 1 AND l.LocationID != 0 and l.LocationStatus = '1' and l.LocationType = 'Outbound Storage' and l.LocationName like '%".mysqli_real_escape_string($this->connectionlink,$data['keyword'])."%' limit 10 ";
		$q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while($row = mysqli_fetch_assoc($q)) {
				if($row['LocationStatus'] == 1)
				{
					$row['LocationStatus'] = 'Active';
				}
				else if($row['LocationStatus'] == 2)
				{
					$row['LocationStatus'] = 'InActive';
				}
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
		}
		return json_encode($json);
	}


	public function RemovePalletFromContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Removal';
				return json_encode($json);
			}


			$query = "update shipping_containers set PalletID = NULL where  ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Container removed from Pallet';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetDispositionVendors ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			//$query = "select m.*,d.disposition,d.sub_disposition from vendor_removal_types m,disposition d where m.disposition_id = d.disposition_id and m.VendorID = '".mysqli_real_escape_string($this->connectionlink,$data['VendorID'])."' order by disposition";
			$query = "select t.*,v.* from vendor_removal_types t,vendor v where t.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' and t.VendorID = v.VendorID and v.Status = '1' order by v.VendorName";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Destinations mapped to Removal Type";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function CreateContainerWithoutShippingID ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			if($data['CreatedBy'] > 0) { //Update existing Container
				//Start get Container Details
				$query = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					//if($row['StatusID'] != '1' && $row['StatusID'] != '7') {
					//if($row['StatusID'] != '1') {
					if($row['StatusID'] != '1' && $row['StatusID'] != '7' && $row['StatusID'] != '6') {
						$json['Success'] = false;			
						$json['Result'] = 'Container Status is not Active';
						return json_encode($json);
					}
				}

				if($row['SealID'] != $data['SealID']) {
					$seal_changed = '1';
				} else {
					$seal_changed = '0';
				}
				///End get Container Details


				//Start check for min weight
				$query4 = "select packageWeight,OptionalLocation from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					// if($row4['packageWeight'] >= $data['ContainerWeight']) {
					// 	$json['Success'] = false;			
					// 	$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
					// 	return json_encode($json);
					// }
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight



				if($data['group'] != $data['GroupName']) { // Location Changed
					if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

						//Start check if valid Group
						$query10 = "select GroupID,FacilityID,LocationType,BinTypeID from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
						$q10 = mysqli_query($this->connectionlink,$query10);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row10 = mysqli_fetch_assoc($q10);						
							if($row10['FacilityID'] != $data['FacilityID']) {
								$json['Success'] = false;
								$json['Result'] = 'Location Facility is different from Shipment Facility';
								return json_encode($json);
							}
							if($row10['LocationType'] != 'Outbound Storage') {
								$json['Success'] = false;
								$json['Result'] = 'Location is not Outbound Storage Location';
								return json_encode($json);
							}				
							$GroupID = $row10['GroupID'];

							//Start get free location from group selected
							$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."'";
							$q112 = mysqli_query($this->connectionlink,$query112);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row112 = mysqli_fetch_assoc($q112);
								$data['NewLocationID'] = $row112['LocationID'];								
								$newLocationName = $row112['LocationName'];
								$data['LocationID'] = $row112['LocationID'];
						 		$location_changed = true;
							} else {
								$json['Success'] = false;
								$json['Result'] = 'No locations available, in selected group';
								return json_encode($json);
							}
							//End get free location from group selected	

						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid Location Group';
							return json_encode($json);
						}
						//End check if valid Group								
					} else {
						$data['LocationID'] = '';
						if($row4['OptionalLocation'] == 0) {
							$json['Success'] = false;
							$json['Result'] = 'Invalid Location';
							return json_encode($json);
						}
						if($row['LocationID'] > 0) {
							$location_changed = true;
						} else {
							$location_changed = false;
						}
					}
				} else {
					$location_changed = false;
				}


				// if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
				// 	$query20 = "select * from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
				// 	$q20 = mysqli_query($this->connectionlink,$query20);
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row20 = mysqli_fetch_assoc($q20);
				// 		if($row20['LocationID'] != $row['LocationID']) {//Location Changed

				// 			if($row20['FacilityID'] != $data['FacilityID']) {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location Facility is different from Container Facility';
				// 				return json_encode($json);
				// 			}
	
				// 			if($row20['Locked'] == '1') {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location is Locked';
				// 				return json_encode($json);
				// 			}
	
				// 			if($row20['LocationType'] != 'Outbound Storage') {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location is not Outbound Storage Location';
				// 				return json_encode($json);
				// 			}
	
				// 			if($row20['LocationStatus'] != '1') {
				// 				$json['Success'] = false;
				// 				$json['Result'] = 'Location is not Active';
				// 				return json_encode($json);
				// 			}						
				// 			$data['LocationID'] = $row20['LocationID'];
				// 			$location_changed = true;
				// 		} else {
				// 			$location_changed = false;
				// 		}											
				// 	} else {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// }  else {
				// 	$data['LocationID'] = '';
				// 	if($row4['OptionalLocation'] == 0) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// 	if($row['LocationID'] > 0) {
				// 		$location_changed = true;
				// 	} else {
				// 		$location_changed = false;
				// 	}
				// }

				//$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."'";
				//$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' ";
				$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',ReferenceIDRequired = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceIDRequired'])."',ReferenceID = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceID'])."',ReferenceTypeID = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."',ReferenceType = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."' ";
				if($data['LocationID'] > 0) {
					$query1 = $query1 .",LocationID = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."'";
				} else {
					$query1 = $query1 .",LocationID = NULL";
				}
				$query1 = $query1 . ",StatusID = 1";
				if($seal_changed == 1) {
					//$query1 = $query1 . ",RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."' ";
				}
				$query1 = $query1 ." where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				//$query1 = "update shipping_containers set idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."',CustomID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."',ContainerNotes = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."',StatusID = 1 where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);				
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Success'] = true;
				$json['Result'] = 'Container Modified';

				//Start check If Container is closed
				if($data['CloseContainer'] == true) {
					$query2 = "update shipping_containers set StatusID = '6' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$json['Result'] = 'Container Closed';
				}
				//End check If Container is closed

				//Start Lock/Unlock Locations				
				if($location_changed == true) {
					if($row['LocationID'] > 0) {
						$query6 = "update location set Locked='2',currentItemType='',currentItemID='' where LocationID = '".mysqli_real_escape_string($this->connectionlink,$row['LocationID'])."'";
						$q6 = mysqli_query($this->connectionlink,$query6);						
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;			
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						$json['Query'] = $query6;
					}	
					
					if($data['LocationID'] > 0) {					
						$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Shipment Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['LocationID'])."'";
						$queryloc = mysqli_query($this->connectionlink,$sqlloc);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							//return json_encode($json);	
						}
					}											
				}
				$json['Changed'] = $location_changed;				
				//End Lock/Unlock Locations

				return json_encode($json);
			} else { //Create new Container
				//Start check If ShippingID exists
				$query1 = "select count(*) from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['count(*)'] > 0) {
						$json['Success'] = false;			
						$json['Result'] = 'Container ID already exists';			
						return json_encode($json);
					}
				}
				//ENd check If ShippingId exists

				//Start check for min weight
				$query4 = "select packageWeight,OptionalLocation from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					// if($row4['packageWeight'] >= $data['ContainerWeight']) {
					// 	$json['Success'] = false;			
					// 	$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
					// 	return json_encode($json);
					// }
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight

				//Start check for valid location

				// if($data['location'] != '' && $data['location'] != null && $data['location'] != 'undefined') {
				// 	$query20 = "select * from location where LocationName = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";							
				// 	$q20 = mysqli_query($this->connectionlink,$query20);
				// 	if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 		$row20 = mysqli_fetch_assoc($q20);

				// 		if($row20['FacilityID'] != $data['FacilityID']) {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location Facility is different from Shipment Facility';
				// 			return json_encode($json);
				// 		}

				// 		if($row20['Locked'] == '1') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is Locked';
				// 			return json_encode($json);
				// 		}

				// 		if($row20['LocationType'] != 'Outbound Storage') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is not Outbound Storage Location';
				// 			return json_encode($json);
				// 		}

				// 		if($row20['LocationStatus'] != '1') {
				// 			$json['Success'] = false;
				// 			$json['Result'] = 'Location is not Active';
				// 			return json_encode($json);
				// 		}						
				// 		$data['location'] = $row20['LocationID'];						
				// 	} else {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// } else {
				// 	if($row4['OptionalLocation'] == 0) {
				// 		$json['Success'] = false;
				// 		$json['Result'] = 'Invalid Location';
				// 		return json_encode($json);
				// 	}
				// }

				//End check for valid location




				if($data['group'] != '' && $data['group'] != null && $data['group'] != 'undefined') {

					//Start check if valid Group
					$query10 = "select GroupID,FacilityID,LocationType,BinTypeID from location_group where GroupName = '".mysqli_real_escape_string($this->connectionlink,$data['group'])."'";
					$q10 = mysqli_query($this->connectionlink,$query10);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row10 = mysqli_fetch_assoc($q10);						
						if($row10['FacilityID'] != $data['FacilityID']) {
							$json['Success'] = false;
							$json['Result'] = 'Location Facility is different from Container Facility';
							return json_encode($json);
						}
						if($row10['LocationType'] != 'Outbound Storage') {
							$json['Success'] = false;
							$json['Result'] = 'Location is not Outbound Storage Location';
							return json_encode($json);
						}				
						$GroupID = $row10['GroupID'];

						//Start get free location from group selected
						$query112 = "select LocationID,LocationType,LocationName from location where Locked = '2' and LocationStatus = '1' and GroupID = '".mysqli_real_escape_string($this->connectionlink,$GroupID)."'";
						$q112 = mysqli_query($this->connectionlink,$query112);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row112 = mysqli_fetch_assoc($q112);
							$data['NewLocationID'] = $row112['LocationID'];
							$data['location'] = $row112['LocationID'];
							$newLocationName = $row112['LocationName'];
						} else {
							$json['Success'] = false;
							$json['Result'] = 'No locations available, in selected group';
							return json_encode($json);
						}
						//End get free location from group selected	

					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
					//End check if valid Group								
				} else {
					if($row4['OptionalLocation'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'Invalid Location Group';
						return json_encode($json);
					}
				}


				//Start check for pallet 
				//$query6 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$query6 = "select count(*) from pallets where idPallet like '%".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."%'";
				$q6 = mysqli_query($this->connectionlink,$query6);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row6 = mysqli_fetch_assoc($q6);
					if($row6['count(*)'] > 0) {
						$pallet_exists = true;
					} else {
						$pallet_exists = false;
					}
				} else {
					$pallet_exists = false;
				}
				if($pallet_exists == true) {
					//start get servers count 
					//$query7 = "select count(*) from speed_server_recovery where idPallet= '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and Type = 'Server'";
					$query7 = "select count(*) from speed_server_recovery where idPallet like '%".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."%' and Type = 'Server'";
					$q7 = mysqli_query($this->connectionlink,$query7);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row7 = mysqli_fetch_assoc($q7);
						$expected_servers = $row7['count(*)'];
					} else {
						$expected_servers = 0;
					}
					//End get servers count
				} else {
					$expected_servers = 0;
				}
				//End check for pallet

				//$query = "insert into shipping_containers (ShippingContainerID,idPackage,StatusID,CreatedDate,CreatedBy,CustomID,ContainerNotes,SealID,ContainerWeight,ShippingControllerLoginID,FacilityID,";
				//$query = "insert into shipping_containers (ShippingContainerID,idPackage,StatusID,CreatedDate,CreatedBy,CustomID,ContainerNotes,ContainerWeight,ShippingControllerLoginID,FacilityID,";
				$query = "insert into shipping_containers (ShippingContainerID,idPackage,StatusID,CreatedDate,CreatedBy,CustomID,ContainerNotes,ShippingControllerLoginID,FacilityID,ReferenceIDRequired,ReferenceID,ReferenceTypeID,ReferenceType,";
				if($data['location'] > 0) {
					$query = $query . "LocationID,";
				}
				if(true) {
					$query = $query . "ExpectedServersCount,ScannedServersCount,";
				}
				//$query = $query . "disposition_id,RecentSealDate,RecentSealBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['SealID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',";
				//$query = $query . "disposition_id) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."',";
				$query = $query . "disposition_id) values ('".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPackage'])."','1',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['CustomID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ContainerNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['ShippingControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceIDRequired'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."',";
				if($data['location'] > 0) {
					$query = $query . "'".mysqli_real_escape_string($this->connectionlink,$data['location'])."',";
				}
				if(true) {
					$query = $query . "'".mysqli_real_escape_string($this->connectionlink,$expected_servers)."','0',";
				}
				$query = $query . "'".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."')";
				$q = mysqli_query($this->connectionlink,$query);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink).$query;			
					return json_encode($json);			
				}

				//Start Lock Location
				if($data['location'] > 0) {
					$sqlloc = "UPDATE `location` SET `Locked` = '1',`currentItemType` = 'Shipment Container',`currentItemID` = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' WHERE `LocationID` = '".mysqli_real_escape_string($this->connectionlink,$data['location'])."'";
					$queryloc = mysqli_query($this->connectionlink,$sqlloc);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						//return json_encode($json);	
					}
				}				
				//End Lock Location

				//Start get Container details
				// $query2 = "select d.*,p.packageName,s.Status,l.LocationName from shipping_containers d 
				// left join package p on d.idPackage = p.idPackage 
				// left join location l on d.LocationID = l.LocationID 
				// left join shipping_status s on d.StatusID = s.ShipmentStatusID where d.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				// $q2 = mysqli_query($this->connectionlink,$query2);		
				// if(mysqli_error($this->connectionlink)) {			
				// 	$json['Success'] = false;			
				// 	$json['Result'] = mysqli_error($this->connectionlink);			
				// 	return json_encode($json);			
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$row2 = mysqli_fetch_assoc($q2);
				// 	$json['Container'] = $row2;
				// }
				//End get Container details

				$json['Success'] = true;
				$json['Result'] = 'Container Created';
				$json['ShippingContainerID'] = $data['ShippingContainerID'];
				return json_encode($json);
			}
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetShippingContainerDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			if($data['ShippingContainerID']) {
				$query = "select c.*,l.LocationName as location from shipping_containers c 
				left join location l on c.LocationID = l.LocationID
				where c.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q = mysqli_query($this->connectionlink,$query);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['ShippingID'] != '') {
						$json['Success'] = false;			
						$json['Result'] = 'Container added to Shipment';			
						return json_encode($json);			
					}
					if($row['StatusID'] != '1') {
						$json['Success'] = false;			
						$json['Result'] = 'Container status is not active';			
						return json_encode($json);			
					}

					if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
						$json['Success'] = false;			
						$json['Result'] = 'Container Facility is different from User Facility';			
						return json_encode($json);			
					}
					$row['ContainerWeight'] = floatval($row['ContainerWeight']);
					$json['Success'] = true;
					$json['Result'] = $row;
					return json_encode($json);	

				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Container ID';
					return json_encode($json);	
				}

			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container ID';
				return json_encode($json);
			}

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateSerialNumberForShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			//$query = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and StatusID = 1";
			$query = "select a.*,co.COO from asset a 
			left join COO co on a.COOID = co.COOID 
			where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by a.StatusID";

			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] == '8') {
					$json['Success'] = false;		
					$json['Result'] = 'SN is already on RZRR';			
					return json_encode($json);
				}

				if($row['StatusID'] == '6') {
					$json['Success'] = false;		
					$json['Result'] = 'SN has been shipped';			
					return json_encode($json);
				}

				if($row['StatusID'] != '1' && $row['StatusID'] != '9') {
					$json['Success'] = false;		
					$json['Result'] = 'Serial Number Status is not Active';			
					return json_encode($json);
				}
				//Start get container Disposition				
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					//Start get Sanitization Verification ID If available
					$query22 = "select sanitization_verification_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc";
					$q22 = mysqli_query($this->connectionlink,$query22);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row22 = mysqli_fetch_assoc($q22);
						$json['sanitization_verification_id'] = $row22['sanitization_verification_id'];
						//$json['sanitization_verification_id'] = $query22;
					}
					//end get Sanitization Verificatio ID If available

					$json['AssetScanID'] = $row['AssetScanID'];
					$json['UniversalModelNumber'] = $row['UniversalModelNumber'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment Container';
					return json_encode($json);		
				}
				//End get container Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddSerialToShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Container';
				return json_encode($json);
			}


			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container

			if(!$data['COOID']) {
				$json['Success'] = false;
				$json['Result'] = 'COO is missing';
				return json_encode($json);
			} else {
				$query234 = "select COO from COO where COOID = '".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."'";
				$q234 = mysqli_query($this->connectionlink,$query234);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row234 = mysqli_fetch_assoc($q234);
					$data['COO'] = $row234['COO'];
				}
			}

			//Start get serialnumber details			
			$query = "select a.*,co.COO from asset a 
			left join COO co on a.COOID = co.COOID  
			where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and a.StatusID in ('1','9' )";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				// if($row['StatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Serial Number Status is not active';			
				// 	return json_encode($json);
				// }

				//Start check for Seal Matching
				$query22 = "select sanitization_verification_id,sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc";
				$q22 = mysqli_query($this->connectionlink,$query22);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row22 = mysqli_fetch_assoc($q22);
					if($row22['sanitization_seal_id'] != $data['sanitization_seal_id'])	{
						$json['Success'] = false;
						$json['Result'] = 'Seal ID not matching';
						return json_encode($json);	
					} else {						
					}
					if($row22['sanitization_verification_id'] != $data['SanitizationVerificationID'])	{
						$json['Success'] = false;
						$json['Result'] = 'Sanitization Verification ID not matching';
						return json_encode($json);	
					} else {						
					}
				} else {
					// $json['Success'] = false;
					// $json['Result'] = 'Serial not involved in Sanitization';
					// return json_encode($json);
				}
				//End check for Seal Matching

				//Start get Shipping Disposition
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					if($row1['FacilityID'] != $row['FacilityID']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Facility is Different from Serial Number Facility';
						return json_encode($json);
					}

					//Start get Part type from MPN and validate MPN
					if($data['UniversalModelNumber'] != '')
					{
						$query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
						$q6 = mysqli_query($this->connectionlink,$query6);	
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row6 = mysqli_fetch_assoc($q6);
						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid MPN';
							return json_encode($json);
						}
					}
					else
					{
						$row6['part_type'] = '';
					}
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update asset set CustomPalletID = NULL,StatusID=8,DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."')";
					//$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,serial_scan_time,mpn_scan_time,COO,COOID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['COO'])."','".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					//Start insert into tracking
					$action = 'Added to Shipment Container ('.$data['ShippingContainerID'].') ';
					$query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into tracking	
					
					//Start check IF MPN Changed
					if($row['UniversalModelNumber'] != $data['UniversalModelNumber']) {//If MPN of Asset Changed
						$query7 = "update asset set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
						$q7 = mysqli_query($this->connectionlink,$query7);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);			
						}

						$desc = "Asset MPN Changed from '".$row['UniversalModelNumber']."' to '".$data['UniversalModelNumber']."' in Shipment Container Screen";
						$query8 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
						$q8 = mysqli_query($this->connectionlink,$query8);
					}
					//End check IF MPN Changed
					
					$json['Success'] = true;
					$json['Result'] = 'Serial Number added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function ValidateInventorySerialNumberForShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			$query = "select i.*,s.InventoryStatus from inventory i,inventory_status s where i.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and i.InventoryStatusID = s.InventoryStatusID ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['InventoryStatusID'] == '4') {
					$json['Success'] = false;
					$json['Result'] = 'Inventory SN is already on RZRR';
					return json_encode($json);
				}

				if($row['InventoryStatusID'] == '5') {
					$json['Success'] = false;
					$json['Result'] = 'Inventory SN has been shipped';
					return json_encode($json);
				}

				if($row['InventoryStatusID'] != '1' && $row['InventoryStatusID'] != '3') { //If not active and not inactive
					$json['Success'] = false;
					$json['Result'] = 'Serial Number Status is '.$row['InventoryStatus'];
					return json_encode($json);
				}
				//Start get Shipping Disposition
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Inventory Serial Number';
						return json_encode($json);
					}					
					$json['UniversalModelNumber'] = $row['UniversalModelNumber'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Container';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Inventory SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddInventorySerialToShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Container Page';
				return json_encode($json);
			}

			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container

			//Start get serialnumber details			
			$query = "select i.*,s.InventoryStatus from inventory i,inventory_status s where i.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and i.InventoryStatusID = s.InventoryStatusID ";
			$query = "select i.*,s.InventoryStatus,co.COO from inventory i 
			left join inventory_status s on i.InventoryStatusID = s.InventoryStatusID 
			left join COO co on i.COOID = co.COOID  
			where i.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				//if($row['InventoryStatusID'] != '1') {
				if($row['InventoryStatusID'] != '1' && $row['InventoryStatusID'] != '3') { //If not active and not inactive
					$json['Success'] = false;
					$json['Result'] = 'Serial Number Status is '.$row['InventoryStatus'];
					return json_encode($json);
				}
				//Start get Shipping Disposition
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					// if($row1['FacilityID'] != $row['FacilityID']) {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Container Facility is Different from Serial Number Facility';
					// 	return json_encode($json);
					// }

					//Start get Part type from MPN and validate MPN
					if($data['UniversalModelNumber'] != '')
					{
						$query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
						$q6 = mysqli_query($this->connectionlink,$query6);	
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row6 = mysqli_fetch_assoc($q6);
						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid MPN';
							return json_encode($json);
						}
					}
					else
					{
						$row6['part_type'] = '';
					}
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update inventory set CustomPalletID = NULL,InventoryStatusID=4,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."' where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."')";
					//$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,serial_scan_time,mpn_scan_time,COO) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row6['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$row['COO'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					//Start check IF MPN Changed
					if($row['UniversalModelNumber'] != $data['UniversalModelNumber']) {//If MPN of Inventory Changed
						$query7 = "update inventory set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."'";
						$q7 = mysqli_query($this->connectionlink,$query7);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);			
						}						
					}
					//End check IF MPN Changed

					//Start insert into tracking
					// $action = 'Added to Shipment Container ('.$data['ShippingContainerID'].') of Shipment ('.$data['ShippingID'].')';
					// $query6 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					// $q6 = mysqli_query($this->connectionlink,$query6);
					// if(mysqli_error($this->connectionlink)) {
					// 	$json['Success'] = false;
					// 	$json['Result'] = mysqli_error($this->connectionlink);
					// 	return json_encode($json);			
					// }
					//End insert into tracking					
					$json['Success'] = true;
					$json['Result'] = 'Serial Number added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Serial Number';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateServerSerialNumberForShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			$query = "select * from speed_server_recovery where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] == '8') {
					$json['Success'] = false;		
					$json['Result'] = 'SN is already on RZRR';			
					return json_encode($json);
				}

				if($row['StatusID'] == '6') {
					$json['Success'] = false;		
					$json['Result'] = 'SN has been shipped';			
					return json_encode($json);
				}

				// if($row['StatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Serial Number Status is not active';			
				// 	return json_encode($json);
				// }
				//Start get container Disposition				
				$query1 = "select s.*,d.disposition from shipping_containers s 
				left join disposition d on s.disposition_id = d.disposition_id 
				where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}

					if($data['ByPassContainerMatching'] != '1') {
						//if($row1['disposition'] == 'Terminal-ServerRecycleProcessed'  && ($row['idPallet'] != $data['ShippingContainerID']) && $row['Type'] == 'Server') {
						if ($row1['disposition'] == 'Terminal-ServerRecycleProcessed' && strpos($row['idPallet'], $data['ShippingContainerID']) === false && ($row['Type'] == 'Server' || $row['Type'] == 'SERVER' || $row['Type'] == 'server')) {
							$json['Success'] = false;		
							$json['Result'] = 'Container ID and Rack ID of Serial should be same ';		
							return json_encode($json);
						}
					}
					
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Serial Number';
						return json_encode($json);
					}
					$json['ServerID'] = $row['ServerID'];
					$json['MPN'] = $row['MPN'];
					$json['Type'] = $row['Type'];
					$json['MediaRecovery_VerificationID'] = $row['MediaRecovery_VerificationID'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment Container';
					return json_encode($json);		
				}
				//End get container Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function AddServerSerialToShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Container';
				return json_encode($json);
			}

			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container

			//Start get serialnumber details			
			$query = "select a.*,co.COO from speed_server_recovery a 
			left join COO co on a.COOID = co.COOID 
			where a.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] == '8') {
					$json['Success'] = false;		
					$json['Result'] = 'SN is already on RZRR';			
					return json_encode($json);
				}

				if($row['StatusID'] == '6') {
					$json['Success'] = false;		
					$json['Result'] = 'SN has been shipped';			
					return json_encode($json);
				}
				//Start get Shipping Disposition
				$query1 = "select s.*,d.disposition from shipping_containers s 
				left join disposition d on s.disposition_id = d.disposition_id 
				where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}

					if($data['ByPassContainerMatching'] != '1') {
						//if($row1['disposition'] == 'Terminal-ServerRecycleProcessed'  && ($row['idPallet'] != $data['ShippingContainerID']) && $row['Type'] == 'Server') {
						if ($row1['disposition'] == 'Terminal-ServerRecycleProcessed' && strpos($row['idPallet'], $data['ShippingContainerID']) === false && ($row['Type'] == 'Server' || $row['Type'] == 'SERVER' || $row['Type'] == 'server')) {
							$json['Success'] = false;
							$json['Result'] = 'Container ID and Rack ID of Serial should be same';
							return json_encode($json);
						}
					}
					
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					// if($row1['FacilityID'] != $row['FacilityID']) {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Container Facility is Different from Serial Number Facility';
					// 	return json_encode($json);
					// }

					//Start get Part type from MPN and validate MPN
					if($data['Type'] == 'Switch') {
						if($data['UniversalModelNumber'] != '')
						{
							$query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
							$q6 = mysqli_query($this->connectionlink,$query6);	
							if(mysqli_error($this->connectionlink)) {			
								$json['Success'] = false;		
								$json['Result'] = mysqli_error($this->connectionlink);			
								return json_encode($json);			
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row6 = mysqli_fetch_assoc($q6);
							} else {
								$json['Success'] = false;
								$json['Result'] = 'Invalid MPN';
								return json_encode($json);
							}
						}
						else
						{
							$row6['part_type'] = '';
						}
						$data['ServerSanitizationVerificationID'] = '';
					} else {
						$data['UniversalModelNumber'] = '';
					}			
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update speed_server_recovery set CustomPalletID = NULL,StatusID=8,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."')";
					//$query5 = "insert into shipping_container_serials (ServerSerialNumber,ServerID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,ControllerLoginID,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSanitizationVerificationID'])."')";
					$query5 = "insert into shipping_container_serials (ServerSerialNumber,ServerID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,serial_scan_time,mpn_scan_time,COO) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$row['COO'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					//Start insert into tracking
					$action = 'Added to Shipment Container ('.$data['ShippingContainerID'].') ';
					$query6 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into tracking	
					
					//Start check IF MPN Changed
					if($data['Type'] == 'Switch') {

						if($row['MPN'] != $data['UniversalModelNumber']) {//If MPN of Asset Changed
							$query7 = "update speed_server_recovery set MPN = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."'";
							$q7 = mysqli_query($this->connectionlink,$query7);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);			
							}
	
							$desc = "Switch MPN Changed from '".$row['MPN']."' to '".$data['UniversalModelNumber']."' in Add Container Screen";
							$query8 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
							$q8 = mysqli_query($this->connectionlink,$query8);
						}
					}					
					//End check IF MPN Changed
					
					$json['Success'] = true;
					$json['Result'] = $data['Type'].' added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetByProducts ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			$query = "select * from by_products where StatusID = '1' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' order by part_type";			
			$query = "select b.*,p.parttype from by_products b left join parttype p on b.part_type = p.parttypeid where b.StatusID = '1' and b.FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and b.disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."' order by p.parttype ASC";
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No ByProducts available for Facility";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddByProductToContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Container';
				return json_encode($json);
			}

			//Start get serialnumber details			
			//$query = "select a.* from by_products a where a.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."'";
			$query = "select a.*,p.parttype from by_products a left join parttype p on a.part_type = p.parttypeid where a.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."'";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Byproduct Status is not active';			
					return json_encode($json);
				}
				//Start get Shipping Disposition
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Byproduct Removal Type';
						return json_encode($json);
					}

					if($row1['FacilityID'] != $row['FacilityID']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Facility is Different from Byproduct Facility';
						return json_encode($json);
					}

					//Start check If byproduct_id already added for shipping container
					//$query55 = "select count(*) from shipping_container_serials where byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."' and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
					//$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
					$query55 = "select count(*) from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
					$q55 = mysqli_query($this->connectionlink,$query55);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row55 = mysqli_fetch_assoc($q55);
						if($row55['count(*)'] > 0) {
							$json['Success'] = false;
							$json['Result'] = 'Some material is already added to Container,for adding Byproduct Container should be empty';
							return json_encode($json);
						}
					}
					//End check If byproduct_id already added for shipping container

					//Start insert into Shipment Container					
					//$query5 = "insert into shipping_container_serials (byproduct_id,StatusID,ShippingContainerID,CreatedDate,CreatedBy,part_type,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['byproduct_id'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					$query5 = "insert into shipping_container_serials (byproduct_id,StatusID,ShippingContainerID,CreatedDate,CreatedBy,part_type) values ('".mysqli_real_escape_string($this->connectionlink,$row['byproduct_id'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['parttype'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					$json['Success'] = true;
					$json['Result'] = 'Byproduct added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Byproduct is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddContainerToShipment ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Prep';
				return json_encode($json);
			}

			//start validating container
			$query = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['ShippingID'] != '') {
					$json['Success'] = false;		
					$json['Result'] = 'Container already added to Shipment '.$row['ShippingID'];			
					return json_encode($json);			
				}
				// if($row['StatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Container status is not Active';			
				// 	return json_encode($json);			
				// }
			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Container ID not in eViridis';			
				return json_encode($json);			
			}
			//end validating container

			//Start validate shipping
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}
				if($row['ASNContainer'] == 1) {//if asn container,only add to destination facility shipment
					if($row1['DestinationFacilityID'] == '' || $row1['DestinationFacilityID'] == '0'){
						$json['Success'] = false;		
						$json['Result'] = 'Container is only allowed to add in Inter Facility Shipment';			
						return json_encode($json);
					}
				}

				if($row['ASNContainer'] == 0) {//regular shipment container
					// if($row1['DestinationFacilityID'] > 0){
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Container is only allowed to add in Destination Shipment,Current shipment is inter facility shipment';			
					// 	return json_encode($json);
					// }
				}
				// if($row1['disposition_id'] != $row['disposition_id']) {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Shipment Removal Type is not matching with Container Removal Type';
				// 	return json_encode($json);
				// }

				if($row1['FacilityID'] != $row['FacilityID']) {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Facility is Different from Container Facility';
					return json_encode($json);
				}

				//Start check if shipment vendor accepts continer removal type
				if($row1['VendorID'] > 0) {
					$query13 = "select count(*) from vendor_removal_types m where m.VendorID = '".mysqli_real_escape_string($this->connectionlink,$row1['VendorID'])."' and disposition_id = '".mysqli_real_escape_string($this->connectionlink,$row['disposition_id'])."'";
					$q13 = mysqli_query($this->connectionlink,$query13);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row13 = mysqli_fetch_assoc($q13);
						if($row13['count(*)'] == '0') {
							$json['Success'] = false;
							$json['Result'] = 'Container Removal Type is not mapped to Shipment Destination';
							return json_encode($json);	
						}
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Invalid';
						return json_encode($json);
					}
				}
				//End check if shipment vendor accepts container removal type

				$query2 = "update shipping_containers set ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',container_added_to_shipment_time = NOW() where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q2 = mysqli_query($this->connectionlink,$query2);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				//Start get container details

				$query3 = "select d.*,p.packageName,s.Status,l.LocationName,dd.disposition from shipping_containers d 
				left join package p on d.idPackage = p.idPackage 
				left join location l on d.LocationID = l.LocationID 
				left join disposition dd on d.disposition_id = dd.disposition_id  
				left join shipping_status s on d.StatusID = s.ShipmentStatusID where d.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row3 = mysqli_fetch_assoc($q3);
					$json['Container'] = $row3;
				}

				//End get container details

				$json['Success'] = true;	
				$json['Result'] = 'Container added to Shipment';
				return json_encode($json);

			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Invalid Ticket ID';
				return json_encode($json);
			}

			//End validate shipping

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddByProductToShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Prep';
				return json_encode($json);
			}

			//Start get serialnumber details			
			$query = "select a.* from by_products a where a.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."'";
			$query = "select a.*,p.parttype from by_products a left join parttype p on a.part_type = p.parttypeid where a.byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."'";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Byproduct Status is not active';			
					return json_encode($json);
				}
				//Start get Shipping Disposition
				//$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Removal Type is not matching with Byproduct Removal Type';
						return json_encode($json);
					}

					if($row1['FacilityID'] != $row['FacilityID']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Facility is Different from Byproduct Facility';
						return json_encode($json);
					}

					//Start check If byproduct_id already added for shipping container
					//$query55 = "select count(*) from shipping_container_serials where byproduct_id = '".mysqli_real_escape_string($this->connectionlink,$data['byproduct_id'])."' and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
					//$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
					$query55 = "select count(*) from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
					$q55 = mysqli_query($this->connectionlink,$query55);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row55 = mysqli_fetch_assoc($q55);
						if($row55['count(*)'] > 0) {
							$json['Success'] = false;
							$json['Result'] = 'Some material is already added to Container,for adding Byproduct Container should be empty';
							return json_encode($json);
						}
					}
					//End check If byproduct_id already added for shipping container

					//Start insert into Shipment Container					
					//$query5 = "insert into shipping_container_serials (byproduct_id,StatusID,ShippingContainerID,CreatedDate,CreatedBy,part_type,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['byproduct_id'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					//$query5 = "insert into shipping_container_serials (byproduct_id,StatusID,ShippingContainerID,CreatedDate,CreatedBy,part_type) values ('".mysqli_real_escape_string($this->connectionlink,$row['byproduct_id'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."')";
					$query5 = "insert into shipping_container_serials (byproduct_id,StatusID,ShippingContainerID,CreatedDate,CreatedBy,part_type) values ('".mysqli_real_escape_string($this->connectionlink,$row['byproduct_id'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['parttype'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					$json['Success'] = true;
					$json['Result'] = 'Byproduct added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Byproduct is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function AddServerSerialToContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Prep';
				return json_encode($json);
			}


			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container

			//Start get serialnumber details			
			$query = "select a.*,d.disposition,co.COO from speed_server_recovery a 
			left join disposition d on a.disposition_id = d.disposition_id 
			left join COO co on a.COOID = co.COOID 
			where a.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] == '8') {
					$json['Success'] = false;		
					$json['Result'] = 'SN is already on RZRR';			
					return json_encode($json);
				}

				if($row['StatusID'] == '6') {
					$json['Success'] = false;		
					$json['Result'] = 'SN has been shipped';			
					return json_encode($json);
				}								

				// if($row['StatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Serial Number Status is not active';			
				// 	return json_encode($json);
				// }
				//Start get Shipping Disposition
				$query1 = "select s.*,d.disposition from shipping s 
				left join disposition d on s.disposition_id = d.disposition_id 
				 where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

				$query1 = "select s.*,d.disposition from shipping_containers s 
				 left join disposition d on s.disposition_id = d.disposition_id 
				 where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}

					if($data['ByPassContainerMatching'] != '1') {
						//if($row1['disposition'] == 'Terminal-ServerRecycleProcessed'  && ($row['idPallet'] != $data['ShippingContainerID']) && $row['Type'] == 'Server') {
						if ($row1['disposition'] == 'Terminal-ServerRecycleProcessed' && strpos($row['idPallet'], $data['ShippingContainerID']) === false && ($row['Type'] == 'Server' || $row['Type'] == 'SERVER' || $row['Type'] == 'server')) {
							$json['Success'] = false;
							$json['Result'] = 'Container ID and Rack ID of Serial should be same';
							return json_encode($json);
						}
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Removal Type is not matching with Serial Number';
						return json_encode($json);
					}

					// if($row1['FacilityID'] != $row['FacilityID']) {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Container Facility is Different from Serial Number Facility';
					// 	return json_encode($json);
					// }

					//Start get Part type from MPN and validate MPN
					if($data['Type'] == 'Switch') {
						if($data['UniversalModelNumber'] != '')
						{
							$query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
							$q6 = mysqli_query($this->connectionlink,$query6);	
							if(mysqli_error($this->connectionlink)) {			
								$json['Success'] = false;		
								$json['Result'] = mysqli_error($this->connectionlink);			
								return json_encode($json);			
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row6 = mysqli_fetch_assoc($q6);
							} else {
								$json['Success'] = false;
								$json['Result'] = 'Invalid MPN';
								return json_encode($json);
							}
						}
						else
						{
							$row6['part_type'] = '';
						}
						$data['ServerSanitizationVerificationID'] = '';
					} else {
						$data['UniversalModelNumber'] = '';
					}			
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update speed_server_recovery set CustomPalletID = NULL,StatusID=8,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',ShippingContainerAddedDate = NOW(),ShippingContainerAddedBy = '".$_SESSION['user']['UserId']."',ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (SerialNumber,AssetScanID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['SanitizationVerificationID'])."')";
					//$query5 = "insert into shipping_container_serials (ServerSerialNumber,ServerID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,ControllerLoginID,SanitizationVerificationID) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSanitizationVerificationID'])."')";
					$query5 = "insert into shipping_container_serials (ServerSerialNumber,ServerID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,SanitizationVerificationID,serial_scan_time,mpn_scan_time,COO) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSanitizationVerificationID'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$row['COO'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					//Start insert into tracking
					$action = 'Added to Shipment Container ('.$data['ShippingContainerID'].') ';
					$query6 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into tracking	
					
					//Start check IF MPN Changed
					if($data['Type'] == 'Switch') {

						if($row['MPN'] != $data['UniversalModelNumber']) {//If MPN of Asset Changed
							$query7 = "update speed_server_recovery set MPN = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ServerID = '".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."'";
							$q7 = mysqli_query($this->connectionlink,$query7);
							if(mysqli_error($this->connectionlink)) {
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);			
							}
	
							$desc = "Switch MPN Changed from '".$row['MPN']."' to '".$data['UniversalModelNumber']."' in Add Container Screen";
							$query8 = "insert into speed_server_recovery_tracking (ServerID,ServerSerialNumber,Type,idPallet,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['ServerID'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['Type'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
							$q8 = mysqli_query($this->connectionlink,$query8);
						}
					}					
					//End check IF MPN Changed
					
					$json['Success'] = true;
					$json['Result'] = $data['Type'].' added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function ValidateServerSerialNumberForContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			$query = "select s.*,d.disposition from speed_server_recovery s 
			left join disposition d on s.disposition_id = d.disposition_id 
			 where s.ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] == '8') {
					$json['Success'] = false;		
					$json['Result'] = 'SN is already on RZRR';			
					return json_encode($json);
				}

				if($row['StatusID'] == '6') {
					$json['Success'] = false;		
					$json['Result'] = 'SN has been shipped';			
					return json_encode($json);
				}				

				// if($row['StatusID'] != '1') {
				// 	$json['Success'] = false;		
				// 	$json['Result'] = 'Serial Number Status is not active';			
				// 	return json_encode($json);
				// }
				//Start get container Disposition				
				$query1 = "select s.*,d.disposition from shipping s 
				left join disposition d on s.disposition_id = d.disposition_id 
				where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

				$query1 = "select s.*,d.disposition from shipping_containers s 
				left join disposition d on s.disposition_id = d.disposition_id 
				where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}

					if($data['ByPassContainerMatching'] != '1') {
						//if($row1['disposition'] == 'Terminal-ServerRecycleProcessed'  && ($row['idPallet'] != $data['ShippingContainerID']) && $row['Type'] == 'Server') {
						if ($row1['disposition'] == 'Terminal-ServerRecycleProcessed' && strpos($row['idPallet'], $data['ShippingContainerID']) === false && ($row['Type'] == 'Server' || $row['Type'] == 'SERVER' || $row['Type'] == 'server')) {
							$json['Success'] = false;		
							$json['Result'] = 'Container ID and Rack ID of Serial should be same';		
							return json_encode($json);
						}
					}					

					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Removal Type is not matching with Serial Number';
						return json_encode($json);
					}
					$json['ServerID'] = $row['ServerID'];
					$json['MPN'] = $row['MPN'];
					$json['Type'] = $row['Type'];
					$json['MediaRecovery_VerificationID'] = $row['MediaRecovery_VerificationID'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get container Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetShipmentCarriers ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			$query = "select * from Carrier where StatusID = '1' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' order by CarrierName";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Carriers Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetFacilityPackageTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			$query = "select * from package where Active = '1' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."' and ContainerClassification = 'Outbound' order by packageName";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			$dispositions = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){
					$dispositions[$i] = $row;
					$i = $i + 1;
				}				
				$json['Success'] = true;			
				$json['Result'] = $dispositions;
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Container Types Available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateRemovalController1 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['UserName'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['RemovalController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Removal Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Valid';
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Controller or Password";
				return json_encode($json);
			}
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetSesstionFacility ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			$query = "select FacilityName,FacilityID from facility where FacilityID = '".$_SESSION['user']['FacilityID']."' ";			
			$q = mysqli_query($this->connectionlink,$query);			
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				$json['Success'] = true;			
				$json['FacilityName'] = $row['FacilityName'];
				$json['FacilityID'] = $row['FacilityID'];
				return json_encode($json);
			} else {
				$json['Success'] = false;			
				$json['Result'] = "No Facility Available for Loggedin User";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function DeleteByProductContainerFromShipment ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				if($shipping['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Shipment';
				return json_encode($json);		
			}
			//End get Shiping Disposition


			$query2 = "select count(*) from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' and (AssetScanID > 0 or InventoryID > 0 or ServerID > 0 or MediaID > 0)";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row2 = mysqli_fetch_assoc($q2);
				if($row2['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Only containers with Byproducts can be removed';
					return json_encode($json);
				}

				$query8 = "delete from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q8 = mysqli_query($this->connectionlink,$query8);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container';
				return json_encode($json);
			}


			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				if($container['LocationID'] > 0) {
					$query11 = "update location set Locked='2',currentItemType='',currentItemID='' where LocationID = '".mysqli_real_escape_string($this->connectionlink,$container['LocationID'])."' ";
					$q11 = mysqli_query($this->connectionlink,$query11);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}	
				}

			}
			//End get shipping container details

			
			$query8 = "delete from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q8 = mysqli_query($this->connectionlink,$query8);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Shipment Container is removed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function DeleteSerialFromShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Container';
				return json_encode($json);
			}

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				if($shipping['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Container Status is not active';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Shipment Container';
				return json_encode($json);		
			}
			//End get Shiping Disposition


			//Start validate BINNAME
			$query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$custompallet = mysqli_fetch_assoc($q);
				if($custompallet['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);
				}
				if($custompallet['disposition_id'] != $shipping['disposition_id']) {
					$json['Success'] = false;
					$json['Result'] = 'Serial Disposition is different from BIN Disposition';
					return json_encode($json);
				}
				if($custompallet['InventoryBased'] == '1' && ($data['SerialNumber'] != '' || $data['ServerSerialNumber'] != '')) {
					$json['Success'] = false;
					$json['Result'] = 'BIN is meant for Sub Component';
					return json_encode($json);
				}
				if($custompallet['InventoryBased'] == '0' && $data['InventorySerialNumber'] != '') {
					$json['Success'] = false;
					$json['Result'] = 'BIN is not meant for Sub Component';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
				return json_encode($json);
			}
			//End validate BINNAME
			
			//Start Delete Serial from Shipment Container
			$query2 = "delete from shipping_container_serials where SerialID = '".mysqli_real_escape_string($this->connectionlink,$data['SerialID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}	
			//End Delete Serial from Shipment Container

			//Start move asset to BIN
			if($data['SerialNumber'] != '') {
				$query6 = "insert into custompallet_items (CustomPalletID,AssetScanID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."',NOW(),'1')";
			} else if($data['InventoryID'] > 0) {
				$query6 = "insert into custompallet_items (CustomPalletID,InventoryID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['InventoryID'])."',NOW(),'1')";
			} else if($data['ServerID'] > 0) {
				$query6 = "insert into custompallet_items (CustomPalletID,ServerID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerID'])."',NOW(),'1')";
			} else if ($data['MediaID'] > 0) {
				$query6 = "insert into custompallet_items (CustomPalletID,MediaID,DateCreated,status) values ('".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."',NOW(),'1')";
			}
			$q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$query9 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."'";
			$q9 = mysqli_query($this->connectionlink,$query9);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if($data['SerialNumber'] != '') {
				$query3 = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '1',ShippingID = NULL,ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL,RecentWorkflowID = '6',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$desc = "Asset Removed from Shipment Container (Container ID : ".$data['ShippingContainerID'].") and Moved to BIN (BIN ID : ".$custompallet['BinName'].")";
				$query7 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else if($data['InventoryID'] > 0) {
				$query3 = "update inventory set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',InventoryStatusID = '1',ShippingID = NULL,ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$data['InventoryID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}	
			} else if($data['ServerID'] > 0) {
				$query3 = "update speed_server_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '1',ShippingContainerID = NULL,ShippingContainerAddedDate = NULL,ShippingContainerAddedBy = NULL,ShippingID = NULL where ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['ServerID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			} else if($data['MediaID'] > 0) {

				$query3 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$custompallet['CustomPalletID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '4',ShippingContainerID = NULL,DateAddedToShipmentContainer = NULL,ShippingID = NULL where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$desc = "Media Removed from Shipment Container (Container ID : ".$data['ShippingContainerID'].") and Moved to BIN (BIN ID : ".$custompallet['BinName'].")";
				//$query7 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$query7 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy,MediaType) values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['part_type'])."')";
				$q7 = mysqli_query($this->connectionlink,$query7);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

			}

			//End move asset to BIN

			$json['Success'] = true;
			$json['Result'] = 'Serial Number removed from Shipment Container';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetTrimmedMPN ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			$exact_mpn = $this->GetExactMPN($data['MPN']);
			if($exact_mpn['Success'] == true) {				
				$json['ExactMPN'] = $exact_mpn['MPN'];
				$json['Success'] = true;
			} else {
				$json['Success'] = false;
				$json['Result'] = $exact_mpn['Error'];
			}						
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function AddMediaToContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}


			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container


			//Start check If ContainerMPNLock Satisfies
			$contaner_lock = $this->isContainerMPNLock($data['ShippingID'],$data['UniversalModelNumber'],$data['ShippingContainerID']);
			if($contaner_lock['Success']) {                    				
			} else {
				$json['Success'] = false;
				$json['Result'] = $contaner_lock['Error'];
				return json_encode($json);
			}
			//End check If ContainerMPNLock Satisfied

			//Start get serialnumber details						
			//$query = "select m.*,s.Status from speed_media_recovery m,speed_status s where m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."' and m.StatusID = s.StatusID ";
			$query = "select m.*,s.Status,co.COO from speed_media_recovery m 
			left join speed_status s on m.StatusID = s.StatusID 
			left join COO co on m.COOID = co.COOID 
			where m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."'  ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				//if($row['InventoryStatusID'] != '1') {
				if($row['StatusID'] != '4') { //If not pending shipment status
					$json['Success'] = false;
					$json['Result'] = 'Media Status is not Pending Shipment';
					return json_encode($json);
				}
				//Start get Shipping Disposition
				//$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Removal Type is not matching with Media Disposition';
						return json_encode($json);
					}

					// if($row1['FacilityID'] != $row['FacilityID']) {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Facility is Different from Serial Number Facility';
					// 	return json_encode($json);
					// }

					//Start get Part type from MPN and validate MPN
					// $query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."'";
					// $q6 = mysqli_query($this->connectionlink,$query6);	
					// if(mysqli_error($this->connectionlink)) {			
					// 	$json['Success'] = false;		
					// 	$json['Result'] = mysqli_error($this->connectionlink);			
					// 	return json_encode($json);			
					// }
					// if(mysqli_affected_rows($this->connectionlink) > 0) {
					// 	$row6 = mysqli_fetch_assoc($q6);
					// } else {
					// 	$json['Success'] = false;
					// 	$json['Result'] = 'Invalid MPN';
					// 	return json_encode($json);
					// }
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where MediaID = '".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update speed_media_recovery set CustomPalletID = NULL,StatusID=5,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',DateAddedToShipmentContainer = NOW(),ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."')";
					//$query5 = "insert into shipping_container_serials (MediaSerialNumber,MediaID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					$query5 = "insert into shipping_container_serials (MediaSerialNumber,MediaID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,serial_scan_time,mpn_scan_time,COO) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$row[['COO']])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					
					//Start insert into tracking
					$action = 'Meida Added to Shipment Container ('.$data['ShippingContainerID'].')';
					$query6 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy,MediaType,ServerSerialNumber,idPallet) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."')";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into tracking					
					$json['Success'] = true;
					$json['Result'] = 'Media added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Media Serial Number';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateMediaSerialNumber ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			//$query = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and StatusID = 1";
			$query = "select * from speed_media_recovery where MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by StatusID";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] != '4') {
					$json['Success'] = false;		
					$json['Result'] = 'Media Status is not Pending Shipment';			
					return json_encode($json);
				}

				$rack_received_since = $this->getTimeSinceRackReceived($row['idPallet']);
				if($rack_received_since['Success'] == true) {
					if($rack_received_since['HoursSinceReceived'] < 14) {

					} else {
						if($row['MediaType'] == 'HDD') {
							$new_status = '1';
							$new_status_text = 'PendingDegauss';
						} else if($row['MediaType'] == 'SSD') {
							$new_status = '2';
							$new_status_text = 'PendingShred';
						} else {
							$json['Success'] = false;		
							$json['Result'] = 'Invalid media';			
							return json_encode($json);
						}

						//Start Update media status to PendingDegauss/Pendingshred since SLA is crossed
						$query16 = "update speed_media_recovery set StatusID = '".$new_status ."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where MediaID = '".$row['MediaID']."' ";
						$q16 = mysqli_query($this->connectionlink,$query16);	
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}

						$action = $row['MediaType']. ' status updated to '.$new_status_text.' on Shipment Prep Screen';
						$query6 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy,MediaType,ServerSerialNumber,idPallet) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."')";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);			
						}

						//End Update media status to PendingDegauss/Pendingshred since SLA is crossed

						$json['SLAFailed'] = '1';
						$json['Success'] = false;
						$json['Result'] = '14 Hours SLA for adding Media to shipment was not met, '.$row['MediaType']. ' status updated to '.$new_status_text;
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = $rack_received_since['Error'];
					return json_encode($json);
				}
				
				//Start get Shipping Disposition
				$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					// if($row1['ShipmentStatusID'] != '1') {
					// 	$json['Success'] = false;		
					// 	$json['Result'] = 'Shipment Status is not active';			
					// 	return json_encode($json);
					// }

					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Shipment Removal Type is not matching with Media ';
						return json_encode($json);
					}
					$json['MediaID'] = $row['MediaID'];
					$json['UniversalModelNumber'] = $row['MediaMPN'];
					$json['MediaType'] = $row['MediaType'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Media SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ValidateMediaSerialNumberForShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			//Start get serialnumber details
			//$query = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and StatusID = 1";
			$query = "select * from speed_media_recovery where MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by StatusID";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);

				if($row['StatusID'] != '4') {
					$json['Success'] = false;		
					$json['Result'] = 'Media Status is not Pending Shipment';			
					return json_encode($json);
				}

				$rack_received_since = $this->getTimeSinceRackReceived($row['idPallet']);
				if($rack_received_since['Success'] == true) {
					if($rack_received_since['HoursSinceReceived'] < 14) {

					} else {
						if($row['MediaType'] == 'HDD') {
							$new_status = '1';
							$new_status_text = 'PendingDegauss';
						} else if($row['MediaType'] == 'SSD') {
							$new_status = '2';
							$new_status_text = 'PendingShred';
						} else {
							$json['Success'] = false;		
							$json['Result'] = 'Invalid media';			
							return json_encode($json);
						}

						//Start Update media status to PendingDegauss/Pendingshred since SLA is crossed
						$query16 = "update speed_media_recovery set StatusID = '".$new_status ."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where MediaID = '".$row['MediaID']."' ";
						$q16 = mysqli_query($this->connectionlink,$query16);	
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}

						$action = $row['MediaType']. ' status updated to '.$new_status_text.' on Shipment Container Screen';
						$query6 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy,MediaType,ServerSerialNumber,idPallet) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."')";
						$q6 = mysqli_query($this->connectionlink,$query6);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);			
						}

						//End Update media status to PendingDegauss/Pendingshred since SLA is crossed

						$json['SLAFailed'] = '1';
						$json['Success'] = false;
						$json['Result'] = '14 Hours SLA for adding Media to shipment was not met , '.$row['MediaType']. ' status updated to '.$new_status_text;
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = $rack_received_since['Error'];
					return json_encode($json);
				}

				
				//Start get Shipping Disposition				
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);				
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Serial Number';
						return json_encode($json);
					}
					$json['MediaID'] = $row['MediaID'];
					$json['UniversalModelNumber'] = $row['MediaMPN'];
					$json['MediaType'] = $row['MediaType'];
					$json['Success'] = true;
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment Container';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Media SN is not in eViridis';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function AddMediaToShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Container';
				return json_encode($json);
			}


			//Start check If any byproduct is added to container
			$query55 = "select count(*) from shipping_container_serials where byproduct_id > 0 and ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";			
			$q55 = mysqli_query($this->connectionlink,$query55);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row55 = mysqli_fetch_assoc($q55);
				if($row55['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Byproduct is added to container,No other items can be added when Byproduct is added to container';
					return json_encode($json);
				}
			}
			//End check If any byproduct is added to container


			//Start check If ContainerMPNLock Satisfies
			// $contaner_lock = $this->isContainerMPNLock($data['ShippingID'],$data['UniversalModelNumber'],$data['ShippingContainerID']);
			// if($contaner_lock['Success']) {                    				
			// } else {
			// 	$json['Success'] = false;
			// 	$json['Result'] = $contaner_lock['Error'];
			// 	return json_encode($json);
			// }
			//End check If ContainerMPNLock Satisfied

			//Start get serialnumber details						
			$query = "select m.*,s.Status from speed_media_recovery m,speed_status s where m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."' and m.StatusID = s.StatusID ";
			$query = "select m.*,s.Status,co.COO from speed_media_recovery m 
			left join speed_status s on m.StatusID = s.StatusID 
			left join COO co on m.COOID = co.COOID 
			where m.MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."'  ";
			$q = mysqli_query($this->connectionlink,$query);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				//if($row['InventoryStatusID'] != '1') {
				if($row['StatusID'] != '4') { //If not pending shipment status
					$json['Success'] = false;
					$json['Result'] = 'Media Status is not Pending Shipment';
					return json_encode($json);
				}
				//Start get Shipping Disposition				
				$query1 = "select s.* from shipping_containers s where s.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;		
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['StatusID'] != '1') {
						$json['Success'] = false;		
						$json['Result'] = 'Container Status is not active';			
						return json_encode($json);
					}
					if($row1['disposition_id'] != $row['disposition_id']) {
						$json['Success'] = false;		
						$json['Result'] = 'Container Removal Type is not matching with Media Serial Number';
						return json_encode($json);
					}

					//Start get Part type from MPN and validate MPN
					if($data['UniversalModelNumber'] != '')
					{
						$query6 = "select part_type from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."'";
						$q6 = mysqli_query($this->connectionlink,$query6);	
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row6 = mysqli_fetch_assoc($q6);
						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid MPN';
							return json_encode($json);
						}
					}
					else
					{
						$row6['part_type'] = '';
					}
					//End get Part type from MPN and validate MPN

					//Start Remove from CustomPallet
					$query2 = "delete from custompallet_items where MediaID = '".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."'";
					$q2 = mysqli_query($this->connectionlink,$query2);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query3 = "update custompallet set AssetsCount = AssetsCount - 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
					$q3 = mysqli_query($this->connectionlink,$query3);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					$query4 = "update speed_media_recovery set CustomPalletID = NULL,StatusID=5,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',DateAddedToShipmentContainer = NOW() where MediaID = '".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."'";
					$q4 = mysqli_query($this->connectionlink,$query4);	
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					//End Remove from CustomPallet

					//Start insert into Shipment Container
					//$query5 = "insert into shipping_container_serials (InventorySerialNumber,InventoryID,Notes,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber) values ('".mysqli_real_escape_string($this->connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['InventoryID'])."','".mysqli_real_escape_string($this->connectionlink,$data['Notes'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['UniversalModelNumber'])."')";
					//$query5 = "insert into shipping_container_serials (MediaSerialNumber,MediaID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,ControllerLoginID) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControllerLoginID'])."')";
					$query5 = "insert into shipping_container_serials (MediaSerialNumber,MediaID,StatusID,ShippingContainerID,CreatedDate,CreatedBy,UniversalModelNumber,part_type,serial_scan_time,mpn_scan_time,COO) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','1','".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$row['COO'])."')";
					$q5 = mysqli_query($this->connectionlink,$query5);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into Shipment Container

					
					//Start insert into tracking
					$action = 'Meida Added to Shipment Container ('.$data['ShippingContainerID'].')';
					$query6 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,Action,Description,CreatedDate,CreatedBy,MediaType,ServerSerialNumber,idPallet) values ('".mysqli_real_escape_string($this->connectionlink,$row['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$row['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$row['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$row['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$row['idPallet'])."')";
					$q6 = mysqli_query($this->connectionlink,$query6);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);			
					}
					//End insert into tracking					
					$json['Success'] = true;
					$json['Result'] = 'Media added to Shipment Container';
					return json_encode($json);
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Shipment';
					return json_encode($json);		
				}
				//End get Shiping Disposition
			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Media Serial Number';			
				return json_encode($json);
			}
			//End get serialnumber details
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function CloseContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			//Start validate Controller
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['RemovalController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Removal Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Controller or Password";
				return json_encode($json);
			}
			//End validate Controller

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				if($shipping['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Shipment';
				return json_encode($json);		
			}
			//End get Shiping Disposition

			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				
				if($container['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not active';			
					return json_encode($json);
				}


				//Start check for min weight
				$query4 = "select packageWeight from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$container['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					if($row4['packageWeight'] >= $data['ContainerWeight']) {
						$json['Success'] = false;			
						$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight

				$query2 = "update shipping_containers set StatusID = '6',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['NewSealID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Result'] = 'Container Closed';
				$this->UpdateShipmentContainerPartTypeSummary($data['ShippingContainerID']);

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Shipment Container is Closed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ReopenContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			//Start get Shipping Disposition
			$query1 = "select s.* from shipping s where s.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$shipping = mysqli_fetch_assoc($q1);
				if($shipping['ShipmentStatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Shipment Status is not active';			
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Shipment';
				return json_encode($json);		
			}
			//End get Shiping Disposition
			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				
				if($container['StatusID'] != '6') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not Closed';			
					return json_encode($json);
				}
				
				$query2 = "update shipping_containers set StatusID = '1',SealID = NULL,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Result'] = 'Container Reopened';
				$this->ResetShipmentContainerPartTypeSummary($data['ShippingContainerID']);

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Shipment Container Reopened';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function CheckClosedContainers ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Removal';
				return json_encode($json);
			}

			//Start check If all containers has atleast one item
			$query12 = "select * from shipping_containers where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
			$q12 = mysqli_query($this->connectionlink,$query12);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				//Start check every container has atleast one serial				
				while($row12 = mysqli_fetch_assoc($q12)) {
					if($row12['ASNContainer'] == '1') {
						continue;
					}
					$query13 = "select count(*) from shipping_container_serials where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$row12['ShippingContainerID'])."'";
					$q13 = mysqli_query($this->connectionlink,$query13);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;			
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row13 = mysqli_fetch_assoc($q13);
						if($row13['count(*)'] == 0) {
							$json['Success'] = false;		
							$json['Result'] = 'Shipment Container '.$row12['ShippingContainerID'].' do not have any items in it';
							return json_encode($json);
						}
					} else {
						$json['Success'] = false;			
						$json['Result'] = 'Invalid';			
						return json_encode($json);
					}					
				}
				//End check every container has atlease one serial
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No containers available in the shipment';
				return json_encode($json);
			}
			//End check If all containers has atleast one item

			$query = "select count(*) from shipping_containers where StatusID != 6 and ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;			
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				
				if($row['count(*)'] > 0) {
					$json['Success'] = false;		
					$json['Result'] = $row['count(*)'].' containers are not closed.Close the container using TPVR Controller';			
					return json_encode($json);
				}

				//Start loop through shipping serials to find any media not met SLA
				$query11 = "select s.*,sh.ShippingID,m.idPallet FROM shipping_container_serials s,shipping_containers c,shipping sh,speed_media_recovery m where s.ShippingContainerID = c.ShippingContainerID and c.ShippingID = sh.ShippingID and s.MediaID > 0 and sh.ShippingID= '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' and s.MediaID = m.MediaID";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($row11 = mysqli_fetch_assoc($q11)) {
						$rack_received_since = $this->getTimeSinceRackReceived($row11['idPallet']);
						if($rack_received_since['Success'] == true) {
							if($rack_received_since['HoursSinceReceived'] < 68) {

							} else {
								$json['Success'] = false;
								$json['Result'] = '68 Hour SLA for Removing Media was not met for Media Serial '.$row11['MediaSerialNumber'].' (Container ID : '.$row11['idPallet'].')';
								return json_encode($json);
							}
						} else {
							$json['Success'] = false;
							$json['Result'] = $rack_received_since['Error'];
							return json_encode($json);
						}
					}
				}
				//Start loop through shipping serials to find any media not met SLA


				$json['Success'] = true;			
				$json['Result'] = 'All containers are closed';
				return json_encode($json);

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment';			
				return json_encode($json);	
			}

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function CloseShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Container';
				return json_encode($json);
			}

			//Start validate Controller
			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['RemovalController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Removal Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Removal Controller or Password";
				return json_encode($json);
			}
			//End validate Controller
			
			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				
				if($container['StatusID'] != '1') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not active';			
					return json_encode($json);
				}


				//Start check for min weight
				$query4 = "select packageWeight from package where idPackage = '".mysqli_real_escape_string($this->connectionlink,$container['idPackage'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					if($row4['packageWeight'] >= $data['ContainerWeight']) {
						$json['Success'] = false;			
						$json['Result'] = 'Container weight should be greater than Container Type weight ('.$row4['packageWeight'].')';			
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;			
					$json['Result'] = 'Invalid Container Type';			
					return json_encode($json);
				}
				//End check for min weight

				$query2 = "update shipping_containers set StatusID = '6',SealID = '".mysqli_real_escape_string($this->connectionlink,$data['NewSealID'])."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['ContainerWeight'])."' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Result'] = 'Container Closed';

				$this->UpdateShipmentContainerPartTypeSummary($data['ShippingContainerID']);

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Shipment Container is Closed';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetShippingContainerDetails1 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Container';
				return json_encode($json);
			}

			if($data['ShippingContainerID']) {
				$query = "select c.*,l.LocationName as location,g.GroupName,g.GroupName as `group` from shipping_containers c 
				left join location l on c.LocationID = l.LocationID 
				LEFT JOIN location_group g on l.GroupID = g.GroupID 
				where c.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
				$q = mysqli_query($this->connectionlink,$query);	
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['ShippingID'] != '') {
						$json['Success'] = false;			
						$json['Result'] = 'Container added to Shipment';			
						return json_encode($json);			
					}
					// if($row['StatusID'] != '1') {
					// 	$json['Success'] = false;			
					// 	$json['Result'] = 'Container status is not active';			
					// 	return json_encode($json);			
					// }

					if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
						$json['Success'] = false;			
						$json['Result'] = 'Container Facility is different from User Facility';			
						return json_encode($json);			
					}
					$row['ContainerWeight'] = floatval($row['ContainerWeight']);
					$json['Success'] = true;
					$json['Result'] = $row;
					return json_encode($json);	

				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Container ID';
					return json_encode($json);	
				}

			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Container ID';
				return json_encode($json);
			}

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function ReopenContainer1 ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}						
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Container';
				return json_encode($json);
			}
			
			//Start get shipping container details
			$query10 = "select * from shipping_containers where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
			$q10 = mysqli_query($this->connectionlink,$query10);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$container = mysqli_fetch_assoc($q10);
				
				if($container['StatusID'] != '6') {
					$json['Success'] = false;		
					$json['Result'] = 'Container Status is not Closed';			
					return json_encode($json);
				}
				
				$query2 = "update shipping_containers set StatusID = '1',SealID = NULL,UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentSealDate = NOW(),RecentSealBy = '".$_SESSION['user']['UserId']."',ShippingControllerLoginID = '' where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {			
					$json['Success'] = false;			
					$json['Result'] = mysqli_error($this->connectionlink);			
					return json_encode($json);			
				}
				$json['Result'] = 'Container Reopened';
				$this->ResetShipmentContainerPartTypeSummary($data['ShippingContainerID']);

			} else {
				$json['Success'] = false;			
				$json['Result'] = 'Invalid Shipment Container';			
				return json_encode($json);	
			}
			//End get shipping container details
			
			$json['Success'] = true;
			$json['Result'] = 'Shipment Container Reopened';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function UnlinkContainerFromShipment ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Sub Shipment Prep';
				return json_encode($json);
			}

			//Start updating PalletID to Shipment Container
			$query = "update shipping_containers set ShippingID = NULL,container_added_to_shipment_time = NULL where ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingContainerID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End updating PalletID to Shipment Container

			$json['Success'] = true;
			$json['Result'] = 'Container unlinked from Shipment';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ValidateSanitizationSealID ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Prep')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Prep Page';
				return json_encode($json);
			}

			$query22 = "select sanitization_verification_id,sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' order by CreatedDate desc";
			$q22 = mysqli_query($this->connectionlink,$query22);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row22 = mysqli_fetch_assoc($q22);
				if($row22['sanitization_seal_id'] != $data['sanitization_seal_id'])	{
					$json['Success'] = false;
					$json['Result'] = 'Seal ID not matching';
					return json_encode($json);	
				} else {
					$json['Success'] = true;
					$json['Result'] = 'Seal ID matches';
					$json['sanitization_verification_id'] = $row22['sanitization_verification_id'];
					return json_encode($json);	
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Serial not involved in Sanitization';
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Container unlinked from Shipment';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function ValidateSanitizationSealIDforShipmentContainer ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Container')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Container Page';
				return json_encode($json);
			}

			$query22 = "select sanitization_verification_id,sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' order by CreatedDate desc";
			$q22 = mysqli_query($this->connectionlink,$query22);	
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row22 = mysqli_fetch_assoc($q22);
				if($row22['sanitization_seal_id'] != $data['sanitization_seal_id'])	{
					$json['Success'] = false;
					$json['Result'] = 'Seal ID not matching';
					return json_encode($json);	
				} else {
					$json['Success'] = true;
					$json['Result'] = 'Seal ID matches';
					$json['sanitization_verification_id'] = $row22['sanitization_verification_id'];
					return json_encode($json);	
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Serial not involved in Sanitization';
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = 'Container unlinked from Shipment';
			return json_encode($json);
			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GenerateDemanASN ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Shipment Removal Page';
				return json_encode($json);
			}			
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Shipment Removal')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Shipment Removal';
				return json_encode($json);
			}

			//Start check If ASN alredy generated or not
			$query = "select * from shipping where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {			
				$json['Success'] = false;		
				$json['Result'] = mysqli_error($this->connectionlink);			
				return json_encode($json);			
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['DemanASNGenerated'] == '1') {
					$json['Success'] = false;		
					$json['Result'] = 'ASN generated for the Shipment';			
					return json_encode($json);
				} else {
					$query1 = "select 'MDT110' as origin_location_id,c.ShippingID as origin_ticket_id,c.ShippingContainerID as container_id,'Rack' as container_type,c.SealID as seal1,'' as seal2,'' as seal3, '' as seal4,c.ContainerWeight as weight_value,'100-002893-001' as container_apn_id,s.ServerSerialNumber as serial_id,s.UniversalModelNumber as mpn_id,'Rack values' as material_type
					FROM shipping_container_serials s, shipping_containers c,shipping sh  
				   	where s.ShippingContainerID = c.ShippingContainerID and c.ShippingID = sh.ShippingID and sh.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

					$query1 = "select f.FacilityName as origin_location_id,c.ShippingID as origin_ticket_id,c.ShippingContainerID as container_id,'Rack' as container_type,c.SealID as seal1,'' as seal2,'' as seal3, '' as seal4,c.ContainerWeight as weight_value,'' as serial_id,'' as mpn_id,'Rack values' as material_type,c.BatchRecovery   
					from shipping_containers c 
					left join shipping s on c.ShippingID = s.ShippingID 
					left join facility f on s.FacilityID = f.FacilityID 
					where c.ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."'";

					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {			
						$json['Success'] = false;		
						$json['Result'] = mysqli_error($this->connectionlink);			
						return json_encode($json);			
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$i = 0;
						$records = array();
						while($row1 = mysqli_fetch_assoc($q1)) {
							//Start get container_apn_id from asn
							$query2 = "select asn.apn_id from shipping_container_serials se,speed_server_recovery s, asn_assets asn 
							where 
							se.ServerID = s.ServerID and s.idPallet = asn.idPallet and se.ServerID > 0 and se.ShippingContainerID = '".mysqli_real_escape_string($this->connectionlink,$row1['container_id'])."'";
							$q2 = mysqli_query($this->connectionlink,$query2);
							if(mysqli_error($this->connectionlink)) {			
								$json['Success'] = false;		
								$json['Result'] = mysqli_error($this->connectionlink);			
								return json_encode($json);			
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row2 = mysqli_fetch_assoc($q2);
								$row1['container_apn_id'] = $row2['apn_id'];
							} else {
								$row1['container_apn_id'] = '';
							}
							//End get container_apn_id from asn
							$records[$i] = $row1;
							$i++;
						}
						$in_deman = $this->CreateASNinDeman($records);
						if($in_deman['Success'] == false) {
							$json['Success'] = false;		
							$json['Result'] = $in_deman;			
							return json_encode($json);
						}
						//Start update in shipment about deman creation
						$query4 = "update shipping set DemanASNGenerated = '1',DemanASNGeneratedDate = NOW(),DemanASNGeneratedBy = '".$_SESSION['user']['UserId']."' where ShippingID = '".mysqli_real_escape_string($this->connectionlink,$data['ShippingID'])."' ";
						$q4 = mysqli_query($this->connectionlink,$query4);
						if(mysqli_error($this->connectionlink)) {			
							$json['Success'] = false;		
							$json['Result'] = mysqli_error($this->connectionlink);			
							return json_encode($json);			
						}
						//End update in shipment about deman creation
						$json['Success'] = true;		
						$json['Result'] = 'Created in Deman';			
						return json_encode($json);
					} else {
						$json['Success'] = false;		
						$json['Result'] = 'No Data with Servers available in the shipment';			
						return json_encode($json);	
					}
				}
			} else {
				$json['Success'] = false;		
				$json['Result'] = 'Invalid Shipment';			
				return json_encode($json);	
			}
			//End check If ASN already generated or not

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetCOOList ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			$query = "select COOID,COO from COO where Status = '1' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$COOList = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){					
					$COOList[$i] = $row;
					$i = $i + 1;
				}				
				$json['COOList'] = $COOList;
			} 
			$json['Success'] = true;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function ManageReferenceType ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);		
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Reference Type')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Reference Type Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Reference Type')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Reference Type';
				return json_encode($json);
			}		
			
			//Start convert facility array to string
			if (in_array("ALL", $data['FacilityIDList'])) {
				$FacilityNameList = 'ALL';
				$FacilityIDList = 'ALL';
			} else {

				$facilityNames = [];
				$ids = implode(",", array_map('intval', $data['FacilityIDList']));

				// Fetch facility names from database
				$query = "select FacilityName FROM facility WHERE FacilityID IN ($ids)";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while ($row = mysqli_fetch_assoc($q)) {
						$facilityNames[] = $row["FacilityName"];
					}
				}
			
				// Convert facility names into a comma-separated string
				$FacilityNameList = implode(", ", $facilityNames);
				$FacilityIDList = $ids;
			}
			//End convert facility array to string


			//Start convert disposition array to string
			if (in_array("ALL", $data['disposition_id_list'])) {
				$disposition_list = 'ALL';
				$disposition_id_list = 'ALL';
			} else {

				$dispositionNames = [];
				$ids = implode(",", array_map('intval', $data['disposition_id_list']));

				// Fetch facility names from database
				$query = "select disposition FROM disposition WHERE disposition_id IN ($ids)";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while ($row = mysqli_fetch_assoc($q)) {
						$dispositionNames[] = $row["disposition"];
					}
				}
			
				// Convert facility names into a comma-separated string
				$disposition_list = implode(", ", $dispositionNames);
				$disposition_id_list = $ids;
			}
			//End convert disposition array to string

			if($data['ReferenceTypeID'] > 0) {//update existing configuration

				//Start check for duplication
				$query = "select count(*) from shipping_reference_type where ReferenceType = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."' and ReferenceTypeID != '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = "Reference Type Name already exists";
						return json_encode($json);
					}
				}
				//End check for duplication

				$query = "select count(*) from shipping_reference_type where FacilityIDList = '".mysqli_real_escape_string($this->connectionlink,$FacilityIDList)."' and disposition_id_list = '".mysqli_real_escape_string($this->connectionlink,$disposition_id_list)."' and ReferenceTypeID != '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."'";				
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = "Facility and Removal Type combination already exists";
						return json_encode($json);
					}
				}


				
				$query = "UPDATE shipping_reference_type 
				SET FacilityIDList = '" . mysqli_real_escape_string($this->connectionlink, $FacilityIDList) . "', 
					FacilityNameList = '" . mysqli_real_escape_string($this->connectionlink, $FacilityNameList) . "', 
					ReferenceType = '" . mysqli_real_escape_string($this->connectionlink, $data['ReferenceType']) . "', 
					ReferenceTypeDescription = '" . mysqli_real_escape_string($this->connectionlink, $data['ReferenceTypeDescription']) . "', 
					Status = '" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "', 
					ReferenceIDRequired = '" . mysqli_real_escape_string($this->connectionlink, $data['ReferenceIDRequired']) . "', 
					ReferenceID = '" . mysqli_real_escape_string($this->connectionlink, $data['ReferenceID']) . "', 
					disposition_id_list = '" . mysqli_real_escape_string($this->connectionlink, $disposition_id_list) . "', 
					disposition_list = '" . mysqli_real_escape_string($this->connectionlink, $disposition_list) . "', 
					UpdatedDate = NOW(), 
					UpdatedBy = '" . mysqli_real_escape_string($this->connectionlink, $_SESSION['user']['UserId']) . "' 
				WHERE ReferenceTypeID = '" . mysqli_real_escape_string($this->connectionlink, $data['ReferenceTypeID']) . "'";

				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$json['Success'] = true;
				$json['Result'] = 'Configuration Modified';
				return json_encode($json);			
			} else {//Create new configuration
				//Start check for duplication
				$query = "select count(*) from shipping_reference_type where ReferenceType = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = "Reference Type Name already exists";
						return json_encode($json);
					}
				}
				//End check for duplication

				$query = "select count(*) from shipping_reference_type where FacilityIDList = '".mysqli_real_escape_string($this->connectionlink,$FacilityIDList)."' and disposition_id_list = '".mysqli_real_escape_string($this->connectionlink,$disposition_id_list)."'";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}				
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row = mysqli_fetch_assoc($q);
					if($row['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = "Facility and Removal Type combination already exists";
						return json_encode($json);
					}
				}

				$query = "insert into shipping_reference_type (FacilityIDList,FacilityNameList,ReferenceType,ReferenceTypeDescription,Status,ReferenceIDRequired,ReferenceID,disposition_id_list,disposition_list,CreatedDate,CreatedBy) value ('".mysqli_real_escape_string($this->connectionlink,$FacilityIDList)."','".mysqli_real_escape_string($this->connectionlink,$FacilityNameList)."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceType'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeDescription'])."','".mysqli_real_escape_string($this->connectionlink,$data['Status'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceIDRequired'])."','".mysqli_real_escape_string($this->connectionlink,$data['ReferenceID'])."','".mysqli_real_escape_string($this->connectionlink,$disposition_id_list)."','".mysqli_real_escape_string($this->connectionlink,$disposition_list)."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = 'Configuration Created';
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetAllReferenceTypes ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			$query = "select ReferenceTypeID,ReferenceType,ReferenceIDRequired,ReferenceID from shipping_reference_type where Status = 'Active' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			$COOList = array();
			$i = 0;
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)){					
					$COOList[$i] = $row;
					$i = $i + 1;
				}				
				$json['Result'] = $COOList;
			}
			$json['Result'] = $COOList;
			$json['Success'] = true;
			return json_encode($json);
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetReferenceTypeDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			$query = "select ReferenceTypeID,ReferenceType,ReferenceIDRequired,ReferenceID from shipping_reference_type where Status = 'Active' ";
			$query ="select * FROM shipping_reference_type WHERE 
    		(FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."', FacilityIDList) OR FacilityNameList = 'ALL')
			AND 
    		(FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."', disposition_id_list) OR disposition_id_list = 'ALL')
			AND Status = 'Active'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);				
				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Matching Reference Types available';
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetReferenceTypeList($data) {
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['ReferenceTypeID']
			);

			/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Recoverytype')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Recoverytype Page';
				return json_encode($json);
			}*/

			$query = "select * from shipping_reference_type where 1 ";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'FacilityNameList') {
							$query = $query . " AND FacilityNameList like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ReferenceType') {
							$query = $query . " AND ReferenceType like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ReferenceTypeDescription') {
							$query = $query . " AND ReferenceTypeDescription like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Status') {
							$query = $query . " AND Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ReferenceIDRequired') {
							$query = $query . " AND ReferenceIDRequired like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ReferenceID') {
							$query = $query . " AND ReferenceID like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'disposition_list') {
							$query = $query . " AND disposition_list like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'FacilityNameList') {
					$query = $query . " order by FacilityNameList " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ReferenceType') {
					$query = $query . " order by ReferenceType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ReferenceTypeDescription') {
					$query = $query . " order by ReferenceTypeDescription " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Status') {
					$query = $query . " order by Status " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ReferenceIDRequired') {
					$query = $query . " order by ReferenceIDRequired " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ReferenceID') {
					$query = $query . " order by ReferenceID " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'disposition_list') {
					$query = $query . " order by disposition_list " . $order_by_type . " ";
				} 
			} else {
				$query = $query . " order by ReferenceTypeID desc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));
			//echo $query;exit;
			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					if($row['ReferenceIDRequired'] == '1') {
						$row['ReferenceIDRequiredText'] = 'Yes';
					} else {
						$row['ReferenceIDRequiredText'] = 'No';
					}
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Reference Type Available";
			}

			if ($data['skip'] == 0) {

				$query1 = "select count(*) from shipping_reference_type where 1";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'FacilityNameList') {
								$query1 = $query1 . " AND FacilityNameList like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ReferenceType') {
								$query1 = $query1 . " AND ReferenceType like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ReferenceTypeDescription') {
								$query1 = $query1 . " AND ReferenceTypeDescription like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Status') {
								$query1 = $query1 . " AND Status like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ReferenceIDRequired') {
								$query1 = $query1 . " AND ReferenceIDRequired like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'ReferenceID') {
								$query1 = $query1 . " AND ReferenceID like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'disposition_list') {
								$query1 = $query1 . " AND disposition_list like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function GetEditReferenceTypeDetails($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		$query = "select * from shipping_reference_type where ReferenceTypeID = '" . mysqli_real_escape_string($this->connectionlink, $data['ReferenceTypeID']) . "' ";
			/*$query ="select * FROM awsdev.shipping_reference_type WHERE 
    		(FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink,$data['FacilityID'])."', FacilityIDList) OR FacilityNameList = 'ALL')
			AND 
    		(FIND_IN_SET('".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."', disposition_id_list) OR disposition_id_list = 'ALL')
			AND Status = 'Active'";*/

		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid Reference Type ID";
		}
		return json_encode($json);
	}	

	public function GenerateReferenceTypeListxls($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['ReferenceTypeListxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}


	public function GetReferenceType ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {			
			$query ="select * FROM shipping_reference_type WHERE ReferenceTypeID = '".mysqli_real_escape_string($this->connectionlink,$data['ReferenceTypeID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);				
				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Matching Reference Types available';
				return json_encode($json);
			}			
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>