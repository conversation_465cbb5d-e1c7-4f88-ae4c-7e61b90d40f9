<?php

session_start();
include_once("../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
/**
 * Shipping Container to Bin Conversion Script
 * Safe execution with batching and duplicate prevention
 * Can be run multiple times safely
 */
// Batch sizes for different operations (smaller batches for stability)
$CONTAINER_BATCH_SIZE = 250;    // Containers to convert per batch
$SERIAL_BATCH_SIZE = 1000;      // Serials to update per batch
$ITEM_BATCH_SIZE = 500;         // Items to create per batch

// Set charset and disable safe updates
mysqli_query($connectionlink, "SET NAMES utf8mb4");
mysqli_query($connectionlink, "SET SQL_SAFE_UPDATES = 0");

echo "<h1>Shipping Container to Bin Conversion</h1>\n";
echo "<pre>\n";

// Get system user ID
//$systemUserId = getSystemUserId($connectionlink);
$systemUserId = '712';
echo "System User ID: " . ($systemUserId ? $systemUserId : 'NULL') . "\n\n";

// Step 1: Create conversion log table (if not exists)
createConversionLogTable($connectionlink);

// Step 2: Convert shipping containers to bins
convertContainersToBins($connectionlink, $systemUserId, $CONTAINER_BATCH_SIZE);

// Step 3: Create custompallet_items from shipping_container_serials
createCustomPalletItems($connectionlink, $systemUserId, $ITEM_BATCH_SIZE);

// Step 4: Update shipping_container_serials with new references
updateShippingContainerSerials($connectionlink, $systemUserId, $SERIAL_BATCH_SIZE);

// Step 5: Update asset counts
updateAssetCounts($connectionlink);

// Step 6: Set conversion flags
setConversionFlags($connectionlink, $systemUserId);

// Step 7: Update location table
updateLocationTable($connectionlink);

// Step 8: Add tracking records
addTrackingRecords($connectionlink, $systemUserId);

// Step 9: Generate final report
generateFinalReport($connectionlink);

echo "\n=== CONVERSION COMPLETED ===\n";
echo "</pre>\n";

// =====================================================
// HELPER FUNCTIONS
// =====================================================

function getSystemUserId($connectionlink) {
    $result = mysqli_query($connectionlink, "SELECT MIN(UserId) as user_id FROM users WHERE UserId > 0 LIMIT 1");
    if ($result && $row = mysqli_fetch_assoc($result)) {
        return $row['user_id'];
    }
    return null;
}

function createConversionLogTable($connectionlink) {
    echo "=== STEP 1: Creating Conversion Log Table ===\n";

    $sql = "CREATE TABLE IF NOT EXISTS container_conversion_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ShippingContainerID VARCHAR(50),
        NewCustomPalletID INT,
        ConversionDate DATETIME DEFAULT NOW(),
        INDEX idx_container_id (ShippingContainerID),
        INDEX idx_pallet_id (NewCustomPalletID)
    )";

    if (mysqli_query($connectionlink, $sql)) {
        echo "✓ Conversion log table ready\n";
    } else {
        die("✗ Error creating conversion log table: " . mysqli_error($connectionlink) . "\n");
    }
}

function convertContainersToBins($connectionlink, $systemUserId, $batchSize) {
    echo "\n=== STEP 2: Converting Shipping Containers to Bins (ACTIVE SHIPMENTS ONLY) ===\n";

    // Check for invalid FacilityIDs first (ACTIVE SHIPMENTS ONLY)
    echo "Checking for invalid FacilityIDs in active shipments...\n";
    $invalidFacilityResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as invalid_count
        FROM shipping_containers sc
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND sc.FacilityID IS NOT NULL
          AND sc.FacilityID != 0
          AND NOT EXISTS (
              SELECT 1 FROM facility f WHERE f.FacilityID = sc.FacilityID
          )
    ");
    $invalidFacilityRow = mysqli_fetch_assoc($invalidFacilityResult);
    $invalidFacilityCount = $invalidFacilityRow['invalid_count'];

    if ($invalidFacilityCount > 0) {
        echo "⚠ Found $invalidFacilityCount containers with invalid FacilityIDs\n";
        echo "Getting default FacilityID...\n";

        // Get the first available FacilityID as default
        $defaultFacilityResult = mysqli_query($connectionlink, "SELECT MIN(FacilityID) as default_facility FROM facility WHERE FacilityID > 0 LIMIT 1");
        $defaultFacilityRow = mysqli_fetch_assoc($defaultFacilityResult);
        $defaultFacilityID = $defaultFacilityRow['default_facility'];

        if ($defaultFacilityID) {
            echo "Using default FacilityID: $defaultFacilityID\n";

            $fixFacilityQuery = "UPDATE shipping_containers sc
                                JOIN shipping s ON sc.ShippingID = s.ShippingID
                                SET sc.FacilityID = $defaultFacilityID
                                WHERE s.ShipmentStatusID = 1
                                  AND sc.FacilityID IS NOT NULL
                                  AND sc.FacilityID != 0
                                  AND NOT EXISTS (
                                      SELECT 1 FROM facility f WHERE f.FacilityID = sc.FacilityID
                                  )";

            if (mysqli_query($connectionlink, $fixFacilityQuery)) {
                echo "✓ Fixed $invalidFacilityCount invalid FacilityIDs\n";
            } else {
                die("✗ Error fixing invalid FacilityIDs: " . mysqli_error($connectionlink) . "\n");
            }
        } else {
            die("✗ No valid facilities found in facility table\n");
        }
    } else {
        echo "✓ All FacilityIDs are valid\n";
    }

    // Check for invalid LocationIDs (ACTIVE SHIPMENTS ONLY)
    echo "Checking for invalid LocationIDs in active shipments...\n";
    $invalidLocationResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as invalid_count
        FROM shipping_containers sc
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND sc.LocationID IS NOT NULL
          AND sc.LocationID != 0
          AND NOT EXISTS (
              SELECT 1 FROM location l WHERE l.LocationID = sc.LocationID
          )
    ");
    $invalidLocationRow = mysqli_fetch_assoc($invalidLocationResult);
    $invalidLocationCount = $invalidLocationRow['invalid_count'];

    if ($invalidLocationCount > 0) {
        echo "⚠ Found $invalidLocationCount containers with invalid LocationIDs\n";
        echo "Setting invalid LocationIDs to NULL...\n";

        $fixLocationQuery = "UPDATE shipping_containers sc
                            JOIN shipping s ON sc.ShippingID = s.ShippingID
                            SET sc.LocationID = NULL
                            WHERE s.ShipmentStatusID = 1
                              AND sc.LocationID IS NOT NULL
                              AND sc.LocationID != 0
                              AND NOT EXISTS (
                                  SELECT 1 FROM location l WHERE l.LocationID = sc.LocationID
                              )";

        if (mysqli_query($connectionlink, $fixLocationQuery)) {
            echo "✓ Fixed $invalidLocationCount invalid LocationIDs\n";
        } else {
            die("✗ Error fixing invalid LocationIDs: " . mysqli_error($connectionlink) . "\n");
        }
    } else {
        echo "✓ All LocationIDs are valid\n";
    }

    // Get total containers to convert (ACTIVE SHIPMENTS ONLY)
    $totalResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as total
        FROM shipping_containers sc
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND NOT EXISTS (
            SELECT 1 FROM custompallet cp
            WHERE cp.BinName = sc.ShippingContainerID
        )
    ");
    $totalRow = mysqli_fetch_assoc($totalResult);
    $totalContainers = $totalRow['total'];

    echo "Total active shipment containers to convert: $totalContainers\n";
    
    if ($totalContainers == 0) {
        echo "✓ No containers to convert\n";
        return;
    }
    
    $offset = 0;
    $converted = 0;
    
    while ($offset < $totalContainers) {
        echo "Processing batch: " . ($offset + 1) . " to " . min($offset + $batchSize, $totalContainers) . "\n";
        
        $sql = "INSERT INTO custompallet (
            FacilityID, LocationID, Description, MaximumAssets, AssetsCount,
            CreatedDate, CreatedBy, LastModifiedDate, LastModifiedBy,
            StatusID, StatusModifiedDate, StatusModifiedBy,
            disposition_id, BinName, SealID, ShippingControllerLoginID,
            ContainerWeight, RecentSealDate, RecentSealBy,
            ReferenceID, ReferenceTypeID, ReferenceType, ReferenceIDRequired,
            idPackage, ShippingID, bin_added_to_shipment_time,
            ASNContainer, idPallet, BatchRecovery, ConvertedFromShippingContainer
        )
        SELECT
            CASE
                WHEN sc.FacilityID IS NOT NULL AND sc.FacilityID != 0
                     AND EXISTS (SELECT 1 FROM facility f WHERE f.FacilityID = sc.FacilityID)
                THEN sc.FacilityID
                ELSE (SELECT MIN(FacilityID) FROM facility WHERE FacilityID > 0 LIMIT 1)
            END as FacilityID,
            CASE
                WHEN sc.LocationID IS NOT NULL AND sc.LocationID != 0
                     AND EXISTS (SELECT 1 FROM location l WHERE l.LocationID = sc.LocationID)
                THEN sc.LocationID
                ELSE NULL
            END as LocationID,
            CONCAT('Converted from Container: ', sc.ShippingContainerID, 
                   CASE WHEN sc.ContainerNotes IS NOT NULL THEN CONCAT(' - ', sc.ContainerNotes) ELSE '' END) as Description,
            NULL as MaximumAssets,
            (SELECT COUNT(*) FROM shipping_container_serials scs WHERE scs.ShippingContainerID = sc.ShippingContainerID) as AssetsCount,
            sc.CreatedDate, sc.CreatedBy, sc.UpdatedDate, sc.UpdatedBy,
            CASE 
                WHEN sc.StatusID = 1 AND sc.ShippingID IS NOT NULL THEN 6
                WHEN sc.StatusID = 1 THEN 1
                WHEN sc.StatusID = 2 THEN 1
                WHEN sc.StatusID = 3 THEN 7
                WHEN sc.StatusID = 4 THEN 1
                WHEN sc.StatusID = 5 THEN 2
                WHEN sc.StatusID = 6 THEN 3
                WHEN sc.StatusID = 7 THEN 4
                ELSE 1
            END as StatusID,
            sc.UpdatedDate as StatusModifiedDate, sc.UpdatedBy as StatusModifiedBy,
            CASE 
                WHEN sc.disposition_id IS NOT NULL AND EXISTS (SELECT 1 FROM disposition d WHERE d.disposition_id = sc.disposition_id) 
                THEN sc.disposition_id 
                ELSE NULL 
            END as disposition_id,
            sc.ShippingContainerID as BinName,
            sc.SealID, sc.ShippingControllerLoginID, sc.ContainerWeight,
            sc.RecentSealDate, sc.RecentSealBy, sc.ReferenceID,
            sc.ReferenceTypeID, sc.ReferenceType, sc.ReferenceIDRequired,
            sc.idPackage, sc.ShippingID, sc.container_added_to_shipment_time as bin_added_to_shipment_time,
            sc.ASNContainer, sc.idPallet, sc.BatchRecovery,
            1 as ConvertedFromShippingContainer
        FROM shipping_containers sc
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND NOT EXISTS (
            SELECT 1 FROM custompallet cp
            WHERE cp.BinName = sc.ShippingContainerID
        )
        LIMIT $batchSize";
        
        if (mysqli_query($connectionlink, $sql)) {
            $batchConverted = mysqli_affected_rows($connectionlink);
            $converted += $batchConverted;
            echo "✓ Converted $batchConverted containers (Total: $converted)\n";

            if ($batchConverted == 0) {
                break; // No more records to process
            }
        } else {
            die("✗ Error converting containers: " . mysqli_error($connectionlink) . "\n");
        }

        $offset += $batchSize;
        // usleep(100000); // 0.1 second delay - removed for faster processing
    }

    // Log conversions (ACTIVE SHIPMENTS ONLY)
    mysqli_query($connectionlink, "
        INSERT IGNORE INTO container_conversion_log (ShippingContainerID, NewCustomPalletID)
        SELECT sc.ShippingContainerID, cp.CustomPalletID
        FROM shipping_containers sc
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        JOIN custompallet cp ON cp.BinName = sc.ShippingContainerID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
    ");
    
    echo "✓ Total containers converted: $converted\n";
}

function createCustomPalletItems($connectionlink, $systemUserId, $batchSize) {
    echo "\n=== STEP 3: Creating CustomPallet Items (ACTIVE SHIPMENTS ONLY) ===\n";

    // Get total items to create (ACTIVE SHIPMENTS ONLY)
    $totalResult = mysqli_query($connectionlink, "
        SELECT COUNT(DISTINCT scs.SerialID) as total
        FROM shipping_container_serials scs
        JOIN custompallet cp ON cp.BinName = scs.ShippingContainerID
        JOIN shipping_containers sc ON sc.ShippingContainerID = scs.ShippingContainerID
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
          AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)
    ");
    $totalRow = mysqli_fetch_assoc($totalResult);
    $totalItems = $totalRow['total'];
    
    echo "Total items to create: $totalItems\n";
    
    if ($totalItems == 0) {
        echo "✓ No items to create\n";
        return;
    }
    
    $created = 0;
    
    while ($created < $totalItems) {
        echo "Creating items batch: " . ($created + 1) . " to " . min($created + $batchSize, $totalItems) . "\n";
        
        $sql = "INSERT IGNORE INTO custompallet_items (
            CustomPalletID, AssetScanID, DateCreated, CreatedBy, status, ServerID, MediaID, Quantity
        )
        SELECT DISTINCT
            cp.CustomPalletID, scs.AssetScanID, scs.CreatedDate as DateCreated,
            scs.CreatedBy, scs.StatusID as status, scs.ServerID, scs.MediaID, scs.Quantity
        FROM shipping_container_serials scs
        JOIN custompallet cp ON cp.BinName = scs.ShippingContainerID
        JOIN shipping_containers sc ON sc.ShippingContainerID = scs.ShippingContainerID
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1
          AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)
        LIMIT $batchSize";
        
        if (mysqli_query($connectionlink, $sql)) {
            $batchCreated = mysqli_affected_rows($connectionlink);
            $created += $batchCreated;
            echo "✓ Created $batchCreated items (Total: $created)\n";

            if ($batchCreated == 0) {
                break; // No more records to process
            }
        } else {
            die("✗ Error creating items: " . mysqli_error($connectionlink) . "\n");
        }
        
        // usleep(100000); // 0.1 second delay - removed for faster processing
    }
    
    echo "✓ Total items created: $created\n";
}

function updateShippingContainerSerials($connectionlink, $systemUserId, $batchSize) {
    echo "\n=== STEP 4: Updating Shipping Container Serials ===\n";

    // Get total serials to update (ACTIVE SHIPMENTS ONLY)
    $totalResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as total
        FROM shipping_container_serials scs
        JOIN custompallet cp ON cp.BinName = scs.ShippingContainerID
        JOIN shipping_containers sc ON sc.ShippingContainerID = scs.ShippingContainerID
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND scs.ShippingContainerID IS NOT NULL
          AND scs.CustomPalletID IS NULL
          AND cp.ConvertedFromShippingContainer = 1
    ");
    $totalRow = mysqli_fetch_assoc($totalResult);
    $totalSerials = $totalRow['total'];

    echo "Total serials to update: $totalSerials\n";

    if ($totalSerials == 0) {
        echo "✓ No serials to update\n";
        return;
    }

    $updated = 0;

    // Use ID-based batching for reliable processing
    $minIdResult = mysqli_query($connectionlink, "
        SELECT MIN(scs.SerialID) as min_id
        FROM shipping_container_serials scs
        WHERE scs.CustomPalletID IS NULL AND scs.ShippingContainerID IS NOT NULL
    ");
    $minIdRow = mysqli_fetch_assoc($minIdResult);
    $currentId = $minIdRow['min_id'] ? $minIdRow['min_id'] : 0;

    while ($updated < $totalSerials) {
        $endId = $currentId + $batchSize - 1;
        echo "Updating serials with IDs: $currentId to $endId\n";

        $sql = "UPDATE shipping_container_serials scs
                JOIN custompallet cp ON cp.BinName = scs.ShippingContainerID
                JOIN shipping_containers sc ON sc.ShippingContainerID = scs.ShippingContainerID
                JOIN shipping s ON sc.ShippingID = s.ShippingID
                SET
                    scs.CustomPalletID = cp.CustomPalletID,
                    scs.BinName = cp.BinName,
                    scs.UpdatedDate = NOW(),
                    scs.UpdatedBy = " . ($systemUserId ? $systemUserId : 'NULL') . "
                WHERE s.ShipmentStatusID = 1
                  AND scs.ShippingContainerID IS NOT NULL
                  AND scs.CustomPalletID IS NULL
                  AND scs.SerialID BETWEEN $currentId AND $endId
                  AND cp.ConvertedFromShippingContainer = 1";

        if (mysqli_query($connectionlink, $sql)) {
            $batchUpdated = mysqli_affected_rows($connectionlink);
            $updated += $batchUpdated;
            echo "✓ Updated $batchUpdated serials (Total: $updated)\n";

            if ($batchUpdated == 0) {
                // Find next batch starting point
                $nextIdResult = mysqli_query($connectionlink, "
                    SELECT MIN(scs.SerialID) as next_id
                    FROM shipping_container_serials scs
                    WHERE scs.CustomPalletID IS NULL
                      AND scs.ShippingContainerID IS NOT NULL
                      AND scs.SerialID > $endId
                ");
                $nextIdRow = mysqli_fetch_assoc($nextIdResult);
                if ($nextIdRow['next_id']) {
                    $currentId = $nextIdRow['next_id'];
                } else {
                    break; // No more records
                }
            } else {
                $currentId = $endId + 1;
            }
        } else {
            die("✗ Error updating serials: " . mysqli_error($connectionlink) . "\n");
        }

        // usleep(100000); // 0.1 second delay - removed for faster processing
    }

    echo "✓ Total serials updated: $updated\n";
}

function updateAssetCounts($connectionlink) {
    echo "\n=== STEP 5: Updating Asset Counts ===\n";

    $sql = "UPDATE custompallet cp
            SET AssetsCount = (
                SELECT COUNT(*)
                FROM custompallet_items cpi
                WHERE cpi.CustomPalletID = cp.CustomPalletID
            )
            WHERE cp.ConvertedFromShippingContainer = 1";

    if (mysqli_query($connectionlink, $sql)) {
        echo "✓ Updated asset counts for " . mysqli_affected_rows($connectionlink) . " bins\n";
    } else {
        die("✗ Error updating asset counts: " . mysqli_error($connectionlink) . "\n");
    }
}

function setConversionFlags($connectionlink, $systemUserId) {
    echo "\n=== STEP 6: Setting Conversion Flags ===\n";

    $sql = "UPDATE custompallet
            SET ConvertedFromShippingContainer = 1,
                LastModifiedDate = NOW(),
                LastModifiedBy = " . ($systemUserId ? $systemUserId : 'NULL') . "
            WHERE BinName COLLATE utf8mb3_unicode_ci IN (
                SELECT ShippingContainerID COLLATE utf8mb3_unicode_ci FROM shipping_containers
            )
            AND (ConvertedFromShippingContainer IS NULL OR ConvertedFromShippingContainer = 0)";

    if (mysqli_query($connectionlink, $sql)) {
        echo "✓ Set conversion flags for " . mysqli_affected_rows($connectionlink) . " bins\n";
    } else {
        die("✗ Error setting conversion flags: " . mysqli_error($connectionlink) . "\n");
    }
}

function updateLocationTable($connectionlink) {
    echo "\n=== STEP 7: Updating Location Table ===\n";

    $sql = "UPDATE location l
            JOIN custompallet cp ON l.LocationID = cp.LocationID
            SET l.currentItemType = 'BIN',
                l.currentItemID = cp.BinName,
                l.Locked = '1'
            WHERE cp.ConvertedFromShippingContainer = 1
              AND cp.LocationID IS NOT NULL
              AND (l.currentItemType != 'BIN' OR l.currentItemID != cp.BinName)";

    if (mysqli_query($connectionlink, $sql)) {
        echo "✓ Updated " . mysqli_affected_rows($connectionlink) . " location records\n";
    } else {
        die("✗ Error updating locations: " . mysqli_error($connectionlink) . "\n");
    }
}

function addTrackingRecords($connectionlink, $systemUserId) {
    echo "\n=== STEP 8: Adding Tracking Records ===\n";

    // Check if tracking records already exist
    $existingResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as existing
        FROM custompallet_tracking ct
        JOIN custompallet cp ON ct.CustomPalletID = cp.CustomPalletID
        WHERE cp.ConvertedFromShippingContainer = 1
          AND ct.ModuleName = 'Container to Bin Conversion'
    ");
    $existingRow = mysqli_fetch_assoc($existingResult);
    $existingTracking = $existingRow['existing'];

    if ($existingTracking > 0) {
        echo "✓ Tracking records already exist ($existingTracking records)\n";
        return;
    }

    $sql = "INSERT INTO custompallet_tracking (
                CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName
            )
            SELECT
                cp.CustomPalletID,
                cp.BinName,
                CONCAT('Container converted to bin from shipping_containers table. Original Container ID: ',
                       cp.BinName,
                       '. Conversion completed on ',
                       DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')) as Action,
                NOW() as CreatedDate,
                " . ($systemUserId ? $systemUserId : 'NULL') . " as CreatedBy,
                'Container to Bin Conversion' as ModuleName
            FROM custompallet cp
            WHERE cp.ConvertedFromShippingContainer = 1
              AND NOT EXISTS (
                  SELECT 1 FROM custompallet_tracking ct
                  WHERE ct.CustomPalletID = cp.CustomPalletID
                    AND ct.ModuleName = 'Container to Bin Conversion'
              )";

    if (mysqli_query($connectionlink, $sql)) {
        echo "✓ Added " . mysqli_affected_rows($connectionlink) . " tracking records\n";
    } else {
        die("✗ Error adding tracking records: " . mysqli_error($connectionlink) . "\n");
    }
}

function generateFinalReport($connectionlink) {
    echo "\n=== STEP 9: Final Conversion Report (ACTIVE SHIPMENTS ONLY) ===\n";

    // Conversion summary
    $summaryResult = mysqli_query($connectionlink, "
        SELECT
            COUNT(*) as Total_Containers_Converted,
            SUM(cp.AssetsCount) as Total_Items_Migrated,
            MIN(cp.CreatedDate) as First_Container_Date,
            MAX(cp.CreatedDate) as Last_Container_Date
        FROM custompallet cp
        WHERE cp.ConvertedFromShippingContainer = 1
    ");
    $summary = mysqli_fetch_assoc($summaryResult);

    echo "CONVERSION SUMMARY:\n";
    echo "- Total Containers Converted: " . $summary['Total_Containers_Converted'] . "\n";
    echo "- Total Items Migrated: " . $summary['Total_Items_Migrated'] . "\n";
    echo "- First Container Date: " . $summary['First_Container_Date'] . "\n";
    echo "- Last Container Date: " . $summary['Last_Container_Date'] . "\n\n";

    // Validation checks (ACTIVE SHIPMENTS ONLY)
    $containerCheck = mysqli_query($connectionlink, "
        SELECT
            (SELECT COUNT(*) FROM shipping_containers sc
             JOIN shipping s ON sc.ShippingID = s.ShippingID
             WHERE s.ShipmentStatusID = 1) as Active_Containers,
            (SELECT COUNT(*) FROM custompallet cp
             JOIN shipping_containers sc ON cp.BinName = sc.ShippingContainerID
             JOIN shipping s ON sc.ShippingID = s.ShippingID
             WHERE cp.ConvertedFromShippingContainer = 1 AND s.ShipmentStatusID = 1) as Converted_Active_Bins
    ");
    $containerRow = mysqli_fetch_assoc($containerCheck);

    echo "VALIDATION CHECKS (ACTIVE SHIPMENTS):\n";
    echo "- Active Shipment Containers: " . $containerRow['Active_Containers'] . "\n";
    echo "- Converted Active Bins: " . $containerRow['Converted_Active_Bins'] . "\n";
    echo "- Active Container Conversion: " . ($containerRow['Active_Containers'] == $containerRow['Converted_Active_Bins'] ? 'PASS' : 'REVIEW_NEEDED') . "\n";

    $serialCheck = mysqli_query($connectionlink, "
        SELECT COUNT(*) as Updated_Serials
        FROM shipping_container_serials
        WHERE CustomPalletID IS NOT NULL
    ");
    $serialRow = mysqli_fetch_assoc($serialCheck);

    echo "- Updated Serials: " . $serialRow['Updated_Serials'] . "\n";

    $locationCheck = mysqli_query($connectionlink, "
        SELECT COUNT(*) as Updated_Locations
        FROM location l
        JOIN custompallet cp ON l.LocationID = cp.LocationID
        WHERE cp.ConvertedFromShippingContainer = 1
          AND l.currentItemType = 'BIN'
    ");
    $locationRow = mysqli_fetch_assoc($locationCheck);

    echo "- Updated Locations: " . $locationRow['Updated_Locations'] . "\n";

    echo "\n✓ Conversion completed successfully!\n";
}

?>
