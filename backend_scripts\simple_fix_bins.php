<?php
/**
 * Simple Fix Bins Script - Container by Container Processing
 * This script processes one container at a time with progress feedback
 */

// Database connection
session_start();
include_once("../connection.php");
$obj1 = new Connection();
$connectionlink = Connection::DBConnect();

$systemUserId = 712; // System user ID for tracking

echo "=== SIMPLE FIX BINS SCRIPT ===\n";
echo "Processing one container at a time with progress feedback\n\n";

// Get all converted containers from active shipments
function getConvertedContainers($connectionlink) {
    echo "Getting list of converted containers from active shipments...\n";
    
    $sql = "SELECT 
                cp.CustomPalletID,
                cp.BinName,
                sc.ShippingContainerID,
                s.ShippingID,
                cp.AssetsCount as Current_AssetsCount
            FROM custompallet cp
            JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
            JOIN shipping s ON sc.ShippingID = s.ShippingID
            WHERE s.ShipmentStatusID = 1
              AND cp.ConvertedFromShippingContainer = 1
            ORDER BY cp.CustomPalletID";
    
    $result = mysqli_query($connectionlink, $sql);
    
    if (!$result) {
        die("Error getting containers: " . mysqli_error($connectionlink) . "\n");
    }
    
    $containers = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $containers[] = $row;
    }
    
    echo "Found " . count($containers) . " converted containers to process\n\n";
    return $containers;
}

// Process a single container
function processSingleContainer($connectionlink, $container, $systemUserId, $containerIndex, $totalContainers) {
    $customPalletID = $container['CustomPalletID'];
    $binName = $container['BinName'];
    $shippingContainerID = $container['ShippingContainerID'];
    
    echo "[$containerIndex/$totalContainers] Processing: $binName (ID: $customPalletID)";
    
    // Step 1: Count existing custompallet_items
    $existingItemsResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as existing_count 
        FROM custompallet_items 
        WHERE CustomPalletID = '$customPalletID'
    ");
    $existingItemsRow = mysqli_fetch_assoc($existingItemsResult);
    $existingItemsCount = $existingItemsRow['existing_count'];
    
    // Step 2: Count shipping_container_serials that should become items
    $serialsResult = mysqli_query($connectionlink, "
        SELECT COUNT(*) as serials_count 
        FROM shipping_container_serials 
        WHERE ShippingContainerID COLLATE utf8mb3_unicode_ci = '$shippingContainerID' COLLATE utf8mb3_unicode_ci
          AND CustomPalletID = '$customPalletID'
          AND (AssetScanID IS NOT NULL OR ServerID IS NOT NULL OR MediaID IS NOT NULL)
    ");
    $serialsRow = mysqli_fetch_assoc($serialsResult);
    $serialsCount = $serialsRow['serials_count'];
    
    // Step 3: Create missing custompallet_items if needed
    $itemsCreated = 0;
    if ($existingItemsCount < $serialsCount) {
        $createItemsSQL = "INSERT IGNORE INTO custompallet_items (
            CustomPalletID, AssetScanID, DateCreated, CreatedBy, status, ServerID, MediaID, Quantity
        )
        SELECT DISTINCT
            '$customPalletID' as CustomPalletID,
            scs.AssetScanID, 
            COALESCE(scs.CreatedDate, NOW()) as DateCreated,
            COALESCE(scs.CreatedBy, $systemUserId) as CreatedBy, 
            COALESCE(scs.StatusID, 8) as status, 
            scs.ServerID, 
            scs.MediaID, 
            COALESCE(scs.Quantity, 1) as Quantity
        FROM shipping_container_serials scs
        WHERE scs.ShippingContainerID COLLATE utf8mb3_unicode_ci = '$shippingContainerID' COLLATE utf8mb3_unicode_ci
          AND scs.CustomPalletID = '$customPalletID'
          AND (scs.AssetScanID IS NOT NULL OR scs.ServerID IS NOT NULL OR scs.MediaID IS NOT NULL)
          AND NOT EXISTS (
              SELECT 1 FROM custompallet_items cpi 
              WHERE cpi.CustomPalletID = '$customPalletID'
                AND (
                    (scs.AssetScanID IS NOT NULL AND cpi.AssetScanID = scs.AssetScanID) OR
                    (scs.ServerID IS NOT NULL AND cpi.ServerID = scs.ServerID) OR
                    (scs.MediaID IS NOT NULL AND cpi.MediaID = scs.MediaID)
                )
          )";
        
        if (mysqli_query($connectionlink, $createItemsSQL)) {
            $itemsCreated = mysqli_affected_rows($connectionlink);
        } else {
            echo " - ERROR creating items: " . mysqli_error($connectionlink);
        }
    }
    
    // Step 4: Update AssetsCount
    $newAssetsCount = $existingItemsCount + $itemsCreated;
    $updateAssetsSQL = "UPDATE custompallet 
                        SET AssetsCount = '$newAssetsCount',
                            LastModifiedDate = NOW(),
                            LastModifiedBy = '$systemUserId'
                        WHERE CustomPalletID = '$customPalletID'";
    
    if (mysqli_query($connectionlink, $updateAssetsSQL)) {
        echo " - Assets: $newAssetsCount";
        if ($itemsCreated > 0) {
            echo " (Created: $itemsCreated items)";
        }
        echo " ✓\n";
        return true;
    } else {
        echo " - ERROR updating AssetsCount: " . mysqli_error($connectionlink) . "\n";
        return false;
    }
}

// Main execution
try {
    $startTime = time();
    
    // Get all containers to process
    $containers = getConvertedContainers($connectionlink);
    
    if (empty($containers)) {
        echo "No containers found to process.\n";
        exit(0);
    }
    
    $totalContainers = count($containers);
    $processed = 0;
    $errors = 0;
    
    echo "Starting container-by-container processing...\n";
    echo "Progress will be shown for each container.\n\n";
    
    // Process each container
    foreach ($containers as $index => $container) {
        $containerIndex = $index + 1;
        
        if (processSingleContainer($connectionlink, $container, $systemUserId, $containerIndex, $totalContainers)) {
            $processed++;
        } else {
            $errors++;
        }
        
        // Show progress every 50 containers
        if ($containerIndex % 50 == 0) {
            $elapsed = time() - $startTime;
            $rate = $containerIndex / $elapsed;
            $remaining = ($totalContainers - $containerIndex) / $rate;
            echo "\n--- PROGRESS UPDATE ---\n";
            echo "Processed: $containerIndex/$totalContainers containers\n";
            echo "Success: $processed, Errors: $errors\n";
            echo "Rate: " . round($rate, 2) . " containers/second\n";
            echo "Estimated time remaining: " . round($remaining/60, 1) . " minutes\n\n";
        }
        
        // Small delay to prevent overwhelming the database
        usleep(10000); // 0.01 second delay
    }
    
    // Final summary
    $totalTime = time() - $startTime;
    echo "\n=== FINAL SUMMARY ===\n";
    echo "Total containers processed: $processed\n";
    echo "Errors encountered: $errors\n";
    echo "Total time: " . round($totalTime/60, 2) . " minutes\n";
    echo "Average rate: " . round($processed/$totalTime, 2) . " containers/second\n";
    
    // Quick validation
    echo "\n=== QUICK VALIDATION ===\n";
    $validationSQL = "SELECT 
        COUNT(*) as total_bins,
        SUM(AssetsCount) as total_assets,
        AVG(AssetsCount) as avg_assets
        FROM custompallet cp
        JOIN shipping_containers sc ON cp.BinName COLLATE utf8mb3_unicode_ci = sc.ShippingContainerID COLLATE utf8mb3_unicode_ci
        JOIN shipping s ON sc.ShippingID = s.ShippingID
        WHERE s.ShipmentStatusID = 1
          AND cp.ConvertedFromShippingContainer = 1";
    
    $validationResult = mysqli_query($connectionlink, $validationSQL);
    if ($validationRow = mysqli_fetch_assoc($validationResult)) {
        echo "Total bins: " . $validationRow['total_bins'] . "\n";
        echo "Total assets: " . $validationRow['total_assets'] . "\n";
        echo "Average assets per bin: " . round($validationRow['avg_assets'], 2) . "\n";
    }
    
    echo "\n✓ Container-by-container processing completed!\n";
    
} catch (Exception $e) {
    echo "\n✗ Error during processing: " . $e->getMessage() . "\n";
    exit(1);
}

?>
