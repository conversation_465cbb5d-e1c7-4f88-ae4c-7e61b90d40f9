<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'Consolidations.'.$today.'.csv';

$csv = "entity_id,consolidation_type,inventory_type,serial_id,mpn_id,part_type,manufacturer_id,source_type,origin_bin_id,origin_container_id,consolidation_datetime,bin_id,container_id,next_step_action,operator_login_id,consolidation_location_id,batch_event_flag,coo_id,classification_type,classification_code_id,event_id,customer_id,event_s_duration_value\n";//Column headers
$sql = "select distinct(BCAM.MovementID) as MovementID, BCAM.SerialNumber as serial_id,BCAM.UniversalModelNumber as mpn_id,BCAM.part_type,BCAMM.ManufacturerName as manufacturer_id,
SCT.Cumstomertype as source_type,BCAM.FromBinName as origin_bin_id,BCAM.FromShippingContainerID as origin_container_id,BCAM.CreatedDate,BCAM.ToBinName as bin_id,
BCAM.ToShippingContainerID as container_id,BCAMD.disposition as next_step_action,BCAMU.UserName as operator_login_id,BCAMF.FacilityName as consolidation_location_id,
BCAM.MovementType as consolidation_type,ACOO.COO as coo_id,BCAMD.WasteClassificationType as classification_type,BCAMA.WasteCode as classification_code_id,
BCAM.event_id,AWSSC.Customer as customer_id,BCAM.batch_event_flag

        FROM bin_consolidation_asset_movement BCAM
        LEFT JOIN manufacturer BCAMM ON BCAMM.idManufacturer = BCAM.idManufacturer
        LEFT JOIN pallets BCAMP on BCAMP.idPallet = BCAM.idPallet
        LEFT JOIN customer SC on SC.CustomerID = BCAMP.idCustomer
        LEFT JOIN customertype SCT on SCT.idCustomertype = BCAMP.idCustomertype
        LEFT JOIN asset BCAMA on BCAMA.SerialNumber = BCAM.SerialNumber
        LEFT JOIN disposition BCAMD on BCAMD.disposition_id = BCAM.disposition_id
        LEFT JOIN users BCAMU on BCAMU.UserId = BCAM.CreatedBy
        LEFT JOIN facility BCAMF on BCAMF.FacilityID = BCAM.FacilityID
        LEFT JOIN COO ACOO ON ACOO.COOID = BCAMA.COOID
        LEFT JOIN aws_customers AWSSC on AWSSC.AWSCustomerID = SC.AWSCustomerID
        WHERE BCAM.CreatedDate Between '".$ProcessDatefrom."' and '".$ProcessDateto."'
        group by BCAM.MovementID";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
    /*if($row['FacilityName'] == 'CVG110')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['InspectionAuditDateTime'] = date('Y-m-d H:i:s', strtotime($row['InspectionAuditDateTime'] . ' + 16 hour'));
	}*/
    $timeFirst  = strtotime($row['UpdatedDate']);
        $timeSecond = strtotime($row['CreatedDate']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    if($row['consolidation_type'] == 'BULK Aset Movement')
    {
        $row['batch_event_flag'] = 'Y';
    }
    else
    {
        $row['batch_event_flag'] = 'N';
    }
    if($row['serial_id'] != '')
    {
        $row['inventory_type'] = 'Serialized';
    }
    else
    {
        $row['inventory_type'] = 'Unserialized';
    }
    if($row['consolidation_type'] == 'Container To Container Consolidation')
    {
        $row['consolidation_type'] = 'Container-to-Container';
        $row['origin_bin_id'] = '';
        $row['bin_id'] = '';
    }
    else if($row['consolidation_type'] == 'Bin To Container Consolidation')
    {
        $row['consolidation_type'] = 'Bin-to-Container';
        $row['origin_container_id'] = '';
        $row['bin_id'] = '';
    }
    else if($row['consolidation_type'] == 'Bin Consolidation')
    {
        $row['consolidation_type'] = 'Bin-to-Bin';
        $row['origin_container_id'] = '';
        $row['container_id'] = '';
    }
    if($row['CreatedDate'] != '')
    {
        $row['CreatedDate'] = date("Y-m-d H:i:s", strtotime($row['CreatedDate']));
    }
    else
    {
        $row['CreatedDate'] = '';
    }
    $date1 = explode(" ",$row['CreatedDate']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("g:i:s A", strtotime($row['CreatedDate']));
    $row['operator_login_id'] = strtolower($row['operator_login_id']);
    $row['consolidation_type'] = str_replace(","," ",$row['consolidation_type']);
	$row['consolidation_type'] = str_replace(","," ",$row['consolidation_type']);
	$row['inventory_type'] = str_replace(","," ",$row['inventory_type']);
    $row['serial_id'] = str_replace(","," ",$row['serial_id']);
    $row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
    $row['part_type'] = str_replace(","," ",$row['part_type']);
    $row['manufacturer_id'] = str_replace(","," ",$row['manufacturer_id']);
    $row['source_type'] = str_replace(","," ",$row['source_type']);
    $row['origin_bin_id'] = str_replace(","," ",$row['origin_bin_id']);
    $row['origin_container_id'] = str_replace(","," ",$row['origin_container_id']);
    $row['bin_id'] = str_replace(","," ",$row['bin_id']);
    $row['container_id '] = str_replace(","," ",$row['container_id ']);
    $row['next_step_action'] = str_replace(","," ",$row['next_step_action']);
    $row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
    $row['consolidation_location_id'] = str_replace(","," ",$row['consolidation_location_id']);
    $row['batch_event_flag'] = str_replace(","," ",$row['batch_event_flag']);
    $row['coo_id'] = str_replace(","," ",$row['coo_id']);
    $row['classification_type'] = str_replace(","," ",$row['classification_type']);
    $row['classification_code_id'] = str_replace(","," ",$row['classification_code_id']);
    $row['event_id'] = str_replace(","," ",$row['event_id']);
    $row['customer_id'] = str_replace(","," ",$row['customer_id']);
    
    if($row['entity_id'] == '')
    {
        $row['entity_id'] = 'n/a';
    }
    if($row['consolidation_type'] == '')
    {
        $row['consolidation_type'] = 'n/a';
    }
    if($row['inventory_type'] == '')
    {
        $row['inventory_type'] = 'n/a';
    }
    if($row['serial_id'] == '')
    {
        $row['serial_id'] = 'n/a';
    }
    if($row['mpn_id'] == '')
    {
        $row['mpn_id'] = 'n/a';
    }
    if($row['part_type'] == '')
    {
        $row['part_type'] = 'n/a';
    }
    if($row['manufacturer_id'] == '')
    {
        $row['manufacturer_id'] = 'n/a';
    }
    if($row['source_type'] == '')
    {
        $row['source_type'] = 'n/a';
    }
    if($row['origin_bin_id'] == '')
    {
        $row['origin_bin_id'] = 'n/a';
    }
    if($row['origin_container_id'] == '')
    {
        $row['origin_container_id'] = 'n/a';
    }
    if($row['bin_id'] == '')
    {
        $row['bin_id'] = 'n/a';
    }
    if($row['container_id'] == '')
    {
        $row['container_id'] = 'n/a';
    }
    if($row['next_step_action'] == '')
    {
        $row['next_step_action'] = 'n/a';
    }
    if($row['operator_login_id'] == '')
    {
        $row['operator_login_id'] = 'n/a';
    }
    if($row['consolidation_location_id'] == '')
    {
        $row['consolidation_location_id'] = 'n/a';
    }
    if($row['batch_event_flag'] == '')
    {
        $row['batch_event_flag'] = 'n/a';
    }
    if($row['coo_id'] == '')
    {
        $row['coo_id'] = 'n/a';
    }
    if($row['classification_type'] == '')
    {
        $row['classification_type'] = 'n/a';
    }
    if($row['classification_code_id'] == '')
    {
        $row['classification_code_id'] = 'n/a';
    }
    if($row['event_id'] == '')
    {
        $row['event_id'] = 'n/a';
    }
    if($row['customer_id'] == '')
    {
        $row['customer_id'] = 'n/a';
    }
    if($date == '')
    {
        $date = 'n/a';
    }
    if($time == '')
    {
        $time = 'n/a';
    }
    $row2  = array('eV-Disposition-1',$row['consolidation_type'],$row['inventory_type'],$row['serial_id'],$row['mpn_id'],$row['part_type'],$row['manufacturer_id'],$row['source_type'],$row['origin_bin_id'],$row['origin_container_id'],$row['CreatedDate'],$row['bin_id'],$row['container_id'],$row['next_step_action'],$row['operator_login_id'],$row['consolidation_location_id'],$row['batch_event_flag'],$row['coo_id'],$row['classification_type'],$row['classification_code_id'],$row['event_id'],$row['customer_id'],$differenceInSeconds);
    $rows[] = $row2;
}

foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>