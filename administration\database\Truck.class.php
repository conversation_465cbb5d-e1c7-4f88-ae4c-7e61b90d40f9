<?php
session_start();
include_once("admin.class.php");
include_once("../../common_functions.php");
include_once("../../excel_reader/SimpleXLSX.php");
include_once("../../administration/templates/xlsxwriter.class.php");
include_once("../../administration/templates/xlsxwriterplus.class.php");
use Aws\S3\S3Client;
use Aws\S3\Exception\S3Exception;

class TruckClass extends AdminClass
{
 	public function SaveParkTypeConfiguration($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Manufacturer Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Manufacturer Page';
			return json_encode($json);
		}*/
		//return json_encode($json);
		if ($data['ParkTypeID'] == '') { //If New Class
			$duplicate = $this->CheckDuplicate('New', 'ParkType', 'ParkTypeName', $data['ParkTypeName'], false, '', '');
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'ParkType Name already Exists';
				return json_encode($json);
			}
			$query = "insert into ParkType (FacilityID,ParkTypeName,Description,Status) values ('" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeName']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "')";
		} else {
			$duplicate = $this->CheckDuplicate('Edit', 'ParkType', 'ParkTypeName', $data['ParkTypeName'], false, 'ParkTypeID', $data['ParkTypeID']);
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'ParkType Name already Exists';
				return json_encode($json);
			}
			$query = "update ParkType set FacilityID='" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',ParkTypeName='" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeName']) . "',Description='" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "' where ParkTypeID='" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if ($data['ParkTypeID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New ParkType Created";
			$json['ParkTypeID'] = $insert_id;
		} else {
			$json['Success'] = true;
			$json['Result'] = "ParkType Modified";
		}
		return json_encode($json);
	}

	public function GetParkTypeConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Manufacturer Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Manufacturer Page';
			return json_encode($json);
		}*/

		//return json_encode($json);
		$query = "select * from ParkType where ParkTypeID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "' ";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid ParkType ID";
		}
		return json_encode($json);
	}

	public function GetParkTypeConfigurationList($data) {
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['SiteID']
			);
			/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Work Station')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Work Station Page';
				return json_encode($json);
			}*/
			//$query = "Select S.*,F.FacilityName,l.LockType from site S, facility F,locked_details l Where F.FacilityID = S.FacilityID AND S.Status = S.Status  and S.AccountID = '".$_SESSION['user']['AccountID']."' and S.Locked = l.Locked";
			$query = "Select PT.*,F.FacilityName,SS.StatusName from ParkType PT left join  facility F on F.FacilityID = PT.FacilityID	left join statusses SS on PT.Status = SS.StatusID Where 1 ";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'ParkTypeName') {
							$query = $query . " AND PT.ParkTypeName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Description') {
							$query = $query . " AND PT.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'FacilityName') {
							$query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'StatusName') {
							$query = $query . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'ParkTypeName') {
					$query = $query . " order by PT.ParkTypeName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Description') {
					$query = $query . " order by PT.Description " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$query = $query . " order by SS.StatusName " . $order_by_type . " ";
				}
			} else {
				$query = $query . " order by ParkTypeName asc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Park Type Available";
				return json_encode($json);
			}

			if ($data['skip'] == 0) {

				//$query1 = "select count(*) from site S, facility F where F.FacilityID = S.FacilityID  AND S.AccountID = '".$_SESSION['user']['AccountID']."'";
				/*$query1 = "Select count(*) from site S, facility F,locked_details l Where F.FacilityID = S.FacilityID AND S.Status = S.Status  and S.AccountID = '" . $_SESSION['user']['AccountID'] . "' and S.Locked = l.Locked";*/
				$query1 = "Select count(*) from ParkType PT left join  facility F on F.FacilityID = PT.FacilityID	left join statusses SS on PT.Status = SS.StatusID Where 1 ";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'ParkTypeName') {
								$query1 = $query1 . " AND PT.ParkTypeName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Description') {
								$query1 = $query1 . " AND PT.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'StatusName') {
								$query1 = $query1 . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


	public function SaveTruckTypeConfiguration($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Truck Type Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Truck Type Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Truck Type Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Truck Type Page';
			return json_encode($json);
		}
		//return json_encode($json);
		if ($data['TruckTypeID'] == '') { //If New Class
			$duplicate = $this->CheckDuplicate('New', 'TruckType', 'TruckTypeName', $data['TruckTypeName'], false, '', '');
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'TruckType Name already Exists';
				return json_encode($json);
			}
			$query = "insert into TruckType (FacilityID,TruckTypeName,Description,Status) values ('" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeName']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "')";
		} else {
			$duplicate = $this->CheckDuplicate('Edit', 'TruckType', 'TruckTypeName', $data['TruckTypeName'], false, 'TruckTypeID', $data['TruckTypeID']);
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'TruckType Name already Exists';
				return json_encode($json);
			}
			$query = "update TruckType set FacilityID='" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',TruckTypeName='" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeName']) . "',Description='" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "' where TruckTypeID='" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if ($data['TruckTypeID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New TruckType Created";
			$json['ParkTypeID'] = $insert_id;
		} else {
			$json['Success'] = true;
			$json['Result'] = "TruckType Modified";
		}
		return json_encode($json);
	}

	public function GetTruckTypeConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Truck Type Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to TruckType Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Truck Type Configuration')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to TruckType Page';
			return json_encode($json);
		}

		//return json_encode($json);
		$query = "select * from TruckType where TruckTypeID = '" . mysqli_real_escape_string($this->connectionlink, $data['TruckTypeID']) . "' ";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid TruckType ID";
		}
		return json_encode($json);
	}

	public function GetTruckTypeConfigurationList($data) {
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['SiteID']
			);
			if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Truck Type Configuration')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Truck Type Configuration Page';
				return json_encode($json);
			}
			$query = "Select T.*,F.FacilityName,SS.StatusName from TruckType T left join  facility F on F.FacilityID = T.FacilityID	left join statusses SS on T.Status = SS.StatusID Where 1 ";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'TruckTypeName') {
							$query = $query . " AND T.TruckTypeName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Description') {
							$query = $query . " AND T.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'FacilityName') {
							$query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'StatusName') {
							$query = $query . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'TruckTypeName') {
					$query = $query . " order by T.TruckTypeName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Description') {
					$query = $query . " order by T.Description " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$query = $query . " order by SS.StatusName " . $order_by_type . " ";
				}
			} else {
				$query = $query . " order by TruckTypeName asc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Truck Type Available";
				return json_encode($json);
			}

			if ($data['skip'] == 0) {

				$query1 = "Select count(*) from TruckType T left join  facility F on F.FacilityID = T.FacilityID left join statusses SS on T.Status = SS.StatusID Where 1 ";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'TruckTypeName') {
								$query1 = $query1 . " AND T.TruckTypeName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Description') {
								$query1 = $query1 . " AND T.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'StatusName') {
								$query1 = $query1 . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function SaveTDRSOPConfiguration($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'TDR SOP')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to TDR SOP Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'TDR SOP')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to TDR SOP Page';
			return json_encode($json);
		}
		//return json_encode($json);
		if ($data['TDRSOPID'] == '') { //If New Class
			/*$duplicate = $this->CheckDuplicate('New', 'TDRSOP', 'TruckTypeName', $data['TruckTypeName'], false, '', '');
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'TruckType Name already Exists';
				return json_encode($json);
			}*/
			$query = "insert into TDRSOP (TdrType,StepNo,Description,Status,VehicleType) values ('" . mysqli_real_escape_string($this->connectionlink, $data['TdrType']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['StepNo']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['VehicleType']) . "')";
		} else {
			/*$duplicate = $this->CheckDuplicate('Edit', 'TruckType', 'TruckTypeName', $data['TruckTypeName'], false, 'TruckTypeID', $data['TruckTypeID']);
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'TruckType Name already Exists';
				return json_encode($json);
			}*/
			$query = "update TDRSOP set TdrType='" . mysqli_real_escape_string($this->connectionlink, $data['TdrType']) . "',StepNo='" . mysqli_real_escape_string($this->connectionlink, $data['StepNo']) . "',Description='" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',VehicleType='" . mysqli_real_escape_string($this->connectionlink, $data['VehicleType']) . "' where TDRSOPID='" . mysqli_real_escape_string($this->connectionlink, $data['TDRSOPID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if ($data['TDRSOPID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New TDR SOP Created";
			$json['ParkTypeID'] = $insert_id;
		} else {
			$json['Success'] = true;
			$json['Result'] = "TDR SOP Modified";
		}
		return json_encode($json);
	}

	public function GetTDRSOPConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'TDR SOP')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to TDR SOP Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'TDR SOP')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to TDR SOP Page';
			return json_encode($json);
		}

		//return json_encode($json);
		$query = "select * from TDRSOP where TDRSOPID = '" . mysqli_real_escape_string($this->connectionlink, $data['TDRSOPID']) . "' ";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid TDRSOP ID";
		}
		return json_encode($json);
	}

	public function GetTDRSOPConfigurationList($data) {
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['SiteID']
			);
			if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'TDR SOP')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to TDR SOP Page';
				return json_encode($json);
			}
			$query = "Select T.*,SS.StatusName from TDRSOP T left join statusses SS on T.Status = SS.StatusID Where 1 ";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'TdrType') {
							$query = $query . " AND T.TdrType like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Description') {
							$query = $query . " AND T.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'StepNo') {
							$query = $query . " AND T.StepNo like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'VehicleType') {
							$query = $query . " AND T.VehicleType like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'StatusName') {
							$query = $query . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'TdrType') {
					$query = $query . " order by T.TdrType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Description') {
					$query = $query . " order by T.Description " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StepNo') {
					$query = $query . " order by T.StepNo " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'VehicleType') {
					$query = $query . " order by T.VehicleType " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$query = $query . " order by SS.StatusName " . $order_by_type . " ";
				}
			} else {
				$query = $query . " order by TdrType asc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No TDRSOP Available";
				return json_encode($json);
			}

			if ($data['skip'] == 0) {

				$query1 = "Select count(*) from TDRSOP T left join statusses SS on T.Status = SS.StatusID Where 1 ";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'TdrType') {
								$query1 = $query1 . " AND T.TdrType like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Description') {
								$query1 = $query1 . " AND T.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'StepNo') {
								$query1 = $query1 . " AND T.StepNo like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'VehicleType') {
								$query1 = $query1 . " AND T.VehicleType like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'StatusName') {
								$query1 = $query1 . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function SaveParkingLocationConfiguration($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Parking Location')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to ParkingLocation Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Parking Location')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to ParkingLocation Page';
			return json_encode($json);
		}
		//return json_encode($json);
		if ($data['ParkingLocationID'] == '') { //If New Class
			/*$duplicate = $this->CheckDuplicate('New', 'ParkingLocation', 'ParkingLocationName', $data['ParkingLocationName'], false, '', '');
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'Parking Location Name already Exists';
				return json_encode($json);
			}*/

			//Start checking for Carrier duplicate
			$query1 = "select count(*) from ParkingLocation where ParkingLocationName = '".mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationName'])."' and FacilityID = '".mysqli_real_escape_string($this->connectionlink, $data['FacilityID'])."' ";
			$q1 = mysqli_query($this->connectionlink, $query1);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Parking Location Name already Exists';
					return json_encode($json);
				}
			}
			//End checking for Carrier duplicate
			$query = "insert into ParkingLocation (FacilityID,ParkingLocationName,Description,Status,ParkTypeID) values ('" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationName']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "','" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "')";
		} else {
			/*$duplicate = $this->CheckDuplicate('Edit', 'ParkingLocation', 'ParkingLocationName', $data['ParkingLocationName'], false, 'ParkingLocationID', $data['ParkingLocationID']);
			if ($duplicate) {
				$json['Success'] = false;
				$json['Result'] = 'Parking Location Name already Exists';
				return json_encode($json);
			}*/
			//Start check If Carrier exists for this Facility
			$query1 = "select count(*) from ParkingLocation where ParkingLocationName = '".mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationName'])."' and FacilityID = '".mysqli_real_escape_string($this->connectionlink, $data['FacilityID'])."' and ParkingLocationID != '" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "'";
			$q1 = mysqli_query($this->connectionlink, $query1);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);
				if($row1['count(*)'] > 0) {
					$json['Success'] = false;
					$json['Result'] = 'Parking Location Name already exists for the Facility';
					return json_encode($json);
				}
			}
			//End check IF Carrier exists for this facility
			$query = "update ParkingLocation set FacilityID='" . mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) . "',ParkingLocationName='" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationName']) . "',Description='" . mysqli_real_escape_string($this->connectionlink, $data['Description']) . "',Status='" . mysqli_real_escape_string($this->connectionlink, $data['Status']) . "',ParkTypeID='" . mysqli_real_escape_string($this->connectionlink, $data['ParkTypeID']) . "' where ParkingLocationID='" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "'";
		}
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if ($data['ParkingLocationID'] == '') {
			$insert_id = mysqli_insert_id($this->connectionlink);
			$json['Success'] = true;
			$json['Result'] = "New Parking Location Created";
			$json['ParkTypeID'] = $insert_id;
		} else {
			$json['Success'] = true;
			$json['Result'] = "Parking Location Modified";
		}
		return json_encode($json);
	}

	public function GetParkingLocationConfigurationDetails($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);

		/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Manufacturer Page';
			return json_encode($json);
		}
		if (!$this->isWritePermitted($_SESSION['user']['ProfileID'], 'Manufacturer')) {
			$json['Success'] = false;
			$json['Result'] = 'You have Read only Access to Manufacturer Page';
			return json_encode($json);
		}*/

		//return json_encode($json);
		$query = "select * from ParkingLocation where ParkingLocationID = '" . mysqli_real_escape_string($this->connectionlink, $data['ParkingLocationID']) . "' ";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$row = mysqli_fetch_assoc($q);
			$json['Success'] = true;
			$json['Result'] = $row;
		} else {
			$json['Success'] = false;
			$json['Result'] = "Invalid ParkingLocation ID";
		}
		return json_encode($json);
	}

	public function GetParkingLocationConfigurationList($data) {
		try {
			if (!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data['ParkingLocationID']
			);
			/*if (!$this->isPermitted($_SESSION['user']['ProfileID'], 'Work Station')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Work Station Page';
				return json_encode($json);
			}*/
			$query = "Select PL.*,F.FacilityName,SS.StatusName,PT.ParkTypeName from ParkingLocation PL left join  facility F on F.FacilityID = PL.FacilityID left join statusses SS on PL.Status = SS.StatusID left join ParkType PT on PT.ParkTypeID = PL.ParkTypeID Where 1 ";
			if (count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if ($value != '') {
						if ($key == 'ParkTypeName') {
							$query = $query . " AND PT.ParkTypeName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'ParkingLocationName') {
							$query = $query . " AND PL.ParkingLocationName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'Description') {
							$query = $query . " AND PL.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'FacilityName') {
							$query = $query . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
						if ($key == 'StatusName') {
							$query = $query . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
						}
					}
				}
			}

			if ($data['OrderBy'] != '') {
				if ($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if ($data['OrderBy'] == 'ParkTypeName') {
					$query = $query . " order by PT.ParkTypeName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'ParkingLocationName') {
					$query = $query . " order by PL.ParkingLocationName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'Description') {
					$query = $query . " order by PL.Description " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by F.FacilityName " . $order_by_type . " ";
				} else if ($data['OrderBy'] == 'StatusName') {
					$query = $query . " order by SS.StatusName " . $order_by_type . " ";
				}
			} else {
				$query = $query . " order by ParkingLocationName asc ";
			}

			$query = $query . " limit " . intval(mysqli_real_escape_string($this->connectionlink, $data['skip'])) . "," . intval(mysqli_real_escape_string($this->connectionlink, $data['limit']));

			$q = mysqli_query($this->connectionlink, $query);
			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if (mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while ($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Parking Location Available";
				return json_encode($json);
			}

			if ($data['skip'] == 0) {

				$query1 = "Select count(*) from ParkingLocation PL left join  facility F on F.FacilityID = PL.FacilityID left join statusses SS on PL.Status = SS.StatusID left join ParkType PT on PT.ParkTypeID = PL.ParkTypeID Where 1 ";
				if (count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if ($value != '') {

							if ($key == 'ParkTypeName') {
								$query1 = $query1 . " AND PT.ParkTypeName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Description') {
								$query1 = $query1 . " AND PL.ParkingLocationName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'Description') {
								$query1 = $query1 . " AND PL.Description like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'FacilityName') {
								$query1 = $query1 . " AND F.FacilityName like '%" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
							if ($key == 'StatusName') {
								$query1 = $query1 . " AND SS.StatusName like '" . mysqli_real_escape_string($this->connectionlink, $value) . "%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink, $query1);
				if (mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if (mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

	public function GetParkType($data)
	{
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data['ParkTypeID']
		);
		$query = "select * from ParkType where Status = '1' ORDER BY ParkTypeName";
		$q = mysqli_query($this->connectionlink, $query);
		if (mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
		}
		if (mysqli_affected_rows($this->connectionlink) > 0) {
			$i = 0;
			while ($row = mysqli_fetch_assoc($q)) {
				$result[$i] = $row;
				$i++;
			}
			$json['Success'] = true;
			$json['Result'] = $result;
		} else {
			$json['Success'] = false;
			$json['Result'] = "No Park Type Available";
		}
		return json_encode($json);
	}


	public function UploadParkingLocationFile($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'other' => $data['file'],
			'Result' => $data['file']['type']
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Parking Location')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Parking Location Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Parking Location')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Parking Location Page';
				return json_encode($json);
			}
			if($data['file']['type'] != 'application/vnd.ms-excel' && $data['file']['type'] != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
				$json['Success'] = false;
				$json['Result'] = 'Invalid File type';
				return json_encode($json);
			}
			$filename = time().$data['file']['name'];
			//$target_path = '../../uploads/'.$filename;

			$upload = $this->UploadToS3($filename,$data['file']['tmp_name']);
			if($upload) {
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Unable to upload file';
				$json['Upload'] = $upload;
				return json_encode($json);
			}

			//if(move_uploaded_file($data['file']['tmp_name'], $target_path)) {
			if(true) {

				$query = "insert into admin_file_uploads (FileName,DateCreated,CreatedBy,FileType) values ('".$filename."',NOW(), '".$_SESSION['user']['UserId']."','ParkingLocation')";
				$q = mysqli_query($this->connectionlink,$query);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$UploadID = mysqli_insert_id($this->connectionlink);

				$s3 = S3Client::factory(
					array(
						'credentials' => array(
							'key' => S3_key_eviridis,
							'secret' => S3_secret_eviridis
						),
						'version' => 'latest',
						'region'  => S3_region_eviridis
					)
				);
				$s3->registerStreamWrapper();

				//if($xlsx = SimpleXLSX::parse('../../uploads/'.$filename)) {
				if($xlsx = SimpleXLSX::parse('s3://'.S3_bucket_eviridis.'/'.$filename)) {
					$can_insert = false;
					$assets_count = 0;
					$i = 0;
					$a = 1;
					$error_message = '';
					$new_records = 0;
					$updated_records = 0;
					$final_output = array();
					$Header_output = ['Facility','ParkingLocationName','Description','ParkTypeName','Status','Result','Message'];
					foreach ($xlsx->rows() as $elt) {// Looking full excel for validation
						$current_row = $elt;
						if($a == 1) { // Executes only for first row
							if(trim($elt[0]) != 'Facility') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell A1 Should be 'Facility'";
								return json_encode($json);
							}

							if(trim($elt[1]) != 'ParkingLocationName') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell B1 Should be 'Parking Location Name'";
								return json_encode($json);
							}

							if(trim($elt[2]) != 'Description') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell C1 Should be 'Description'";
								return json_encode($json);
							}

							if(trim($elt[3]) != 'ParkTypeName') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell D1 Should be 'Park Type Name'";
								return json_encode($json);
							}

							if(trim($elt[4]) != 'Status') {
								$json['Success'] = false;
								$json['Result'] = "Invalid File Format Cell E1 Should be 'Status'";
								return json_encode($json);
							}							
						}

						if($a > 1) {//looping through data
							//if(trim($elt[1]) != '' && trim($elt[2]) != '' && trim($elt[3]) != '' && trim($elt[4]) != '') {
								$exception = false;
								$exception_message = '';
								$exception_field = '';

									
								if(trim($elt[0]) != '') { //SiteID
									//Start check If SiteID exists in our database
									$query101 = "select * from facility where FacilityName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[0]))."' and FacilityStatus = '1'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row101 = mysqli_fetch_assoc($q101);
										$FacilityID = $row101['FacilityID'];
									} else {
										$exception = true;
										$exception_message = 'Facility Name does not exist';
										$exception_field = 'FacilityID';
										$FacilityID = NULL;
										$FacilityID = '';
										$current_row[] = 'Failure';
										$current_row[] = $exception_message;
										$final_output[] = $current_row;
										$a++;
										continue;
									}
									//End check If SiteID exists in our database								
								} else {
									$exception = true;
									$exception_message = 'Missing Facility Name';
									$exception_field = 'FacilityID';
									$FacilityID = NULL;

									$current_row[] = 'Failure';
									$current_row[] = $exception_message;
									$final_output[] = $current_row;
									$a++;
									continue;
								}

								if(trim($elt[3]) != '') { //SiteID
									//Start check If SiteID exists in our database
									$query101 = "select * from ParkType where ParkTypeName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[3]))."' and Status = '1'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row101 = mysqli_fetch_assoc($q101);
										$ParkTypeID = $row101['ParkTypeID'];
									} else {
										$exception = true;
										$exception_message = 'ParkType Name does not exist';
										$exception_field = 'ParkTypeID';
										$ParkTypeID = NULL;
										$ParkTypeID = '';
										$current_row[] = 'Failure';
										$current_row[] = $exception_message;
										$final_output[] = $current_row;
										$a++;
										continue;
									}
									//End check If SiteID exists in our database								
								} else {
									$exception = true;
									$exception_message = 'Missing ParkType Name';
									$exception_field = 'ParkTypeID';
									$ParkTypeID = NULL;

									$current_row[] = 'Failure';
									$current_row[] = $exception_message;
									$final_output[] = $current_row;
									$a++;
									continue;
								}

								if(trim($elt[4]) != '') { //SiteID
									//Start check If SiteID exists in our database
									$query101 = "select * from statusses where StatusName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[4]))."'";
									$q101 = mysqli_query($this->connectionlink,$query101);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										while ($row101 = mysqli_fetch_assoc($q101)) {
											if ($row101['StatusID'] == 'Active') {
												$row101['StatusID'] = '1';
											} else if ($row101['StatusID'] == 'InActive') {
												$row101['StatusID'] = '2';
											}
											$StatusID = $row101['StatusID'];
										}
										$json['Success'] = true;
										//$json['Result'] = $StatusID;
									} else {
										$exception = true;
										$exception_message = 'Status Name does not exist';
										$exception_field = 'StatusID';
										$StatusID = NULL;

										$current_row[] = 'Failure';
										$current_row[] = $exception_message;
										$final_output[] = $current_row;
										$a++;
										continue;
									}
									//End check If SiteID exists in our database								
								} else {
									$exception = true;
									$exception_message = 'Missing Status Name';
									$exception_field = 'Status';
									$StatusID = NULL;

									$current_row[] = 'Failure';
									$current_row[] = $exception_message;
									$final_output[] = $current_row;
									$a++;
									continue;
								}

								if (!isset($elt[1]) || trim($elt[1]) === '') {
								    // Description missing, raise failure
								    $exception = true;
								    $exception_message = 'Parking Location Name is Missing';
								    $exception_field = 'ParkingLocationName';
								    $elt[1] = NULL;

								    $current_row[] = 'Failure';
								    $current_row[] = $exception_message;
								    $final_output[] = $current_row;
								    $a++;
								    continue;
								}

								if (!isset($elt[2]) || trim($elt[2]) === '') {
								    // Description missing, raise failure
								    $exception = true;
								    $exception_message = 'Description is Missing';
								    $exception_field = 'Description';
								    $elt[1] = NULL;

								    $current_row[] = 'Failure';
								    $current_row[] = $exception_message;
								    $final_output[] = $current_row;
								    $a++;
								    continue;
								}

								//Start Check If Rigname Exists
								$query2 = "select ParkingLocationID from ParkingLocation where ParkingLocationName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$FacilityID)."' ";
								$q2 = mysqli_query($this->connectionlink,$query2);
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$row2 = mysqli_fetch_assoc($q2);
									$ID = $row2['ParkingLocationID'];
								} else {//Create new Rig
									$ID = 0;
								}
								//End Chedk If Rigname Exists

								if($ID > 0) {								
									$query = "update ParkingLocation set ParkingLocationName = '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."',FacilityID = '".mysqli_real_escape_string($this->connectionlink,$FacilityID)."',Description = '".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."',ParkTypeID = '".mysqli_real_escape_string($this->connectionlink,trim($ParkTypeID))."',Status = '".mysqli_real_escape_string($this->connectionlink,trim($StatusID))."' where ParkingLocationID = '".$ID."'";
									
									$q = mysqli_query($this->connectionlink,$query);
									if(mysqli_error($this->connectionlink)) {
										$error_message = $error_message . 'Error at Row '.$a.'  '.mysqli_error($this->connectionlink).'';
									} else {
										$updated_records = $updated_records + 1;
									}


									$current_row[] = 'Success';
									$current_row[] = 'Record Updated';
									$final_output[] = $current_row;									
								} else {//Create new MPN
									//Start check duplicate
									$query11 = "select count(*) from ParkingLocation where ParkingLocationName =  '".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."' and FacilityID = '".mysqli_real_escape_string($this->connectionlink,$FacilityID)."' ";
									$q11 = mysqli_query($this->connectionlink,$query11);
									if(mysqli_error($this->connectionlink)) {
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row11 = mysqli_fetch_assoc($q11);
										if($row11['count(*)'] > 0) {
											$json['Success'] = false;
											$json['Result'] = 'Parking Location Name already Exists for the Facility';
											//return json_encode($json);


											$current_row[] = 'Failure';
											$current_row[] = 'Parking Location Name already exists for the Facility';
											$final_output[] = $current_row;
											$a++;
											continue;
										}
									} else {
										$json['Success'] = false;
										$json['Result'] = 'Invalid';
										return json_encode($json);
									}
									//End check duplicate
									$query1 = "insert into ParkingLocation (FacilityID,ParkingLocationName,Description,ParkTypeID,Status) values ('".mysqli_real_escape_string($this->connectionlink,$FacilityID)."','".mysqli_real_escape_string($this->connectionlink,trim($elt[1]))."','".mysqli_real_escape_string($this->connectionlink,trim($elt[2]))."','".mysqli_real_escape_string($this->connectionlink,$ParkTypeID)."','".mysqli_real_escape_string($this->connectionlink,$StatusID)."')";
									$q1 = mysqli_query($this->connectionlink,$query1);
									if(mysqli_error($this->connectionlink)) {
										$error_message = $error_message . 'Error at Row '.$a.'  '.mysqli_error($this->connectionlink).'';
									} else {
										$new_records = $new_records + 1;
									}

									$current_row[] = 'Success';
									$current_row[] = 'Record Created';
									$final_output[] = $current_row;
									
								}
							//}
						}
						$a++;
					}
				}
				else {
					$json['Success'] = false;
					$json['Result'] = SimpleXLSX::parseError();
					return json_encode($json);
				}

				//Start Update Upload record
				$message = $new_records.' New Parking Location Created, '.$updated_records.'  Part Type Updated. '.$error_message;
				$query4 = "update admin_file_uploads set Comments = '".mysqli_real_escape_string($this->connectionlink,$message)."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				//End Update Upload record

				$transaction = 'Administration ---> eViridis Administration ---> Parking Location';
				$description = 'Parking Location File Uploaded';
				$this->RecordUserTransaction($transaction,$description);

				$json['Success'] = true;
				$json['Result'] = $new_records.' New Parking Location Created, '.$updated_records.'  Parking Location Updated. '.$error_message;
				$json['FinalResult'] = $final_output;
				$_SESSION['FinalResult'] = $final_output;
				$_SESSION['HeaderResult'] = $Header_output;
				return json_encode($json);
			} else{
				$json['Success'] = false;
				$json['Result'] = 'Problem with File uploading';
			}
			return json_encode($json);
		}  catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GenerateParkTypeConfigurationxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['ParkTypeConfigurationxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function GenerateTruckTypeConfigurationxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['TruckTypeConfigurationxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function GenerateTDRSOPConfigurationxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['TDRSOPConfigurationxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

}

?>