<?php
include_once("../../config.php");
include_once("../../connection.php");
$today = date("mdY");
$month = date("m");
$day = date("d");
$year = date("Y");
$ProcessDatefrom = date('Y-m-d', strtotime(date("Y-m-d") . ' - 1 day'))." 00:00:00";
$ProcessDateto = date("Y-m-d")." 00:00:00";
//$ProcessDatefrom = '2025-04-28';
//$ProcessDateto = '2025-04-24';
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$filname = 'UnknownRecovery.'.$today.'.csv';
//$filname = 'UR.'.$today.'.csv';
$csv = "entity_id,source_id,source_id_scan_time,customer_id,material_type,material_type_scan_time,source_type,recovered_serial_id,serial_scan_time,mpn_id,mpn_scan_time,part_type,part_type_scan_time,manufacturer_id,coo_id,coo_scan_time,operator_login_id,recovery_location_id,recovery_result,result_scan_time,recovery_datetime,bin_id,bin_scan_time,container_id,container_scan_time,next_step_action,next_step_rule_id,classification_type,classification_code_id,workstation_id,workstation_scan_time,storage_location_group_id,exception_investigation_result,investigation_result_scan_time,event_id,event_s_duration_value\n";//Column headers
$sql = "select 'eV-Disposition-1' as entity_id,APC.CustomerName as source_id,A.part_type_scan_time as source_id_scan_time,AWSA.Customer as customer_id,
A.MaterialType as material_type,A.part_type_scan_time as material_type_scan_time,APCT.Cumstomertype as source_type,A.SerialNumber as recovered_serial_id,
A.serial_scan_time as serial_scan_time,A.UniversalModelNumber as mpn_id,A.mpn_scan_time as mpn_scan_time,A.part_type as part_type,A.part_type_scan_time as part_type_scan_time,
ACP.BinName as bin_id,AMF.ManufacturerName as manufacturer_id,ACO.COO as coo_id,A.coo_scan_time as coo_scan_time,AU.UserName as operator_login_id,
AF.FacilityName as recovery_location_id,ASW.input as recovery_result,A.result_scan_time as result_scan_time,A.DateCreated as recovery_datetime,
ACP.BinName as bin_id,A.bin_scan_time as bin_scan_time,'' as container_id,A.origin_container_id_scan_time as container_scan_time,
AD.disposition as next_step_action,ABR.rule_name as next_step_rule_id,AD.WasteClassificationType as classification_type,A.WasteCode as classification_code_id,
ASS.SiteName as workstation_id,A.workstation_scan_time as workstation_scan_time,ACPLG.GroupName as storage_location_group_id,'lost/found' as exception_investigation_result,
A.result_scan_time as investigation_result_scan_time,A.event_id
            from asset A
            LEFT JOIN pallets AP on AP.idPallet = A.idPallet
			LEFT JOIN customer APC on APC.CustomerID = A.CustomerID
			LEFT JOIN customertype APCT on APCT.idCustomertype = A.idCustomertype
            LEFT JOIN aws_customers AWSA on AWSA.AWSCustomerID = A.AWSCustomerID
            LEFT JOIN custompallet ACP on ACP.CustomPalletID = A.FirstReceivedCustomPalletID
            LEFT JOIN manufacturer AMF on AMF.idManufacturer = A.idManufacturer
            LEFT JOIN COO ACO on ACO.COOID = A.COOID
            LEFT JOIN users AU on AU.UserId = A.CreatedBy
            LEFT JOIN facility AF on AF.FacilityID = A.FacilityID
            LEFT JOIN workflow_input ASW ON ASW.input_id = A.input_id
            LEFT JOIN disposition AD ON AD.`disposition_id` = A.`disposition_id`
            LEFT JOIN business_rule ABR on ABR.rule_id = A.rule_id
            LEFT JOIN site ASS ON ASS.SiteID = A.SiteID
            LEFT JOIN location ACPL ON ACPL.LocationID = ACP.LocationID
            LEFT JOIN location_group ACPLG ON ACPLG.GroupID = ACPL.GroupID
            WHERE A.DateCreated Between '".$ProcessDatefrom."' and '".$ProcessDateto."' AND A.UnknownRecovery = 1
            ";
$query = mysqli_query($connectionlink1,$sql);
while($row = mysqli_fetch_assoc($query))
{
	/*if($row['FacilityName'] == 'CVG110')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 4 hour'));
	}
	else if($row['FacilityName'] == 'DUB210')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 9 hour'));
	}
	else if($row['FacilityName'] == 'SIN100')
	{
		$row['CreatedDate'] = date('Y-m-d H:i:s', strtotime($row['CreatedDate'] . ' + 16 hour'));
	}
    if($row['event_id'] != $eventid)
    {
        $i = 0;
    }
    if($i ==0)
    {
        $eventid = $row['event_id'];
        $timeFirst  = strtotime($row['recovery_type_scan_time']);
        $timeSecond = strtotime($row['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    else
    {
        $eventid = $row['event_id'];
        $timeFirst  = $timeSecond;
        $timeSecond = strtotime($row['serial_scan_time']);
        $differenceInSeconds = $timeSecond - $timeFirst;
    }
    $i = $i+1;*/
    $timeFirst  = strtotime($row['serial_scan_time']);
    $timeSecond = strtotime($row['recovery_datetime']);
    $differenceInSeconds = $timeSecond - $timeFirst;
    if($differenceInSeconds < 0)
    {
        $differenceInSeconds = '';
    }
    if($row['DateUpdated'] == '')
    {
        $row['DateUpdated'] = $row['DateCreated'];
    }
    if($row['tpvr_reason'] != '')
    {
        $row['controller_scan_time'] = '';
    }
    if($row['ShippingContainerID'] != '')
    {
        $row['bin_id'] = 'n/a';
        $row['storage_location_group_id'] = $row['GroupName'];
    }
    $start = strtotime($row['DateCreated']);
	$end = strtotime($row['DateUpdated']);
	$mins = ($end - $start) / 60;
	$hrs = $mins/60;
	$hrs1 = number_format($hrs,2);
    $date1 = explode(" ",$row['DateCreated']);
    $date2 = explode("-",$date1[0]);
    $date = $date2[1]."/".$date2[2]."/".$date2[0];
    $time = date("H:i:s", strtotime($row['DateCreated']));
    if($row['DateCreated'] != '')
    {
        $row['DateCreated'] = date("Y-m-d H:i:s", strtotime($row['DateCreated']));
    }
    else
    {
        $row['DateCreated'] = '';
    }
    $row['operator_login_id'] = strtolower($row['operator_login_id']);
    $row['entity_id'] = str_replace(","," ",$row['entity_id']);
	$row['recovered_serial_id'] = str_replace(","," ",$row['recovered_serial_id']);
	$row['mpn_id'] = str_replace(","," ",$row['mpn_id']);
	$row['part_type'] = str_replace(","," ",$row['part_type']);
    $row['manufacturer_id'] = str_replace(","," ",$row['manufacturer_id']);
    $row['source_type'] = str_replace(","," ",$row['source_type']);
    $row['operator_login_id'] = str_replace(","," ",$row['operator_login_id']);
    $row['recovery_location_id'] = str_replace(","," ",$row['recovery_location_id']);
    $row['recovery_result'] = str_replace(","," ",$row['recovery_result']);
    $row['bin_id'] = str_replace(","," ",$row['bin_id']);
    $row['container_id'] = str_replace(","," ",$row['container_id']);
    $row['storage_location_group_id'] = str_replace(","," ",$row['storage_location_group_id']);
    $row['coo_id'] = str_replace(","," ",$row['coo_id']);
    $row['classification_type'] = str_replace(","," ",$row['classification_type']);
    $row['classification_code_id'] = str_replace(","," ",$row['classification_code_id']);
    $row['controller_login_id'] = str_replace(","," ",$row['controller_login_id']);
    $row['event_id'] = str_replace(","," ",$row['event_id']);
    $row['customer_id'] = str_replace(","," ",$row['customer_id']);
    
    if($row['entity_id'] == '')
    {
        $row['entity_id'] = 'n/a';
    }
    if($row['source_id'] == '')
    {
        $row['source_id'] = '';
    }
    if($row['source_type'] == '')
    {
        $row['source_type'] = 'n/a';
    }
    if($row['customer id'] == '')
    {
        $row['customer id'] = '';
    }
    if($row['material_type'] == '')
    {
        $row['material_type'] = 'n/a';
    }
    if($row['recovered_serial_id'] == '')
    {
        $row['recovered_serial_id'] = '';
    }
    if($row['mpn_id'] == '')
    {
        $row['mpn_id'] = 'n/a';
    }
    if($row['part_type'] == '')
    {
        $row['part_type'] = '';
    }
    if($row['manufacturer_id'] == '')
    {
        $row['manufacturer_id'] = 'n/a';
    }
    if($row['coo_id'] == '')
    {
        $row['coo_id'] = 'n/a';
    }
    if($row['operator_login_id'] == '')
    {
        $row['operator_login_id'] = 'n/a';
    }
    if($row['recovery_location_id'] == '')
    {
        $row['recovery_location_id'] = '';
    }
    if($row['recovery_result'] == '')
    {
        $row['recovery_result'] = 'n/a';
    }
    if($row['bin_id'] == '')
    {
        $row['bin_id'] = 'n/a';
    }
    if($row['container_id'] == '')
    {
        $row['container_id'] = 'n/a';
    }
    if($row['next_step_action'] == '')
    {
        $row['next_step_action'] = 'n/a';
    }
    if($row['next_step_rule_id'] == '')
    {
        $row['next_step_rule_id'] = 'n/a';
    }
    if($row['classification_type'] == '')
    {
        $row['classification_type'] = 'n/a';
    }
    if($row['classification_code_id'] == '')
    {
        $row['classification_code_id'] = 'n/a';
    }
    if($row['workstation_id'] == '')
    {
        $row['workstation_id'] = 'n/a';
    }
    if($row['storage_location_group_id'] == '')
    {
        $row['storage_location_group_id'] = '';
    }
    if($row['exception_investigation_result'] == '')
    {
        $row['exception_investigation_result'] = 'n/a';
    }
    if($row['event_id'] == '')
    {
        $row['event_id'] = '';
    }
    if($row['event_s_duration_value'] == '')
    {
        $row['event_s_duration_value'] = 'n/a';
    }
    if($row['source_id_scan_time'] != '')
    {
        if($row['source_id_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['source_id_scan_time'] = date("Y-m-d H:i:s", strtotime($row['source_id_scan_time']));
        }
        else
        {
            $row['source_id_scan_time'] = '';
        }
    }
    else
    {
        $row['source_id_scan_time'] = '';
    }
    if($row['material_type_scan_time'] != '')
    {
        if($row['material_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['material_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row['material_type_scan_time']));
        }
        else
        {
            $row['material_type_scan_time'] = '';
        }
    }
    else
    {
        $row['material_type_scan_time'] = '';
    }
    if($row['serial_scan_time'] != '')
    {
        if($row['serial_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['serial_scan_time'] = date("Y-m-d H:i:s", strtotime($row['serial_scan_time']));
        }
        else
        {
            $row['serial_scan_time'] = '';
        }
    }
    else
    {
        $row['serial_scan_time'] = '';
    }
    if($row['mpn_scan_time'] != '')
    {
        if($row['mpn_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['mpn_scan_time'] = date("Y-m-d H:i:s", strtotime($row['mpn_scan_time']));
        }
        else
        {
            $row['mpn_scan_time'] = '';
        }
    }
    else
    {
        $row['mpn_scan_time'] = '';
    }
    if($row['part_type_scan_time'] != '')
    {
        if($row['part_type_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['part_type_scan_time'] = date("Y-m-d H:i:s", strtotime($row['part_type_scan_time']));
        }
        else
        {
            $row['part_type_scan_time'] = '';
        }
    }
    else
    {
        $row['part_type_scan_time'] = '';
    }
    if($row['coo_scan_time'] != '')
    {
        if($row['coo_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['coo_scan_time'] = date("Y-m-d H:i:s", strtotime($row['coo_scan_time']));
        }
        else
        {
            $row['coo_scan_time'] = '';
        }
    }
    else
    {
        $row['coo_scan_time'] = '';
    }
    if($row['result_scan_time'] != '')
    {
        if($row['result_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['result_scan_time'] = date("Y-m-d H:i:s", strtotime($row['result_scan_time']));
        }
        else
        {
            $row['result_scan_time'] = '';
        }
    }
    else
    {
        $row['result_scan_time'] = '';
    }
    if($row['recovery_datetime'] != '')
    {
        if($row['recovery_datetime'] != '0000-00-00 00:00:00')
        {
            $row['recovery_datetime'] = date("Y-m-d H:i:s", strtotime($row['recovery_datetime']));
        }
        else
        {
            $row['recovery_datetime'] = '';
        }
    }
    else
    {
        $row['recovery_datetime'] = '';
    }
    if($row['bin_scan_time'] != '')
    {
        if($row['bin_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['bin_scan_time'] = date("Y-m-d H:i:s", strtotime($row['bin_scan_time']));
        }
        else
        {
            $row['bin_scan_time'] = '';
        }
    }
    else
    {
        $row['bin_scan_time'] = '';
    }
    if($row['container_scan_time'] != '')
    {
        if($row['container_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['container_scan_time'] = date("Y-m-d H:i:s", strtotime($row['container_scan_time']));
        }
        else
        {
            $row['container_scan_time'] = '';
        }
    }
    else
    {
        $row['container_scan_time'] = '';
    }
    if($row['workstation_scan_time'] != '')
    {
        if($row['workstation_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['workstation_scan_time'] = date("Y-m-d H:i:s", strtotime($row['workstation_scan_time']));
        }
        else
        {
            $row['workstation_scan_time'] = '';
        }
    }
    else
    {
        $row['workstation_scan_time'] = '';
    }
    if($row['investigation_result_scan_time'] != '')
    {
        if($row['investigation_result_scan_time'] != '0000-00-00 00:00:00')
        {
            $row['investigation_result_scan_time'] = date("Y-m-d H:i:s", strtotime($row['investigation_result_scan_time']));
        }
        else
        {
            $row['investigation_result_scan_time'] = '';
        }
    }
    else
    {
        $row['investigation_result_scan_time'] = '';
    }
    //$row['event_id'] = '';
    $row2  = array($row['entity_id'],$row['source_id'],$row['source_id_scan_time'],$row['customer id'],$row['material_type'],$row['material_type_scan_time'],$row['source_type'],$row['recovered_serial_id'],$row['serial_scan_time'],$row['mpn_id'],$row['mpn_scan_time'],$row['part_type'],$row['part_type_scan_time'],$row['manufacturer_id'],$row['coo_id'],$row['coo_scan_time'],$row['operator_login_id'],$row['recovery_location_id'],$row['recovery_result'],$row['result_scan_time'],$row['recovery_datetime'],$row['bin_id'],$row['bin_scan_time'],$row['container_id'],$row['container_scan_time'],$row['next_step_action'],$row['next_step_rule_id'],$row['classification_type'],$row['classification_code_id'],$row['workstation_id'],$row['workstation_scan_time'],$row['storage_location_group_id'],$row['exception_investigation_result'],$row['investigation_result_scan_time'],$row['event_id'],$differenceInSeconds);
    $rows[] = $row2;
}
foreach ($rows as $record){
    $csv.= $record[0].','.$record[1].','.$record[2].','.$record[3].','.$record[4].','.$record[5].','.$record[6].','.$record[7].','.$record[8].','.$record[9].','.$record[10].','.$record[11].','.$record[12].','.$record[13].','.$record[14].','.$record[15].','.$record[16].','.$record[17].','.$record[18].','.$record[19].','.$record[20].','.$record[21].','.$record[22].','.$record[23].','.$record[24].','.$record[25].','.$record[26].','.$record[27].','.$record[28].','.$record[29].','.$record[30].','.$record[31].','.$record[32].','.$record[33].','.$record[34].','.$record[35]."\n"; //Append data to csv
}

$csv_handler = fopen('/var/www/html/aws/daily_report/'.$filname,'w');
fwrite ($csv_handler,$csv);
fclose ($csv_handler);
$just_filename = '/var/www/html/aws/daily_report/'.$filname;
$sqlselect = "Select count(*) as assetcount,cronid from s3cron where filename LIKE '".$filname."'";
$queryselect = mysqli_query($connectionlink1,$sqlselect);
$rowselect = mysqli_fetch_assoc($queryselect);
if($rowselect['assetcount'] == 0)
{
	$sqlinsert = "INSERT INTO `s3cron` (`cronid`, `filename`, `crontime`, `s3move`,`reportname`) VALUES (NULL, '".$filname."', '".date("Y-m-d H:i:s")."', 'No','Daily Receipt Report');";
	$queryinsert = mysqli_query($connectionlink,$sqlinsert);
	if(mysqli_error($connectionlink)) {
		echo mysqli_error($connectionlink).$sqlinsert;
	}
}
$sqls = "select * from s3cron where cronid ='".$rowselect['cronid']."'";
$querys = mysqli_query($connectionlink,$sqls);
while($rows = mysqli_fetch_assoc($querys))
{
	$awsfilepath = $year."/".$month."/".$day."/".$rows['filename'];
	$ch = curl_init();
	//$url = "http://*************/eviridis/uploadawss3.php?path=daily_report/".$rows['filename']."&filename=".$rows['filename']."";
	$url = HOST."uploadstg.php?path=/var/www/html/aws/daily_report/".$rows['filename']."&filename=".$awsfilepath."&foldername=eViridis_Outputs";
	echo $url;
	// set URL and other appropriate options
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, 0);
	
	// grab URL and pass it to the browser
	curl_exec($ch);
	
	// close cURL resource, and free up system resources
	curl_close($ch);
	//
	$sqluplad = "UPDATE s3cron SET `s3move` = 'Yes' WHERE `cronid` = '".$rows['cronid']."'";
	$queryupdate = mysqli_query($connectionlink,$sqluplad);
	//unlink("../../daily_report/".$rows['filename']);
}
?>